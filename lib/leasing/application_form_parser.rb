class Leasing::ApplicationFormParser
  include PdfFilling

  def initialize(path)
    @path = path
  end

  def parse
    fields = pdftk.get_fields(path)
    form_values(fields)
  end

  private

  attr_reader :path

  def field_map
    @map || JSON.parse(
      File.read('config/brown_application_field_map.json')
    )
  end

  def form_values(fields)
    field_map.filter_map do |entry|
      form_key = entry['form_key']
      pdf_key = entry['pdf_key']

      field = fields.find { |f| f.name == pdf_key }

      value = if entry['type'] == 'radio'
                radio_value(entry, field)
              else
                field.value
              end

      next unless value

      [form_key, value]
    end.to_h
  end

  def radio_value(entry, field)
    radio = entry['radios'].find { |r| r['pdf_key'] == field.value }
    radio && radio['form_key']
  end
end
