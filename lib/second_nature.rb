module SecondNature
  def self.benefit_package_accounts
    Plutus::AccountsQuery.new.search.resident_benefit_package_accounts
  end

  def self.active_lease_memberships
    entries = ChargeSchedule::Entry.where(
      account: benefit_package_accounts,
      recurring: true
    )

    schedules = ChargeSchedule.joins(:entries).merge(entries)

    LeaseMembership
      .where.not(role: %i[guarantor occupant minor])
      .includes(
        :tenant,
        lease: [
          chain_membership: :chain,
          chain: :aging_delinquency,
          unit: [
            :address, [property: :address]
          ]
        ]
      )
      .references(:lease)
      .merge(Lease.active)
      .merge(Lease.where(charge_schedule: schedules))
  end
end
