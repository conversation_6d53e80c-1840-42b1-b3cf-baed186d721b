class Accounting::Journal::HealthWorkbook
  extend Service

  attr_reader :journals, :days

  def initialize(journals:, days: 120)
    @journals = journals
    @days = days
  end

  def call
    workbook = RubyXL::Workbook.new

    workbook[0].add_cell(0, 0, 'Journal')
    workbook[0].add_cell(0, 1, 'AP Diff')
    workbook[0].add_cell(0, 2, 'AR Diff')
    workbook[0].add_cell(0, 3, 'Good %')
    workbook[0].add_cell(0, 4, 'Streak')
    workbook[0].add_cell(0, 5, 'Locked')

    journals.each.with_index do |journal, j|
      worksheet = workbook.add_worksheet(journal.name)

      worksheet.add_cell(0, 0, 'Date')
      worksheet.add_cell(0, 1, 'BS AP')
      worksheet.add_cell(0, 2, 'Aging AP')
      worksheet.add_cell(0, 3, 'AP Diff')
      worksheet.add_cell(0, 4, 'BS AR')
      worksheet.add_cell(0, 5, 'Aging AR')
      worksheet.add_cell(0, 6, 'AR Diff')

      good_days = 0
      ap_diff = 0
      ar_diff = 0
      streak = 0

      (start_date..end_date).each.with_index do |date, y|
        Rails.logger.debug { "#{journal.name} / #{date}" }

        health = Accounting::Journal::HealthSnapshot.new(journal, date: date)

        bs_ap = health.balance_sheet_accounts_payable
        bs_ar = health.balance_sheet_accounts_receivable

        aging_ap = health.aging_payables_balance
        aging_ar = health.aging_receivables_balance

        ap_diff = bs_ap - aging_ap
        ar_diff = bs_ar - aging_ar

        worksheet.add_cell(y + 1, 0,  date.to_fs(:short_date))
        worksheet.add_cell(y + 1, 1,  bs_ap.to_f)
        worksheet.add_cell(y + 1, 2,  aging_ap.to_f)
        worksheet.add_cell(y + 1, 3,  ap_diff.to_f)
        worksheet.add_cell(y + 1, 4,  bs_ar.to_f)
        worksheet.add_cell(y + 1, 5,  aging_ar.to_f)
        worksheet.add_cell(y + 1, 6,  ar_diff.to_f)

        if [ap_diff, ar_diff].all?(&:zero?)
          good_days += 1
          streak += 1
        else
          streak = 0
        end
      end

      good_pct = ((good_days / days.to_f) * 100).to_i

      workbook[0].add_cell(j + 1, 0, journal.name)
      workbook[0].add_cell(j + 1, 1, ap_diff.format)
      workbook[0].add_cell(j + 1, 2, ar_diff.format)
      workbook[0].add_cell(j + 1, 3, good_pct)
      workbook[0].add_cell(j + 1, 4, streak)
      workbook[0].add_cell(j + 1, 5, journal.locked_at&.to_fs(:short_date))
    end

    workbook
  end

  private

  def start_date
    end_date - (days - 1).days
  end

  def end_date
    Time.zone.today
  end
end
