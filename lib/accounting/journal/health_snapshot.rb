class Accounting::Journal::HealthSnapshot
  attr_reader :journal, :user, :filters, :date

  def initialize(journal,
                 user: revela_support,
                 filters: {},
                 date: Time.zone.today)
    @user = user
    @journal = journal
    @filters = filters
    @date = date
  end

  def balance_sheet_accounts_payable
    @balance_sheet_accounts_payable ||=
      Money.new(accounts_payable.report_balance(balance_filters))
  end

  def balance_sheet_accounts_receivable
    @balance_sheet_accounts_receivable ||=
      Money.new(accounts_receivable.report_balance(balance_filters))
  end

  def aging_payables_balance
    @aging_payables_balance ||=
      report_total(Reports::V3::AgingPayables)
  end

  def aging_receivables_balance
    @aging_receivables_balance ||=
      report_total(Reports::V3::AgingReceivables)
  end

  def payables_difference
    balance_sheet_accounts_payable - aging_payables_balance
  end

  def receivables_difference
    balance_sheet_accounts_receivable - aging_receivables_balance
  end

  def trial_balance_debits
    @trial_balance_debits ||= begin
      debits = Plutus::DebitAmount.joins(:entry)
                                  .merge(trial_balance_entries)
                                  .sum(:amount)
      Money.new(debits)
    end
  end

  def trial_balance_credits
    @trial_balance_credits ||= begin
      credits = Plutus::CreditAmount.joins(:entry)
                                    .merge(trial_balance_entries)
                                    .sum(:amount)
      Money.new(credits)
    end
  end

  def trial_balance
    @trial_balance ||= trial_balance_debits - trial_balance_credits
  end

  private

  def revela_support
    PropertyManager.find_by!(email: '<EMAIL>')
  end

  def balance_filters
    { **filters, journal_id: journal.id, to_date: date }
  end

  def report_filters
    OpenStruct.new(**filters, date: date, context_id: journal.to_sgid.to_s)
  end

  def report_total(report)
    instance = report.new(filters: report_filters, user: user)

    index = -1

    # TODO: Remove
    if report == Reports::V3::AgingReceivables &&
       Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox])
      index -= 2
    end

    instance.as_json[:footer][:cells][index][:value]
  end

  def trial_balance_entries
    Plutus::Entry.where(journal_id: journal.id)
                 .where('plutus_entries.date <= ?', date)
  end

  delegate :accounts_payable, :accounts_receivable, to: :journal
end
