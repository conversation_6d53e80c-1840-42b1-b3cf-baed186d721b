class Accounting::Context::AccountingRegion < Accounting::Context::Base
  attr_reader :region, :chart_of_accounts

  def initialize(region)
    super()
    @region = region
    assign_single_chart_of_accounts_or_fail!
  end

  delegate :accounts, to: :chart_of_accounts

  private

  def entry_params
    { journal_id: region.companies.pluck(:id) }
  end

  def assign_single_chart_of_accounts_or_fail!
    charts = ChartsOfAccountsQuery.new.search.by_companies(region.companies)

    fail "Found #{charts.size} chart(s) of accounts" unless charts.size == 1

    @chart_of_accounts = charts.first
  end
end
