module Accounting::PassthroughAccount::Mapping
  class << self
    def mapped_passthrough_account(local_account:, local_chart:, passthrough_chart:)
      return unless passthrough_account_mapping_enabled?

      mapping = passthrough_account_mapping

      return if mapping.nil?

      passthrough_account_id = mapping.dig(local_chart.id, passthrough_chart.id, local_account.id)

      passthrough_chart.accounts.find_by(id: passthrough_account_id)
    end

    def passthrough_account_mapping_enabled?
      Feature.enabled?(:passthrough_account_mapping, Customer.current)
    end

    def passthrough_account_mapping
      case Customer.current_subdomain
      when 'marketplacehomes'
        marketplacehomes_passthrough_account_mapping
      end
    end

    # local account => passthrough account
    def marketplacehomes_passthrough_account_mapping
      {
        17 => {
          13 => {
            3404 => 4182
          }
        },
        5 => {
          13 => {
            3408 => 4182
          }
        },
        20 => {
          13 => {
            3403 => 4182
          }
        },
        25 => {
          13 => {
            4927 => 4182
          }
        }
      }
    end
  end
end
