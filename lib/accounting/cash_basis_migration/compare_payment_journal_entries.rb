class Accounting::CashBasisMigration::ComparePaymentJournalEntries
  extend Service

  attr_reader :payment

  def initialize(payment:)
    @payment = payment
  end

  def call
    persisted_description = description(persisted_journal_entries)
    virtual_description = description(virtual_journal_entries)

    if persisted_description == virtual_description
      OpenStruct.new(successful?: true)
    else
      OpenStruct.new(
        successful?: false,
        persisted_description: persisted_description,
        virtual_description: virtual_description
      )
    end
  end

  def persisted_journal_entries
    payment.journal_entries.persisted_cash_basis_only
  end

  def virtual_journal_entries
    Plutus::Cash::Entry.where(commercial_document: payment).uniq
  end

  def attributes(entry)
    entry.amounts.uniq.map do |amount|
      {
        date: entry.date,
        description: entry.description,
        kind: entry.kind,
        journal_id: entry.journal_id,
        property_id: entry.property_id,
        unit_id: entry.unit_id,
        lease_membership_id: entry.lease_membership_id,
        account: amount.account.display_name,
        amount: Money.new(amount.amount).to_f.floor, # TODO: slicing off cents here because rounding is different in PG
        type: amount.debit? ? 'debit' : 'credit'
      }
    end
  end

  def strings(entry)
    attributes(entry).map do |amount_hash|
      amount_hash.map do |key, value|
        "#{key}: #{value}"
      end.join(' ')
    end
  end

  def description(entries)
    entries.flat_map do |entry|
      strings(entry)
    end.sort.join("\n")
  end
end
