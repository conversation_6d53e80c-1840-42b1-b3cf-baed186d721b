module Accounting::Ledger::PaymentStatistics
  def timely_count
    paid_invoices.reject(&:late?).count
  end

  def late_count
    paid_invoices.count(&:late?)
  end

  def unpaid_count
    unpaid_invoices.count
  end

  def timely_percentage
    return 1.0 if paid_invoices.none?

    timely_count.to_f / paid_invoices.count
  end

  private

  def paid_invoices
    invoices.select(&:paid?)
  end

  def unpaid_invoices
    invoices.reject(&:paid?)
  end
end
