namespace :brakeman do
  desc 'Run Brakeman'
  task :run, :output_files do |_, args|
    require 'brakeman'

    files = args[:output_files].split if args[:output_files]
    Brakeman.run app_path: '.', output_files: files, print_report: true
  end

  desc 'Check your code with <PERSON><PERSON><PERSON>'
  task :check do
    require 'brakeman'

    result = <PERSON><PERSON><PERSON>.run app_path: '.', print_report: true

    unless result.filtered_warnings.empty?
      exit Brakeman::Warnings_Found_Exit_Code
    end
  end
end
