class Reports::Packet::Renderer::PdfConcatenated < Reports::Packet::Renderer
  def filename
    "#{base_filename}.pdf"
  end

  def stream
    files = reports.map do |report|
      pdf = Pdf.from_report(report).force_encoding('UTF-8')
      file = Tempfile.new(['report', '.pdf'])
      file.write(pdf)
      file
    end

    paths = files.map(&:path).join(' ')
    file = Tempfile.new(['report_packet', '.pdf'])
    file.open
    command = "pdftk #{paths} cat output #{file.path}"
    system(command)
    files.each { |f| File.delete(f) }
    file
  end
end
