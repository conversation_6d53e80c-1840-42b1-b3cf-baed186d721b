class Reports::Packet::<PERSON><PERSON><PERSON>
  def initialize(packet:)
    @packet = packet
  end

  attr_reader :packet

  def stream
    fail NotImplementedError
  end

  private

  delegate :reports, :template, :entities, to: :packet

  def base_filename
    [
      template.name,
      scope_name,
      Time.zone.now.to_fs(:export)
    ].compact.map(&:parameterize).map(&:downcase).join('-')
  end

  def scope_name
    entities.first.name if entities.one?
  end
end
