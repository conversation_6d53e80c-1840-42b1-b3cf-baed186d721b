class Reports::CashFlowStatement
  include Reports::CashFlowStatement::Exportable

  def initialize(params:, user:)
    @params = params

    @target = GlobalID::Locator.locate_signed(params[:target_id])
    @target ||= Accounting::Context.top_level(user).first

    @filters = {
      from_date: period_beginning,
      to_date: period_ending
    }.merge(query_params)

    @income_statement = Accounting::IncomeStatement.new(
      accounts, @filters.merge(skip_equity_transfers: true)
    )
  end

  def as_json(options = {})
    {
      version: 1,
      name: title,
      subheader: subheader,
      sections: sections
    }
  end

  def title
    'Cash Flow Statement'
  end

  private

  attr_reader :filters, :income_statement, :params, :target

  def subheader
    entity = accounting_context.name
    "#{entity}, From #{period_beginning.strftime('%B %d, %Y')} " \
      "to #{period_ending.strftime('%B %d, %Y')}"
  end

  def period_beginning
    (params[:begin_date] && Date.parse(params[:begin_date])) || default_period_beginning
  end

  def default_period_beginning
    if accounting_context.respond_to?(:journal)
      accounting_context.journal.fiscal_year.start_date
    else
      Time.zone.today.beginning_of_month
    end
  end

  def period_ending
    (params[:end_date] && Date.parse(params[:end_date])) || Time.zone.today
  end

  def account_line(account, cents)
    {
      link: account.url,
      name: account.display_name,
      balance_cents: cents.to_i
    }
  end

  def net_income_line
    {
      link: '/reports/profit-and-loss',
      name: 'Net Income',
      balance_cents: net_income.cents.to_i
    }
  end

  def operating_activities_accounts
    selected_accounts = accounts
                        .where(category: ['Current Asset', 'Current Liability'])
                        .where.not(name: ['Cash', 'Undeposited Funds'])

    if Customer.current_subdomain == 'marketplacehomes'
      selected_accounts = selected_accounts.or(
        accounts.where(id: [3117, 3118, 3134])
      ).where.not(
        id: [3120]
      )
    end

    operating = selected_accounts
                .order(gl_code: :asc, id: :asc)
                .map do |account|
                  cents = account.balance(filters)
                  cents = account.is_a?(Plutus::Asset) ? -cents : cents
                  account_line(account, cents)
                end

    [net_income_line] + operating
  end

  def investing_activities_accounts
    selected_accounts = accounts.where(category: ['Fixed Asset'])

    if Customer.current_subdomain == 'marketplacehomes'
      selected_accounts = selected_accounts.where.not(
        id: [3117, 3118]
      )
    end

    selected_accounts.order(gl_code: :asc, id: :asc).map do |account|
      cents = -account.balance(filters)
      account_line(account, cents)
    end
  end

  def financing_activities_accounts
    selected_accounts = accounts.where(category: ['Non-current Liability'])

    if Customer.current_subdomain == 'marketplacehomes'
      selected_accounts = selected_accounts.or(
        accounts.where(id: [3141, 3142, 3143, 3146, 3120])
      ).where.not(
        id: [3134]
      )
    end

    selected_accounts.order(gl_code: :asc, id: :asc).map do |account|
      cents = account.report_balance(filters)
      account_line(account, cents)
    end
  end

  def sections
    [
      { name: 'Operating Activities', accounts: operating_activities_accounts },
      { name: 'Investing Activities', accounts: investing_activities_accounts },
      { name: 'Financing Activities', accounts: financing_activities_accounts }
    ]
  end

  def accounts
    if hide_inactivity?
      Plutus::AccountsQuery
        .new(accounting_context.accounts).search
        .with_activity_in \
          start_date: period_beginning,
          end_date: period_ending,
          params: query_params
    else
      accounting_context.accounts
    end
  end

  def hide_inactivity?
    Feature.enabled?(:hide_inactive_accounts, Customer.current)
  end

  delegate :net_income, to: :income_statement
  delegate :accounting_context, to: :target
  delegate :query_params, to: :accounting_context
end
