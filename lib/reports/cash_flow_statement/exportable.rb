module Reports::CashFlowStatement::Exportable
  include ::Exportable::XLSX

  def as_xlsx
    {
      version: 2,
      title: 'Cash Flow Statement',
      subheader: subheader,
      header: { cells: [{ value: '' }, { value: '' }] },
      rows: parse
    }
  end

  private

  def parse
    net_cash_flow = 0
    out = []

    sections.each do |activity|
      out << nil

      out << start_row(0, 'h5', activity[:name])
      total = 0
      activity[:accounts].each do |account|
        out << start_row(1, 'p', account[:name])
        out[-1][:cells] << { value: Money.new(account[:balance_cents]) }
        total += account[:balance_cents]
        net_cash_flow += account[:balance_cents]
      end
      out << start_row(1, 'b', "Net Cash Flows from #{activity[:name]}")
      out[-1][:cells] << { value: Money.new(total) }
    end

    out << nil

    change = 'Increase'
    change = 'Decrease' if net_cash_flow.negative?
    out << start_row(0, 'h5', "Net #{change} in Cash")
    out[-1][:cells] << { value: Money.new(net_cash_flow) }

    out
  end

  def start_row(depth, type, value)
    value = indent(depth, value) if type != '$'
    { depth: depth, cells: [{ type: type, value: value }] }
  end

  def indent(i, string)
    return string if i.zero?

    indent(i - 1, "  #{string}")
  end
end
