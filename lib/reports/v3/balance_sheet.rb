class Reports::V3::BalanceSheet < Reports::V3::Report
  def self.available_filters
    [
      if Feature.enabled?(:reports_scope_portfolios, Customer.current)
        :portfolio_accounting_context
      else
        :accounting_context
      end,
      (:owner if CustomerSpecific::Behavior.reports_add_owner_filters?),
      :date,
      :basis
    ].compact
  end

  def self.default_filter_values
    { date: Time.zone.today }
  end

  def as_json(options = {})
    in_basis do
      legacy_report.as_json(options).merge(
        title: title,
        version: 2,
        custom_partial: 'reports/v3/custom/balance_sheet'
      )
    end
  end

  def as_xlsx(options = {})
    in_basis { legacy_report.as_xlsx }
  end

  delegate :scope, to: :legacy_report

  private

  def in_basis(&)
    if filters.basis == 'cash'
      Plutus.in_cash_basis(journal_cache_hint: journal_cache_hint, &)
    else
      yield
    end
  end

  def legacy_report
    params = ActionController::Parameters.new(
      date: filters.date&.to_s,
      basis: filters.basis,
      target_id: filters.context_id
    )

    @legacy_report ||= Reports::BalanceSheet.new(
      params: params, # TODO: Remove
      filters: filters,
      user: user
    )
  end

  delegate :journal_cache_hint, to: :legacy_report
end
