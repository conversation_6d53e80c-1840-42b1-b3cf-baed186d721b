class Reports::V3::OwnerPortalTenantLedger < Reports::V3::Ledger
  def name
    title
  end

  def title
    'Tenant Ledger'
  end

  def self.available_filters
    %i[lease]
  end

  def lease_values
    leases.map(&:chain).map do |chain|
      [
        chain.to_s,
        chain.id
      ]
    end.sort_by(&:first)
  end

  def entity
    @entity ||= if filters.lease_id
                  leases.find(filters.lease_id)
                else
                  leases.first
                end
  end

  private

  def leases
    LeasesQuery.new.search.by_properties(user.properties)
  end
end
