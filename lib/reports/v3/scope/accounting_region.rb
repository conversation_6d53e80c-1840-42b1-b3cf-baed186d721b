class Reports::V3::Scope::AccountingRegion
  attr_reader :region_name, :user

  def initialize(region_name, user:)
    @region_name = region_name
    @user = user
  end

  def region
    @region_name
  end

  def name
    @region_name
  end

  def companies
    Company.where(id: companies_in_region)
  end

  def properties
    @user.properties.where(company: companies)
  end

  def units
    @user.units.where(property: properties)
  end

  def accounting_context
    Accounting::Context::AccountingRegion.new(self)
  end

  private

  def companies_in_region
    @user.companies.includes(:address).select do |company|
      company.address&.region_name == @region_name
    end
  end
end
