class Reports::V3::UnitStatus::RentedOnNotice
  include Reports::V3::UnitStatus::Section

  private

  def subtitle
    "#{count} #{'unit'.pluralize(count)} expiring with an upcoming lease."
  end

  def header
    {
      cells: [
        { value: 'Unit', justify: :center },
        { value: 'Move Out', justify: :center },
        { value: 'Resident', justify: :center },
        { value: 'Rent', justify: :center },
        { value: 'Move In', justify: :center },
        { value: 'Applicant', justify: :center },
        { value: 'Rent', justify: :center },
        { value: 'Market Rate', justify: :center },
        { value: 'Square Feet', justify: :center },
        { value: 'Bedrooms', justify: :center }
      ]
    }
  end

  def rows
    units.map do |unit|
      active_lease = Lease.find(unit.active_lease_id)
      upcoming_lease = Lease.find(unit.upcoming_lease_id)

      {
        cells: [
          { value: unit.qualified_name, link: unit.url },
          { value: active_lease.move_out_date },
          { value: active_lease.primary_tenant.name, link: active_lease.primary_tenant.url },
          { value: active_lease.amount, link: active_lease.url },
          { value: upcoming_lease.move_in_date },
          { value: upcoming_lease.primary_tenant.name, link: upcoming_lease.primary_tenant.url },
          { value: upcoming_lease.amount, lilnk: upcoming_lease.url },
          { value: unit.price },
          { value: unit.square_feet },
          { value: unit.bedrooms }
        ]
      }
    end
  end

  def units
    @units ||= UnitStatusQuery.new(scoped_units, date: filters.date)
                              .search
                              .status_rented(filters.date)
                              .status_on_notice(filters.date)
                              .includes(:floorplan)
                              .with_lease_ids
                              .uniq
  end
end
