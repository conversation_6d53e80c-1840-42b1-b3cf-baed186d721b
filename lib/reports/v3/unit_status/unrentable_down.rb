class Reports::V3::UnitStatus::UnrentableDown
  include Reports::V3::UnitStatus::Section

  private

  def subtitle
    "#{count} #{'unit'.pluralize(count)} designated as down."
  end

  def header
    {
      cells: [
        { value: 'Unit', justify: :center },
        { value: 'Down From', justify: :center },
        { value: 'Down Until', justify: :center },
        { value: 'Reason', justify: :center },
        { value: 'Note', justify: :center },
        { value: 'Market Rate', justify: :center },
        { value: 'Square Feet', justify: :center },
        { value: 'Bedrooms', justify: :center }
      ]
    }
  end

  def rows
    units.map do |unit|
      downtime = unit.downtimes.active(filters.date).last

      {
        cells: [
          { value: unit.qualified_name, link: unit.url },
          { value: downtime.start_date, justify: :center },
          { value: downtime.end_date, justify: :center },
          { value: downtime.reason.humanize },
          { value: downtime.note },
          { value: unit.price },
          { value: unit.square_feet },
          { value: unit.bedrooms }
        ]
      }
    end
  end

  def units
    @downtimes ||= UnitStatusQuery
                   .new(scoped_units, date: filters.date)
                   .search
                   .status_down(filters.date)
                   .includes(:floorplan)
  end
end
