# Helper module to help detect accounting changes that would impact reports.
module Reports::V3::RejectChanges
  class ReportChangedError < StandardError
    attr_reader :before, :after

    def initialize(before, after)
      @before = before
      @after = after
      super('Report changed.')
    end
  end

  def without_changing(&)
    ActiveRecord::Base.transaction do
      before = as_json

      yield

      after = as_json

      fail ReportChangedError.new(before, after) unless before == after
    end
  end
end
