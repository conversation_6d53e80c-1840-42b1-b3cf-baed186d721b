class Reports::V3::Parking < Reports::V3::Report
  def title
    'Parking Reservations'
  end

  def self.available_filters
    %i[parking_lot date]
  end

  def self.default_filter_values
    { date: Time.zone.today }
  end

  def self.parking_lot_values
    non_commercial_parking_lots.sort_by(&:name).map do |lot|
      [lot.name, lot.id]
    end
  end

  def mapping
    {
      parker: I18n.t('tenant_term'),
      unit: 'Unit',
      phone: 'Phone',
      space: 'Space',
      license_plate: 'Plate',
      vehicle: 'Vehicle',
      end_date: 'Expires'
    }
  end

  def default_grouping
    :parking_lot
  end

  def basis
    Class.new(Reports::V3::Basis::ParkingReservation) do
      def relation
        super.where(
          parking_lot: Reports::V3::Parking.non_commercial_parking_lots
        )
      end

      def self.columns
        superclass.columns
      end

      def self.groupings
        superclass.groupings
      end
    end
  end

  def self.non_commercial_parking_lots
    joined = ParkingLot.left_joins(property: { company: :portfolio })

    joined.where.not(portfolios: { name: 'Commercial Parking' })
          .or(joined.where(property: nil))
  end
end
