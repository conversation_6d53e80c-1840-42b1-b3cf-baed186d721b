class Reports::V3::ConsolidatedManagementIncomeStatement < Reports::V3::ConsolidatedIncomeStatement
  def accounting_context
    @accounting_context ||= Customer.current.client_entity.accounting_context
  end

  def rows
    Plutus.in_cash_basis(journal_cache_hint: nil) do
      super
    end
  end

  def columns
    @columns ||= CustomerPortfolioColumns.new(
      parent: parent,
      period_start: period_starting,
      period_end: period_ending
    ).columns
  end

  def account_balance(account, column)
    @matrix ||= Accounting::AccountBalanceMatrix.new(
      accounting_context: accounting_context,
      start_date: column.period_start,
      end_date: column.period_end
    )

    @matrix.balance_for_customer_portfolio_filter(
      account: account,
      filter: column.try(:customer_portfolio_id)&.to_s
    )
  end

  class CustomerPortfolioColumn
    attr_reader :name, :customer_portfolio_id, :period_start, :period_end

    def initialize(name:, customer_portfolio_id:, period_start:, period_end:)
      @name = name
      @customer_portfolio_id = customer_portfolio_id.to_s
      @period_start = period_start
      @period_end = period_end
    end
  end

  class CustomerPortfolioColumns
    def initialize(parent:, period_start:, period_end:)
      @parent = parent
      @period_start = period_start
      @period_end = period_end
    end

    def columns
      [*portfolio_columns.sort_by(&:name), none_column]
    end

    private

    attr_reader :parent, :period_start, :period_end

    def parent_column
      Reports::V3::ConsolidatedReport::EntitiesColumn.new(
        name: parent.name,
        entities: Company.where(id: parent.id),
        period_start: period_start,
        period_end: period_end
      )
    end

    def portfolio_columns
      Portfolio.all.map do |portfolio|
        CustomerPortfolioColumn.new(
          name: portfolio.name,
          customer_portfolio_id: portfolio.id,
          period_start: period_start,
          period_end: period_end
        )
      end
    end

    def none_column
      CustomerPortfolioColumn.new(
        name: 'None',
        customer_portfolio_id: 'none',
        period_start: period_start,
        period_end: period_end
      )
    end
  end
end
