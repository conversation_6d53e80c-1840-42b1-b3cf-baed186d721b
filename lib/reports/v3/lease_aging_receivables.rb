class Reports::V3::LeaseAgingReceivables < Reports::V3::Report
  def self.available_filters
    [
      :portfolio_accounting_context,
      (:owner if CustomerSpecific::Behavior.reports_add_owner_filters?),
      :date
    ].compact
  end

  def self.default_filter_values
    { date: Time.zone.today }
  end

  def mapping
    {
      lease: 'Lease',
      property: 'Property',
      lease_ledger_id: 'Ledger',
      open_count: 'Open',
      due: 'Total Due',
      current: 'Current',
      overdue: 'Overdue',
      one_to_thirty_days: '1 - 30',
      thirty_one_to_sixty_days: '31 - 60',
      sixty_one_to_ninty_days: '61 - 90',
      ninty_one_plus_days: '91 +'
      # prepaid: 'Prepaid',
      # balance: 'Balance'
    }
  end

  def scope
    Reports::V3::Scope.new(user: user, filters: filters)
  end

  def basis
    Class.new(Reports::V3::Basis::Base) do
      def relation
        # TODO: #present
        Lease::Chain::AgingReceivablesSummary.where.not(due_cents: 0)
      end

      def rows
        @rows ||= LeaseMembership::AgingDelinquency.with_effective_date(filters.date) do
          super
        end
      end

      def scoped(query)
        scope.apply(query) { |s| where(unit: s.units) }
      end

      def joined(query)
        query.includes(
          unit: :property,
          # TODO: move #tenant to AgingReceivablesSummary
          lease_chain: [leases: :primary_tenant]
        )
      end

      column :lease do
        tenant = lease_chain.primary_tenant

        { value: tenant.name, link: tenant.url }
      end

      column :lease_ledger_id do
        { value: lease_chain.id }
      end

      column :property do
        { value: property.name, link: property.url }
      end

      column :open_count do
        { value: open_invoice_count }
      end

      column :due do
        { value: due }
      end

      column :current do
        { value: current }
      end

      column :overdue do
        { value: overdue }
      end

      column :one_to_thirty_days do
        { value: one_to_thirty_days }
      end

      column :thirty_one_to_sixty_days do
        { value: thirty_one_to_sixty_days }
      end

      column :sixty_one_to_ninty_days do
        { value: sixty_one_to_ninety_days }
      end

      column :ninty_one_plus_days do
        { value: ninety_one_plus_days }
      end

      column :prepaid do
        { value: prepaid }
      end

      column :balance do
        { value: balance }
      end
    end
  end

  def footer
    totals_row([*3...mapping.length])
  end
end
