class Reports::V3::CollectionsCommunications < Reports::V3::Report
  def self.available_filters
    %i[date_range status]
  end

  def self.default_filter_values
    {
      start_date: 1.month.ago,
      end_date: Time.zone.today
    }
  end

  def self.status_values
    Collections::Communication.statuses.keys
  end

  def mapping
    {
      date: 'Date',
      channel: 'Channel',
      status: 'Status',
      processed_at: 'Processed At',
      contact: 'Contact',
      balance: 'Balance'
    }
  end

  def basis
    Reports::V3::Basis::CollectionsCommunication
  end
end
