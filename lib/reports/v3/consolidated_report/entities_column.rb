class Reports::V3::ConsolidatedReport::EntitiesColumn
  attr_reader :name, :entities, :period_start, :period_end

  def initialize(name:, entities:, period_start:, period_end:)
    @name = name
    @entities = entities
    @period_start = period_start
    @period_end = period_end
  end

  def query_params
    { journal_id: entities.pluck(:id) }
  end

  def budget_for(account)
    amounts = budget_amounts.where(account: account)

    Money.sum(amounts.map(&:amount))
  end

  def budgets
    year = entities.first.current_fiscal_year(period_end).calendar_year

    Budget.where(company: entities, property: nil, year: year)
  end

  def budget_amounts
    BudgetAmount::Monthly
      .where(budget: budgets)
      .where('month >= ?', period_start)
      .where('month <= ?', period_end)
  end
end
