class Reports::V3::ConsolidatedReport::PropertyIncomeStatementColumns
  def initialize(entity:, period_start:, period_end:)
    @entity = entity
    @period_start = period_start
    @period_end = period_end
  end

  def columns
    properties.map { |property| property_column(property) }
  end

  private

  attr_reader :entity, :period_start, :period_end

  def property_column(property)
    Reports::V3::ConsolidatedReport::PropertyColumn.new(
      name: property.name,
      property: property,
      period_start: period_start,
      period_end: period_end
    )
  end

  def properties
    entity.properties.unarchived(period_start).order(name: :asc)
  end
end
