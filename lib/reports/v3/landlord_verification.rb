class Reports::V3::LandlordVerification < Reports::V3::Report
  def self.available_filters
    %i[date_range status]
  end

  def self.default_filter_values
    { start_date: 1.month.ago, end_date: Time.zone.today }
  end

  def self.status_values
    %i[pending completed submitted skipped]
  end

  def mapping
    {
      lease_application: 'Lease Application',
      created_at: 'Sent',
      status: 'Status',
      submitted_at: 'Completed',
      landlord_name: 'Landlord',
      landlord_email: 'Email',
      landlord_phone: 'Phone',
      address: 'Unit Address',
      monthly_rent: 'Monthly Rent',
      late_rent_count: 'Late Rent Count',
      number_of_occupants: 'Number of Occupants',
      move_out_condition: 'Move Out Condition',
      notice_given: 'Notice Given',
      taken_to_court: 'Taken to Court',
      deposit_information: 'Deposit Information',
      additional_information: 'Additional Information'
    }
  end

  def basis
    Reports::V3::Basis::LandlordVerification
  end
end
