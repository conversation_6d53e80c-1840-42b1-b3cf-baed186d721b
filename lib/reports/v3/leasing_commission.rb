class Reports::V3::LeasingCommission < Reports::V3::Report
  def self.available_filters
    if Feature.enabled?(:reports_scope_portfolios, Customer.current)
      %i[portfolio_accounting_context date_range]
    else
      %i[accounting_context date_range]
    end
  end

  def self.default_filter_values
    {
      start_date: 1.month.ago,
      end_date: Time.zone.today
    }
  end

  def mapping
    {
      property: 'Property',
      unit: 'Unit',
      name: 'Tenant',
      rent_amount: 'Rent',
      start_date: 'Start Date',
      end_date: 'End Date',
      execution_date: 'Execution Date',
      term_length: 'Term',
      new_or_renewal: 'Kind'
    }
  end

  def grouping
    :property
  end

  def scope
    Reports::V3::Scope.new(user: user, filters: filters)
  end

  def basis
    Class.new(Reports::V3::Basis::Lease) do
      def self.columns
        superclass.columns
      end

      def self.groupings
        superclass.groupings
      end

      def relation
        super.where(eligible_for_placement_fee: true)
      end
    end
  end

  def sections
    super.map do |section|
      section.merge(footer: footer_row(section))
    end
  end

  private

  def footer_row(section)
    rows = section[:rows]

    count = header_values.count

    {
      cells: Array.new(count) do |i|
        if i.zero?
          { value: 'Total', type: 'b' }
        elsif i == 3
          value = sum_column(rows)[i]
          type = if value.is_a?(Money)
                   'b$'
                 else
                   'b'
                 end
          { value: value, type: type }
        else
          {}
        end
      end
    }
  end
end
