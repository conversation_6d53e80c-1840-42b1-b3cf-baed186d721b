class Reports::V3::InspectionTemplates < Reports::V3::Report
  def self.available_filters
    %i[inspection_template]
  end

  def name
    I18n.t('activerecord.models.inspection/template').pluralize
  end

  def mapping
    {
      template: 'Template',
      category: 'Category',
      section: 'Section',
      prompt: 'Prompt',
      kind: 'Type',
      options: 'Options'
    }
  end

  def basis
    Reports::V3::Basis::InspectionQuestion
  end
end
