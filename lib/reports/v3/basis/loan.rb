class Reports::V3::Basis::Loan < Reports::V3::Basis::Base
  def relation
    Lending::Loan.all
  end

  def joined(query)
    query = query.with_property_loan_index if mapping.key?(:property_loan_index)

    includes = {
      tags: :tags,
      lender: :lender,
      borrower: :borrower,
      property: :property
    }.filter_map { |key, value| value if mapping.key?(key) }

    query.includes(includes)
  end

  def filtered(query)
    query = query.where(taggings: { tag_id: filters.tag }) if filters.tag.present?

    query = query.unarchived(filters.date) if filters.date.present?

    query
  end

  column :lender do
    { value: lender.name, link: lender.url }
  end

  column :borrower do
    { value: borrower.name, link: borrower.url }
  end

  column :property do
    { value: property.name, link: property.url }
  end

  column :number do
    { value: loan_number, link: lending_loan_path(self) }
  end

  column :property_loan_index do
    { value: property_loan_index, link: lending_loan_path(self) }
  end

  column :origination_date do
    { value: origination_date }
  end

  column :interest_start_date do
    { value: interest_start_date }
  end

  column :first_payment_date do
    { value: first_payment_date }
  end

  column :maturity_date do
    { value: maturity_date }
  end

  column :interest_rate_percentage do
    { value: interest_rate_percentage }
  end

  column :repayment_type do
    { value: repayment_type.titleize }
  end

  column :payment_frequency do
    { value: payment_frequency.titleize }
  end

  column :principal do
    { value: principal }
  end

  column :installment_amount do
    { value: installment_amount }
  end

  column :principal_balance do |filters|
    value = if CustomerSpecific::Behavior.xlsx_loan_values?
              xlsx_unpaid_principal_balance(filters.date)
            else
              principal_balance(filters.date)
            end

    { value: value }
  end

  column :interest_balance do |filters|
    value = if CustomerSpecific::Behavior.xlsx_loan_values?
              xlsx_unpaid_interest_balance(filters.date)
            else
              interest_balance(filters.date)
            end

    { value: value }
  end

  column :balance do |filters|
    value = if CustomerSpecific::Behavior.xlsx_loan_values?
              xlsx_total_due(filters.date)
            else
              balance(filters.date)
            end

    { value: value }
  end

  column :late_balance do |filters|
    value = if CustomerSpecific::Behavior.xlsx_loan_values?
              xlsx_total_past_due(filters.date)
            else
              late_balance(filters.date)
            end

    { value: value }
  end

  column :tags do
    { value: tags.map(&:tag).join(', ') }
  end

  column :notes do
    { value: notes }
  end
end
