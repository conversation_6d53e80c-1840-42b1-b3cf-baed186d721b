class Reports::V3::Basis::LandlordVerification < Reports::V3::Basis::Base
  def relation
    ::LandlordVerification.all
  end

  def ordered(query)
    query.reorder(created_at: :desc)
  end

  def filtered(query)
    range = filters.start_date.beginning_of_day..filters.end_date.end_of_day

    query = query.where(created_at: range)

    case filters.status
    when 'pending' then query.pending
    when 'completed' then query.completed
    when 'submitted' then query.submitted
    when 'skipped' then query.skipped
    else
      query
    end
  end

  column :lease_application do
    {
      value: lease_application.primary_tenant.name,
      link: lease_application.url
    }
  end

  column :status do
    { value: status_text }
  end

  column :landlord_name do
    { value: landlord_information.landlord_name, justify: :center }
  end

  column :landlord_email do
    { value: landlord_information.landlord_email, justify: :center }
  end

  column :landlord_phone do
    { value: landlord_information.formatted_landlord_phone, justify: :center }
  end

  column :created_at do
    { value: created_at }
  end

  column :submitted_at do
    { value: submitted_at }
  end

  column :address do
    { value: tenant_information&.address&.simple_address }
  end

  %i[
    monthly_rent late_rent_count
    move_out_condition number_of_occupants notice_given taken_to_court
    deposit_information additional_information
  ].each do |key|
    column key do
      if submitted?
        value = case key
                when :deposit_information
                  lease_information.return_full_security_deposit
                when :notice_given, :taken_to_court
                  lease_information.taken_to_court ? 'Yes' : 'No'
                when :move_out_condition
                  lease_information.move_out_condition.titleize
                else
                  lease_information.public_send(key)
                end

        { value: value, justify: :center }
      end
    end
  end
end
