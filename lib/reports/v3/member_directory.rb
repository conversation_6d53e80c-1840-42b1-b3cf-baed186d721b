class Reports::V3::MemberDirectory < Reports::V3::Report
  def self.available_filters
    %i[property date tag]
  end

  def self.default_filter_values
    { date: Time.zone.today }
  end

  def self.possible_tags
    tenant_tags = TagsQuery.new.search.for_tenants
    agreement_tags = TagsQuery.new.search.for_agreements_simple_agreements
    tenant_tags.or(agreement_tags)
  end

  def mapping
    {
      chapter: 'Chapter',
      first_name: 'First Name',
      last_name: 'Last Name',
      email: 'Email',
      phone: 'Phone',
      date_of_birth: 'Date of Birth',
      forwarding_address: 'Forwarding Address',
      tags: 'Tags'
    }.tap do |mapping|
      mapping[:social_security_number] = 'SSN' if user.role.can_see_eins?
    end
  end

  def basis
    Reports::V3::Basis::MemberDirectoryBasis
  end

  def scope
    Reports::V3::Scope.new(user: user, filters: filters)
  end

  def header_values
    super + basis_instance.metadata_keys.map(&:titleize)
  end
end
