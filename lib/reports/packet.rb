class Reports::Packet
  attr_reader :template, :user, :options

  def initialize(template:, user:, options:)
    @template = template
    @user = user
    @options = options

    return if options[:period].present?

    return if options[:start_date].present? && options[:end_date].present?

    fail ArgumentError, ':period or :start_date and :end_date must be present in :options'
  end

  def entities
    @entities ||= begin
      companies = user.companies.unarchived.order(name: :asc)

      if options[:entity_id] == 'all'
        companies
      else
        [companies.find_by(id: options[:entity_id])]
      end
    end
  end

  def reports
    entities.flat_map do |entity|
      template.entries.map do |entry|
        entry.report(user: user, extra_filters: extra_filters(entity))
      end
    end
  end

  private

  def extra_filters(entity)
    {
      context_id: entity&.to_sgid&.to_s,
      entity_id: entity&.id,
      date: end_date,
      period: end_date,
      start_date: start_date,
      end_date: end_date
    }.compact
  end

  def period
    case options[:period]
    when Date then options[:period]
    when String then Date.parse(options[:period])
    end
  end

  def start_date
    case options[:start_date]
    when Date then options[:start_date]
    when String then Date.parse(options[:start_date])
    when nil then period.beginning_of_month
    end
  end

  def end_date
    case options[:end_date]
    when Date then options[:end_date]
    when String then Date.parse(options[:end_date])
    when nil then period.end_of_month
    end
  end
end
