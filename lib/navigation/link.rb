class Navigation::Link
  attr_reader :text, :path, :icon, :permission

  # rubocop:disable Metrics/ParameterLists
  def initialize(request:, text:, path:, icon:, links: [], active_filter: nil, permission: true)
    @request = request
    @text = text
    @path = path
    @icon = icon
    @links = links
    @active_filter = active_filter
    @permission = permission
  end
  # rubocop:enable Metrics/ParameterLists

  def active?
    match = @active_filter || path

    @request.path.start_with?(match)
  end

  def disabled?
    !@permission
  end

  def hidden?
    !@permission || path.nil?
  end

  def links
    if @links.is_a?(Proc)
      @links.call.compact
    else
      @links.compact
    end
  end
end
