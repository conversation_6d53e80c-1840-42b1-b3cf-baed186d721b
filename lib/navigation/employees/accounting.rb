module Navigation::Employees::Accounting
  def accounting
    if user.role.name.in?(['Facilities', 'Human Resources', 'Leasing Agent'])
      return
    end

    path = if Customer.current_subdomain.in?(%w[gebraelmgmt marketplacehomes])
             # Skip over unperformant accounting pulse until it can be removed
             accounting_payables_path
           else
             accounting_links.compact.find(&:permission)&.path
           end

    link(
      text: 'Accounting',
      path: path,
      icon: 'dollar',
      active_filter: %r{/accounting(?!/electronic_payments)|/taxes},
      links: -> { accounting_links },
      permission: can.show_accounting_menu?
    )
  end

  private

  def accounting_links
    [
      accounting_pulse,
      payables,
      receivables,
      payments,
      journals,
      budgets,
      statements,
      ten_ninety_nines
    ]
  end

  def accounting_pulse
    return if house_director?

    link(
      text: 'Pulse',
      path: accounting_pulse_index_path,
      icon: 'heartbeat',
      permission: can.show_accounting_pulse?
    )
  end

  def payables
    return if user.role.name == 'Chapter Advisor / Officer'

    return if lending?

    link(
      text: 'Payables',
      path: accounting_payables_path,
      icon: 'inbox',
      permission: can.show_accounting_payables?
    )
  end

  def receivables
    link(
      text: 'Receivables',
      path: accounting_receivables_path,
      icon: 'dollar',
      permission: can.show_accounting_receivables?
    )
  end

  def payments
    link(
      text: 'Payments',
      path: accounting_payments_path,
      icon: 'payment',
      permission: can.show_accounting_payments?
    )
  end

  def budgets
    return unless Feature.enabled?(:budget, current_customer)

    return if house_director?

    return if lending?

    link(
      text: 'Budgets',
      path: accounting_budgets_path,
      icon: 'calculator',
      permission: can.show_accounting_budgets?
    )
  end

  def statements
    return unless Feature.enabled?(:statements, current_customer)

    return if house_director?

    return if lending?

    link(
      text: 'Statements',
      path: accounting_statements_path,
      icon: 'file outline',
      permission: can.show_accounting_statements?
    )
  end

  def journals
    return if house_director?

    link(
      text: 'Journals',
      path: accounting_journals_path,
      icon: 'book',
      permission: can.show_accounting_journals?
    )
  end

  def ten_ninety_nines
    return unless Feature.enabled?(:taxes_1099s, Customer.current)

    link(
      text: '1099s',
      path: taxes_ten_ninety_nines_path,
      icon: 'percentage',
      active_filter: %r{/taxes},
      permission: can.show_accounting_1099s?
    )
  end
end
