##
# Determines a booking time to use when scheduling Inspectify inspections to
# internal inspectors. Attempts to schedule for beginning of work day in
# inspection's local time, falling back to the next top of the hour during work
# hours, falling back to beginning of the next work day if it is past the end
# of the work day.
class Inspectify::BookingTime
  WORK_START_HOUR = 9
  WORK_END_HOUR = 17

  def initialize(inspectify_order)
    @earliest_start_date = inspectify_order.inspection_report.earliest_start_date
    @local_time_zone = inspectify_order.inspection_report.property.address.time_zone

    fail 'No time zone found for inspection address' unless @local_time_zone
  end

  def booking_time
    # Ideally, work start local time on the ideal booking date.
    time = ideal_booking_time

    # Clamp to the nearest next hour, if the time has already passed.
    time = top_of_the_next_hour if time.past?

    # Clamp to at least work start time.
    time = work_start_today if before_work_start_today?(time)

    # If this is after work end time, move it to tomorrow.
    time = work_start_tomorrow if after_work_end_today?(time)

    time
  end

  private

  attr_reader :local_time_zone

  def ideal_booking_date
    @earliest_start_date || Time.zone.today
  end

  def ideal_booking_time
    ideal_booking_date.in_time_zone(local_time_zone).change(hour: WORK_START_HOUR)
  end

  def work_start_today
    local_time_zone.now.change(hour: WORK_START_HOUR, min: 0, sec: 0)
  end

  def work_end_today
    local_time_zone.now.change(hour: WORK_END_HOUR, min: 0, sec: 0)
  end

  def work_start_tomorrow
    local_time_zone.tomorrow.in_time_zone(local_time_zone).change(hour: WORK_START_HOUR)
  end

  def top_of_the_next_hour
    local_time_zone.now.change(min: 0, sec: 0) + 1.hour
  end

  def before_work_start_today?(time)
    time.today? && time.before?(work_start_today)
  end

  def after_work_end_today?(time)
    time.today? && time.after?(work_end_today)
  end
end
