class BackgroundChecks::RsXmlBuilder
  DOB_FORMAT = '%m-%d-%Y'.freeze
  METHOD = 'SEND ORDER'.freeze

  attr_reader :application, :applicant

  def initialize(background_check)
    @application = background_check.lease_application
    @applicant = background_check.lease_application_membership.applicant
    @package = background_check.package
    @order_id = background_check.id
  end

  def to_xml
    builder = Nokogiri::XML::Builder.new do |xml|
      xml.OrderXML do
        xml.Method METHOD
        xml.Authentication { authentication(xml) }
        xml.TestMode test_mode
        xml.ReturnResultUrl response_url
        xml.OrderingUser ordering_user
        xml.Order { order(xml) }
      end
    end

    builder.to_xml
  end

  private

  def response_url
    "https://#{Customer.current.subdomain}.revela.co/reference_services_hook?order_id=#{@order_id}"
  end

  def order(xml)
    xml.Subject { subject(xml) }
    xml.PackageServiceCode('OrderId' => @order_id) do
      xml.text(@package.service_code)
    end
  end

  def authentication(xml)
    xml.Username ENV.fetch('REFERENCE_SERVICES_USERNAME')
    xml.Password ENV.fetch('REFERENCE_SERVICES_PASSWORD')
  end

  def subject(xml)
    xml.FirstName applicant.first_name
    xml.MiddleName applicant.middle_initial
    xml.LastName applicant.last_name
    xml.DOB date_of_birth
    xml.SSN formatted_ssn(applicant)
    xml.Gender applicant.gender
    xml.Ethnicity applicant.ethnicity
    xml.DLNumber applicant.drivers_license_number
    xml.CurrentAddress { address(xml) }
  end

  def date_of_birth
    return nil if applicant.birth_date.blank?

    Date.parse(applicant.birth_date).strftime(DOB_FORMAT)
  end

  def address(xml)
    address = applicant.address || Address.new
    xml.StreetAddress address.street_address.squish
    xml.City address.city.squish
    xml.State (address.region_code || address.region).squish
    xml.Zipcode address.postal_code.squish
  end

  def ordering_user
    [Customer.current.name, property.name].join(' / ')
  end

  def test_mode
    if Feature.enabled?(:background_checks, Customer.current)
      'NO'
    else
      'YES'
    end
  end

  def formatted_ssn(applicant)
    return nil if applicant.social_security_number.blank?

    applicant.social_security_number.gsub(/[^0-9]/, '')
  end

  delegate :property, to: :application
end
