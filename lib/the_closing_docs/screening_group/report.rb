class TheClosingDocs::ScreeningGroup::Report < TheClosingDocs::Service
  attr_reader :screening_group

  def initialize(screening_group)
    @screening_group = screening_group
    super()
  end

  def call
    endpoint = [
      SCREENING_GROUP_ENDPOINT,
      screening_group.api_id,
      'report'
    ].join('/')

    response = connection.get(endpoint)

    body = response.body

    url = body['url']

    OpenStruct.new(successful?: true, url: url)
  rescue Faraday::Error
    OpenStruct.new(successful?: false)
  end
end
