class DocxFilling::ListTags
  def self.tags(docx_file)
    Zip::File.open(docx_file.path) do |zip_file|
      entry = zip_file.entries.find do |file|
        file.name.ends_with?('document.xml')
      end

      xml = entry.get_input_stream.read

      xml.scan(/{[^}]+/).map do |tag|
        tag.gsub(/{/, '').gsub(/}/, '')
      end
    end
  rescue StandardError => e
    Honeybadger.notify(e)
    []
  end
end
