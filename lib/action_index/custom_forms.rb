class ActionIndex::CustomForms < ActionIndex::Base
  def base_path
    organization_forms_path
  end

  def filter_items
    [filter_archived]
  end

  def selection_items
    [archive, unarchive]
  end

  private

  def filter_archived
    {
      type: 'dropdown',
      name: 'archived',
      default_text: 'Unarchived',
      values: [
        %w[All all],
        %w[Archived archived],
        %w[Unarchived unarchived]
      ]
    }
  end

  def archive
    {
      type: 'button',
      text: 'Archive',
      modal: :confirm_archive_modal,
      permission: user.can.archive_organization_forms?
    }
  end

  def unarchive
    {
      type: 'button',
      text: 'Unarchive',
      action: 'bulk_unarchive',
      permission: user.can.archive_organization_forms?
    }
  end
end
