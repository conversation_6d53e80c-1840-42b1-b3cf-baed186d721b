class ActionIndex::Onboardings < ActionIndex::Base
  def base_path
    onboardings_path
  end

  def filter_items
    [filter_portfolio, filter_date]
  end

  def selection_items
    [archive]
  end

  private

  def filter_date
    {
      type: 'date',
      name: 'created_at',
      default_text: 'Date'
    }
  end

  def filter_portfolio
    {
      type: 'dropdown',
      name: 'portfolio_id',
      default_text: 'Portfolio',
      search: {
        placeholder: 'Search portfolios...'
      },
      special_values: [['All Portfolios', ' ']],
      values: user.portfolios.pluck(:name, :id)
    }
  end

  def archive
    {
      type: 'button',
      text: 'Archive',
      modal: :confirm_archive_modal
    }
  end
end
