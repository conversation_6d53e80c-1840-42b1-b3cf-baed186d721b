class MemberOnboarding::Tenants::Validator::RequiredGuarantorInformation <
  MemberOnboarding::Tenants::Valida<PERSON>

  attr_reader :member
  alias_attribute :guarantor, :tenant

  def initialize(guarantor:, requirements:, member:)
    super(tenant: guarantor, requirements: requirements)
    @member = member
  end

  def call
    tenant.validate
    validate_presence!('phone')
    validate_presence!('email')
    self.class.validate_not_member!(guarantor:, member:)
    validate_presence!('forwarding_address') if requirements.gathering_address?
    if requirements.gathering_drivers_license_number?
      validate_metadata_presence!('drivers_license_number')
    end

    return unless requirements.gathering_collections_information?

    validate_presence!('taxpayer_identification')
    validate_presence!('date_of_birth')
  end

  def self.validate_not_member!(guarantor:, member:)
    same_email = (guarantor.email&.squish&.downcase == member.email.downcase.squish)
    is_lease_primary_member =
      Tenant::LeaseMembershipsQuery.current_lease_membership(tenant: guarantor)&.primary_tenant?
    is_membership_primary_member =
      Tenant::SimpleAgreementsQuery.current_simple_agreement(tenant: guarantor)
        &.memberships&.where(tenant: guarantor)&.primary&.any?

    if (same_email || is_lease_primary_member || is_membership_primary_member)
      guarantor.errors.add(:base, 'Cannot set member as guarantor')
    end
  end
end
