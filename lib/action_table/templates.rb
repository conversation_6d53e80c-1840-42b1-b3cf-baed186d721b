class ActionTable::Templates < ActionTable::Base
  def frontend_sorting?
    false
  end

  def columns
    [name, updated, edit, delete]
  end

  def multi_selection?
    true
  end

  private

  def name
    {
      title: 'Name',
      width: :four,
      align: :center,
      value: ->(template) { template.name },
      link: lambda { |template|
        template.edit_url if can.update_messaging_templates?
      }
    }
  end

  def updated
    {
      title: 'Updated',
      align: :center,
      width: :nine,
      value: ->(template) { template.updated_at.to_fs(:short_datetime) }
    }
  end

  def edit
    {
      title: 'Actions',
      colspan: 2,
      width: :three,
      align: :center,
      value: ->(_) { 'Edit' },
      link: ->(template) { template.edit_url },
      permission: can.update_messaging_templates?
    }
  end

  def delete
    {
      title: 'Actions',
      colspan: 0,
      width: :three,
      align: :center,
      value: ->(_) { 'Delete' },
      link: ->(template) { template.remove_url },
      link_options: lambda { |_|
        {
          data: {
            method: :delete,
            confirm: 'Delete Template?'
          }
        }
      },
      permission: can.destroy_messaging_templates?
    }
  end
end
