class ActionTable::Employees < ActionTable::Base
  def frontend_sorting?
    false
  end

  def multi_selection?
    false
  end

  def columns
    [name, email, phone, role, properties]
  end

  def name
    {
      title: 'Name',
      align: :left,
      sort_key: :last_name,
      value: ->(employee) { employee.name },
      link: ->(employee) { organization_employee_path(employee) }
    }
  end

  def email
    {
      title: 'Email',
      sort_key: :email,
      value: ->(employee) { employee.email },
      link: ->(employee) { "mailto:#{employee.email}" }
    }
  end

  def phone
    {
      title: 'Phone',
      sort_key: :phone,
      value: ->(employee) { employee.formatted_phone }
    }
  end

  def role
    {
      title: 'Role',
      sort_key: :role,
      value: ->(employee) { employee.role.name }
    }
  end

  def properties
    {
      title: 'Properties',
      value: lambda do |employee|
        if employee.top_level?
          'All'
        elsif employee.properties.length == 1
          employee.properties.first.name
        else
          "#{employee.properties.length} Properties"
        end
      end
    }
  end
end
