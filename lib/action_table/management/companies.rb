class ActionTable::Management::Companies < ActionTable::Base
  def multi_selection?
    true
  end

  def columns
    [name, owner_contacts, billed_through, last_closed]
  end

  def table_row_class_names
    ['multiline-top']
  end

  private

  def name
    {
      title: 'Name',
      align: :left,
      width: :six,
      value: ->(company) { company.name },
      view: ->(company) { name_cell_for(company) }
    }
  end

  def owner_contacts
    {
      title: 'Owner Contact(s)',
      align: :left,
      width: :five,
      value: ->(_) { 'Owner Contacts(s)' },
      view: ->(company) { owner_contacts_cell_for(company) }
    }
  end

  def billed_through
    {
      title: 'Billed Through',
      width: :two,
      value: ->(company) { company.management_fees_billed_through&.to_fs(:short_date) || 'Never' }
    }
  end

  def last_closed
    {
      title: 'Last Closed',
      width: :two,
      value: ->(company) { company.locked_at&.to_fs(:short_date) || 'Never' }
    }
  end

  def name_cell_for(company)
    path = if company.setup?
             manage_company_path(company)
           else
             setup_manage_company_path(company)
           end
    html = tag.a(company.name, href: path) +
           tag.br +
           build_properties_list(company)
    unless company.setup?
      html += tag.br
      html += tag.a(tag.i(class: %w[yellow warning icon]) + 'Requires Setup',
                    class: %w[ui small basic yellow label align-with-text])
    end

    html
  end

  def owner_contacts_cell_for(company)
    tag.div do
      company.owners.each.with_index do |owner, idx|
        if can.show_portfolio_owners?
          concat tag.a owner.name, href: owner_path(owner)
        else
          concat tag.span owner.name
        end

        concat tag.span ', ' unless idx == company.owners.size - 1
      end
    end
  end

  def build_properties_list(company)
    path = manage_company_path(company)
    property_count = company.properties.size
    property_link_limit = 2
    property_limit = if property_count > property_link_limit
                       property_link_limit - 1
                     else
                       property_link_limit
                     end
    tag.div class: %w[ui horizontal link list] do
      property_links(company, property_limit)

      if property_count > property_limit
        concat tag.a "(#{property_count - property_limit} more)", href: path, class: 'item'
      end

      concat tag.span 'No Properties', class: 'item' if property_count.zero?
    end
  end

  def property_links(company, property_limit)
    company.properties.take(property_limit).each do |property|
      if can.show_portfolio_properties?
        concat tag.a truncate(property.name), href: property_path(property), class: ['item']
      else
        concat tag.span truncate(property.name), class: ['item']
      end
    end
  end
end
