class University::TermProrater
  attr_reader :lease_membership, :overlay, :year_mode, :academic_year

  def initialize(lease_membership, overlay: nil, year_mode: false, academic_year: nil)
    @lease_membership = lease_membership
    @overlay = overlay
    @academic_year = academic_year
    @year_mode = year_mode
  end

  def rent
    return nil unless lease_membership

    lease_membership.amount
  end

  def sem_1
    @sem_1 ||= begin
      return nil unless lease_membership

      recurring = sem_1_months * sem_1_rent
      one_time = if move_in_date.month >= 6
                   one_time_charges
                 else
                   Money.zero
                 end

      recurring + one_time
    end
  end

  def sem_2
    @sem_2 ||= begin
      return nil unless lease_membership

      recurring = sem_2_months * sem_2_rent
      one_time = if move_in_date.month < 6
                   one_time_charges
                 else
                   Money.zero
                 end

      recurring + one_time
    end
  end

  def sem_1_fractions
    @sem_1_fractions ||= month_fractions.select do |year, month, _value|
      if year_mode
        year < academic_year
      elsif year_lease?
        year == move_in_date.year
      else
        month >= 6
      end
    end
  end

  def sem_1_rent
    @sem_1_rent ||= begin
      start = sem_1_fractions.first

      return Money.zero unless start

      range = Date.new(start[0], start[1], 1).all_month

      Money.sum(allocations.recurring.merge(active_filter(range)).map(&:amount))
    end
  end

  def sem_1_months
    sem_1_fractions.sum do |_year, _month, value|
      value
    end
  end

  def sem_2_fractions
    @sem_2_fractions ||= month_fractions.select do |year, month, _value|
      if year_mode
        year >= academic_year
      elsif year_lease?
        year > move_in_date.year
      else
        month < 6
      end
    end
  end

  def sem_2_months
    sem_2_fractions.sum do |_year, _month, value|
      value
    end
  end

  def sem_2_rent
    @sem_2_rent ||= begin
      start = sem_2_fractions.first

      return Money.zero unless start

      range = Date.new(start[0], start[1], 1).all_month

      Money.sum(allocations.recurring.merge(active_filter(range)).map(&:amount))
    end
  end

  def one_time_charges
    if range.include?(move_in_date)
      Money.sum(allocations.one_time.map(&:amount))
    else
      Money.zero
    end
  end

  def range
    if overlay
      ([move_in_date, overlay.first].max)..([move_out_date, overlay.last].min)
    else
      move_in_date..move_out_date
    end
  end

  def month_fractions
    @fractions ||= day_counts.map do |(year, month), count|
      days_in_month = Date.new(year, month, 1)
                          .end_of_month.day

      fraction = count.to_f / days_in_month

      value = if lease.prorated_nearest_month?
                fraction > 0.5 ? 1.0 : 0.0
              elsif lease.prorated_half_month?
                if fraction > 0.75
                  1.0
                elsif fraction > 0.25
                  0.5
                else
                  0.0
                end
              else
                fraction
              end

      [year, month, value]
    end
  end

  def day_counts
    range.to_a
         .group_by { |day| [day.year, day.month] }
         .map { |a, b| [a, b.count] }
  end

  def year_lease?
    @yl ||= move_in_date.year < move_out_date.year
  end

  def active_filter(range)
    ChargeSchedule::Entry.where(
      start_date: ..range.last,
      end_date: range.first..
    )
  end

  delegate :lease, :move_in_date, :move_out_date, :allocations,
           to: :lease_membership
end
