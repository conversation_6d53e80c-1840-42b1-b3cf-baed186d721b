module Zillow::Hotpads::Common
  extend ActiveSupport::Concern

  included do
    include Zillow::Hotpads::Identifiers
    def zip_code
      cc = address.country_code || 'US'
      cc == 'US' ? address.postal_code&.gsub(/[^\d]/, '').try(:[], ..4) : address.postal_code
    end

    def customer = @customer

    def client_entity = @client_entity ||= customer.client_entity

    def contact_name = agent&.name

    def contact_email = agent.email

    def contact_phone = agent.phone

    def hotpads_lease_term(listing)
      lease_term_length = listing.configuration.lease_terms.pluck(:length).compact.first
      return 12 if lease_term_length.blank?

      case lease_term_length
      when 12 then 'OneYear'
      when 6 then 'SixMonths'
      when 1 then 'Monthly'
      else
        'ContactForDetails'
      end
    end
  end
end
