class Zeamster::Contact::Create < Zeamster::Service
  extend ::Service

  attr_reader :address, :entity

  def initialize(entity:, merchant_account:, address:)
    @entity = entity
    @merchant_account = merchant_account
    @address = address
  end

  def call
    response = post('/v2/contacts', request)

    return unsuccessful(response) unless response.success?

    result = OpenStruct.new(response.body['contact'])

    contact = entity.merchant_account_contacts.create!(
      payment_processor: merchant_account.payment_processor,
      location_id: merchant_account.location_id,
      reference_number: result.id
    )

    success(contact: contact, entity: entity)
  end

  private

  def request
    {
      contact: {
        location_id: location_id,
        first_name: first_name&.truncate(30),
        last_name: last_name&.truncate(30),
        email: entity_email,
        address: address.line_one,
        city: address.city,
        state: address.region,
        zip: address.postal_code
      }
    }
  end

  def company?
    entity.is_a?(Company) || entity.is_a?(Vendor)
  end

  def first_name
    entity.first_name unless company?
  end

  def last_name
    if company?
      entity.name
    else
      entity.last_name
    end
  end

  def entity_email
    if entity.is_a?(Company)
      entity.contact_email
    else
      entity.email
    end
  end
end
