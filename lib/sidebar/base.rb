class Sidebar::Base
  extend Service

  include ActionDispatch::Routing
  include ActionView::Context
  include ActionView::Helpers
  include Rails.application.routes.url_helpers

  cattr_accessor :current_path

  def call
    render
  end

  protected

  def menu_class
    'ui vertical orange secondary pointing menu'
  end

  def render
    content_tag(:div, class: menu_class) do
      items.compact.map do |item|
        link = item[:link] unless item[:disabled]
        tag = item[:disabled] ? :span : :a
        klass = 'item'

        active = (Sidebar::Base.current_path == item[:link] || item[:active]) && item[:active] != false
        klass = "active #{klass}" if active
        klass = "disabled #{klass}" if item[:disabled]

        icon_tag = item[:icon] ? content_tag(:i, nil, class: [item[:icon], 'icon']) : ''

        item_tag = content_tag(tag, icon_tag + item[:name], class: klass, href: link)

        if item[:embed_menu]
          concat(
            content_tag(:div, class: 'item') do
              concat(item_tag)
              concat(item[:menu]) if item[:menu]
            end
          )
        else
          concat(item_tag)
          concat(item[:menu]) if item[:menu]
        end
      end
    end
  end
end
