class Importers::V3::MaintenanceTickets < Importers::V3::Base
  include ActionView::Helpers::TextHelper

  protected

  def process_item(row)
    number = row.value('WO#', 'Work Order Number', 'Meld number')

    return if number.blank? # Skip empty lines, headers etc.

    regardable = regarding(row)

    if regardable.blank?
      Rails.logger.warn "Row #{row.index + 1}: Unable to find service area"
      return nil
    end

    subject = row.value('Description')

    description = row.value('Detailed description', 'Description', 'Job Description', 'Instructions') ||
                  subject || 'No Description'

    subject ||= description

    ticket = MaintenanceTicket.create_with(
      opened_at: opened_at(row),
      opened_by: opened_by(row),
      subject: truncate(subject, length: 48),
      description: description,
      closed_at: closed_at(row),
      status: status(row),
      urgency: urgency(row)
    ).find_or_initialize_by(
      number: number
    )

    MaintenanceTicket::Regard.call(ticket, regardable)

    ticket.tags = tags(row)

    closing_comment = row.value('Closing Comments').presence
    if closing_comment && ticket.new_record?
      ticket.events.comment.build(
        author: ticket.opened_by,
        body: closing_comment,
        created_at: ticket.closed_at
      )
    end

    ticket.save!

    record_imported(ticket)
  end

  def status(row)
    value = row.value('Status', 'Meld status').downcase
    if value == 'new'
      'fresh'
    elsif value.in?(['open', 'estimate requested', 'estimated', 'waiting', 'scheduled', 'pending meld assignment', 'pending vendor acceptance', 'pending more vendor availability'])
      'seen'
    elsif value.in?(['completed', 'work completed', 'completed no need to bill', 'canceled', 'canceled by manager', 'vendor could not complete', 'canceled by tenant'])
      'closed'
    elsif value.in?(['pending completion'])
      'in_progress'
    else
      value
    end
  end

  def urgency(row)
    value = row.value('Urgency', 'Priority')&.downcase

    if value.in?(['low'])
      'minor'
    elsif value.in?(['high'])
      'critical'
    else
      'normal'
    end
  end

  def regarding(row)
    property = property(row)

    unit = property&.units&.find_by(name: row.value('Unit'))
    if unit
      lease = LeasesQuery.new.search.by_unit(unit).active(opened_at(row)).last
      lease&.lease_memberships&.first || unit
    else
      property
    end
  end

  def property(row)
    name = row.value('Property')

    if name
      name = name.split(' - ').first.strip
      id = property_cache[name]
      Property.find(id) if id
    else
      line_one = row.value('Address line 1')
      city = row.value('City')
      Address
        .where(addressable_type: 'Property')
        .where("REPLACE(line_one, ' ', '') = REPLACE(?, ' ', '')", line_one)
        .where("REPLACE(city, ' ', '') = REPLACE(?, ' ', '')", city)
        .first&.addressable
    end
  end

  def tags(row)
    row.value('Tags')&.split(',') || []
  end

  def opened_at(row)
    date = row.value('Date Created', 'Created At', 'Meld creation date')
    date = Date.parse(date) if date.is_a?(String)
    date.change(offset: 'EST')
  end

  def closed_at(row)
    return nil unless status(row) == 'closed'

    date = row.value('Date Completed', 'Completed On', 'Meld completion date')
    date = Date.parse(date) if date.is_a?(String)

    date&.change(offset: 'EST', hour: 12) || opened_at(row)
  end

  def opened_by(row)
    PropertyManager.all.find { |pm| pm.name == row.value('Managed By') } ||
      PropertyManager.first
  end

  def property_cache
    @property_cache ||= Property.pluck(:name, :id).to_h
  end
end
