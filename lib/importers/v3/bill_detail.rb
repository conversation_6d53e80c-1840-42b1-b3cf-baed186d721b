# TODO: handle tenant refunds
class Importers::V3::BillDetail < Importers::V3::Base
  protected

  set_callback :import, :around, :fail_unsuccessful

  def fail_unsuccessful(&)
    @had_errors = false

    yield

    return unless @had_errors

    fail 'Error(s) found, see above output. No transactions were persisted.'
  end

  def journal
    return nil if options['journal_id'].blank?

    @journal ||= Company.find(options['journal_id'])
  end

  def management_company
    Customer.current.client_entity
  end

  def process_item(transaction)
    return unless transaction.amount.positive?

    buyer = management_company
    seller = transaction.vendor

    management_fees = false

    if buyer == seller # Management Fees
      management_fees = true
      buyer = transaction.property
    end

    original_invoice = Invoice.create!(
      post_date: transaction.invoice_date,
      due_date: transaction.due_date,
      description: transaction.description,
      buyer: buyer,
      seller: seller,
      line_items_attributes: transaction.line_items_attributes
    )

    record_imported(original_invoice)

    if transaction.payment_date
      Invoice::MarkPaid.call(
        invoice: original_invoice,
        params: ActionController::Parameters.new(
          payment: {
            date: transaction.payment_date,
            description: 'Payment'
          }
        ),
        receivable: false
      )
    end

    return if management_fees

    Invoice::Forward.call(original_invoice).compact.each do |invoice|
      invoice.payments.each do |payment|
        if transaction.payment_date
          payment.update!(date: transaction.payment_date)
          payment.journal_entries.each do |entry|
            entry.update!(date: transaction.payment_date)
          end
        else
          payment.destroy!
        end
      end

      record_imported(invoice)
    end
  rescue StandardError => e
    @had_errors = true

    row = "Row #{transaction.index}"

    string = [row, transaction.description, e].join(': ')

    log_message(string)
  end

  def processable_items(&)
    markups, originals = parser.data_rows.partition do |row|
      row.value('Description')&.start_with?('Markup')
    end

    transactions_by_hash = {}

    originals.each do |row|
      key = [
        row.value('Reference'),
        row.value('Bill Date'),
        row.value('Due Date'),
        row.value('Property'),
        row.value('Payee Name')
      ].hash

      (transactions_by_hash[key] ||= Transaction.new(journal)).add_line_item(row)
    end

    transactions = transactions_by_hash.values

    markups.each do |row|
      description_value = row.value('Description')

      transaction = if description_value.start_with?('Markup: ')
                      markup_description = description_value.gsub(/^Markup: /, '')

                      transactions.find do |transaction|
                        transaction.invoice_date == row.value('Bill Date') && transaction.line_items.map do |item|
                          item.value('Description').presence || ''
                        end.any? do |description|
                          description.strip[..64] == markup_description.strip[..64]
                        end
                      end
                    elsif description_value.start_with?('Markup of ')
                      account_description = description_value.gsub(/^Markup of /, '')

                      transactions.find do |transaction|
                        transaction.invoice_date == row.value('Bill Date') && transaction.line_items.map do |item|
                          item.value('Account').gsub(/^\d* - /, '')
                        end.any?(account_description)
                      end
                    end

      if transaction
        transaction.add_markup_item(row)
      else
        string = "#{row.index}: Unable to find original transaction for markup '#{description_value}'"
        @had_errors = true
        log_message(string)
      end
    end

    total = transactions.size

    transactions.each.with_index do |transaction, index|
      yield transaction, index, total
    end
  end

  class Transaction
    attr_reader :journal, :line_items, :markup_items

    def initialize(journal)
      @journal = journal
      @line_items = []
      @markup_items = []
    end

    # Expected xlsx index
    def index
      @line_items.first.index
    end

    def add_line_item(item)
      @line_items << item
    end

    def add_markup_item(item)
      @markup_items << item
    end

    def invoice_date
      first_item.value('Bill Date')
    end

    def due_date
      first_item.value('Due Date')
    end

    def payment_date
      first_item.value('Paid Date')
    end

    def description
      first_item.value('Description') || 'Unspecified'
    end

    def amount
      Money.sum(
        line_items.map do |row|
          Monetize.parse(row.value('Paid')) + Monetize.parse(row.value('Unpaid'))
        end
      )
    end

    def vendor
      @vendor ||= begin
        name = first_item.value('Payee Name')

        funny_find(name) || fail("Unable to find contact '#{name}'")
      end
    end

    def line_items_attributes
      line_items.zip(markup_items).map do |row, markup_row|
        amount = Monetize.parse(row.value('Paid')) + Monetize.parse(row.value('Unpaid'))

        receivable_account = if vendor.is_a?(Company)
                               vendor.chart_of_accounts.default_management_fees_revenue_account
                             end

        account_value = row.value('Account')
        payable_account = find_account(account_value) || fail("Unable to find GL Account '#{account_value}'")

        properties = if journal
                       journal.properties.unarchived(invoice_date)
                     else
                       Property.unarchived(invoice_date)
                     end

        if markup_row
          name = markup_row.value('Property Name')
          property = properties.find_by(name: name) || fail("Unable to find property '#{name}'")
          markup_kind = 'fixed'
          markup_raw = markup_row.value('Paid') + markup_row.value('Unpaid')
        else
          name = first_item.value('Property Name')
          property = properties.find_by(name: name) || fail("Unable to find property '#{name}'")
          markup_kind = 'fixed'
          markup_raw = 0
        end
        forward_id = property.to_sgid.to_s

        {
          unit_price: amount.format,
          quantity: 1,
          description: row.value('Description') || 'Unspecified',
          payable_account: payable_account,
          receivable_account: receivable_account,
          markup_kind: markup_kind,
          markup_raw: markup_raw,
          forward_id: forward_id
        }
      end
    end

    def property
      property_name = first_item.value('Property Name')
      Property.find_by!(name: property_name)
    rescue ActiveRecord::RecordNotFound
      raise "Unable to find property '#{property_name}'"
    end

    private

    def first_item
      line_items.first
    end

    def funny_find(value)
      return nil if value.blank?

      (Thread.current[:funny_find_cache] ||= {})[value] ||= begin
        splits = value.to_s.split(',')

        splits.permutation.map(&:join).flat_map do |arrangement|
          v = arrangement.downcase.gsub(/[^a-z0-9]/, '')

          [v.singularize, v.pluralize].flat_map do |pluralization|
            [Vendor, Company].flat_map do |klass|
              klass.where("regexp_replace(LOWER(name), '[^a-z0-9]', '', 'g') = ?", pluralization)
            end
          end
        end.first || find_vendor_by_contact(value)
      end
    end

    def find_vendor_by_contact(value)
      value.to_s.split(/,?\s+/).permutation.filter_map do |arrangement|
        first_name, last_name = *arrangement
        VendorContact.find_by(
          first_name: first_name.strip, last_name: last_name.strip
        )
      end.first&.vendor
    end

    def find_account(value)
      gl_code, header, name = value.to_s.split(' - ')
      Plutus::Account.find_by(gl_code: gl_code)
    end
  end
end
