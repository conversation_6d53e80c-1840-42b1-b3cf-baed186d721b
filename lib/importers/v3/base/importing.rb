module Importers::V3::Base::Importing
  private

  def import(&)
    post_update(&)

    processable_items do |item, index, total|
      set_xlsx_index(item)

      run_callbacks :process_item do
        process_item(item)
      end

      set_percent((index / total.to_f) * 100)

      post_update(&)
    end
  end

  def processable_items(&)
    parser.each_item(&)
  end

  def process_item(item)
    fail NotImplementedError, "Implement #{self.class}#process_item"
  end

  def set_xlsx_index(item)
    @xlsx_index = case item
                  when nil then 0
                  when Array then item[0]&.index
                  else item.index
                  end
  end
end
