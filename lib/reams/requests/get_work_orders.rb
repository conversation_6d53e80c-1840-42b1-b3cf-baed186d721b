class REAMS::Requests::GetWorkOrders < REAMS::Requests::Request
  def initialize(start_date:, end_date:, status:)
    super()
    @start_date = start_date
    @end_date = end_date
    @status = status
  end

  def as_json
    {
      APITransportRq: {
        Signon: {
          SignonPswd: {
            CustId: {
              ClientAppName: client_app_name,
              CustLoginId: username
            },
            CustPswd: {
              EncryptionType: 'NONE',
              Pswd: password
            }
          },
          ClientDate: client_date,
          ClientTime: client_time,
          CustLangPref: nil,
          ProtocolVersion: {
            ClientApp: client_app_name,
            Version: '1.0'
          }
        },
        RequestRq: {
          RqUID: request_uuid,
          RequestType: 'GetWorkorders',
          ContentType: '',
          ReferenceNo: nil,
          Delta: nil,
          UOM: 'range',
          StartDate: @start_date.strftime('%m/%d/%Y'),
          EndDate: @end_date.strftime('%m/%d/%Y'),
          Status: @status,
          VendorName: vendor_name,
          WorkorderType: ''
        },
        Data: nil,
        WorkorderNote: nil,
        WorkorderAuditStatus: nil
      }
    }
  end
end
