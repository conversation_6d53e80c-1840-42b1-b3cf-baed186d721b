class REAMS::Requests::Request
  def as_json
    fail NotImplementedError
  end

  def to_json(options = {})
    as_json.to_json(options)
  end

  private

  def client_date
    Time.zone.now.strftime('%m/%d/%Y')
  end

  def client_time
    Time.zone.now.strftime('%I:%M %p')
  end

  def request_uuid
    SecureRandom.uuid
  end

  def reams_configuration
    @reams_configuration ||= REAMS::Configuration.sole
  end

  delegate :client_app_name,
           :username,
           :password,
           :vendor_name, to: :reams_configuration
end
