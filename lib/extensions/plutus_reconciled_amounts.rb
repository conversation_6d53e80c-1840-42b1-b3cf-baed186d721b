module Extensions::PlutusReconciledAmounts
  extend ActiveSupport::Concern

  included do
    validate :prevent_reconciled_changes

    before_destroy :prevent_reconciled_removal
  end

  def reconciled?
    reconciliation&.submitted?
  end

  def reconcile!(reconciliation)
    update!(reconciliation: reconciliation)
  end

  def unreconcile!
    update!(reconciliation: nil)
  end

  private

  def reconciliation_date_string
    reconciliation.statement_date.to_fs(:short_date)
  end

  def add_reconciled_error(model)
    model.errors.add(
      :base,
      "Cannot modify reconciled amounts (#{reconciliation_date_string})."
    )
  end

  def prevent_reconciled_changes
    return unless reconciled?

    return unless amount_changed? || account_id_changed?

    document = entry.commercial_document

    add_reconciled_error(self)
    add_reconciled_error(document) if document
  end

  def prevent_reconciled_removal
    return unless reconciled?

    document = entry.commercial_document

    add_reconciled_error(self)
    add_reconciled_error(document) if document

    throw(:abort)
  end
end
