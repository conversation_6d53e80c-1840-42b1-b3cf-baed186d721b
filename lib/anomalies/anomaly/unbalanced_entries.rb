class Anomalies::Anomaly::UnbalancedEntries
  def title
    'Unbalanced Entries'
  end

  def applicable?
    true
  end

  def header
    %w[ID Created Date Description URL]
  end

  def rows
    entries.map do |entry|
      [
        entry.id,
        entry.created_at,
        entry.date,
        entry.description,
        "https://#{subdomain}.revela.co/accounting/journals/#{entry.journal_id}/entries/#{entry.id}"
      ]
    end
  end

  private

  def entries
    Plutus::Entry
      .joins(:amounts)
      .group('plutus_entries.id', 'plutus_amounts.entry_id')
      .having("
        SUM(
          CASE plutus_amounts.type
          WHEN 'Plutus::CreditAmount' THEN -plutus_amounts.amount
          WHEN 'Plutus::DebitAmount' THEN plutus_amounts.amount
          END
        ) <> 0
      ")
  end

  def subdomain
    Customer.current_subdomain
  end
end
