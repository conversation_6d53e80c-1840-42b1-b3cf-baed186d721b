class Anomalies::Anomaly::LeaselessPayments <
  Anomalies::Anomaly::DepositlessPayments
  def title
    'Leaseless Payments'
  end

  def applicable?
    !Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox])
  end

  private

  def payments
    Payment
      .where(payer_type: 'Tenant', payer_lease_membership: nil)
      .where.not(payee: Customer.current.client_entity)
      .order(date: :desc)
      .preload(:payer, :payee)
  end
end
