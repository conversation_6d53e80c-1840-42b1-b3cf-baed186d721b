class CustomForms::TemplateNameToClass
  TEMPLATE_NAME_TO_KLASS = {
    'blank_v1' => 'CustomForms::Template::Blank::V1',
    'event_registration_v1' => 'CustomForms::Template::EventRegistration::V1',
    'event_registration_with_payment_v1' => 'CustomForms::Template::EventRegistrationWithPayment::V1'
  }.freeze

  AUTOMATION_NAMES = {
    'event_registration_v1' => ['event_v1'],
    'event_registration_with_payment_v1' => ['payment_v1', 'event_v1'],
    'blank_v1' => []
  }.freeze

  class << self
    def convert(template_name)
      template_klass = TEMPLATE_NAME_TO_KLASS[template_name]
      CustomForms.const_get(template_klass)
    end

    def automation_names_for(template_name)
      AUTOMATION_NAMES[template_name] || []
    end
  end
end
