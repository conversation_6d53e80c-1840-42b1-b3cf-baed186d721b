module CustomForms::Template
  AVAILABLE_TEMPLATES = [
    ['Blank Form', 'blank_v1'],
    ['Event Registration', 'event_registration_v1'],
    ['Event Registration with Payment', 'event_registration_with_payment_v1']
  ].freeze

  def create_from_template(data, params = {})
    automations = CustomForms::TemplateNameToClass.automation_names_for(template_name)
    verify_automation_keys_exist(data, automations)
    fields = build_data_fields(data)

    form = CustomForms::Form.create(
      name: form_name,
      template_name: template_name,
      start_date: Time.zone.today,
      form_fields: fields
    )

    return form unless form.valid?

    create_automation_settings(automations, form, params)

    form
  end

  def build_data_fields(data)
    data.each_with_index.flat_map do |row, row_index|
      row.each_with_index.flat_map do |metadata, col_index|
        element = build_element(**metadata)
        build_field(**metadata, element: element, row: row_index,
                                column: col_index)
      end
    end
  end

  def verify_automation_keys_exist(data, automations)
    template_keys = data.flatten.pluck(:key).compact
    automations.each do |automation_name|
      automation_keys = CustomForms::AutomationNameToClass.keys_for(automation_name)
      missing_keys = automation_keys - template_keys
      unless missing_keys.empty?
        fail "Automation keys #{missing_keys.join(', ')} do not exist in template data."
      end
    end
  end

  def create_automation_settings(automations, form, params)
    automations.each do |automation_name|
      automation = CustomForms::AutomationNameToClass.convert(automation_name)
      automation.create_automation_setting(form, params)
    end
  end

  def build_field(element:, row:, column:, key: nil, **rest)
    CustomForms::FormField.new(
      element: element,
      key: key,
      row: row,
      order: column,
      removable: false
    )
  end

  def build_element(type:, **rest)
    element_class = "CustomForms::Element::#{type.to_s.camelize}".constantize
    element_class.new(
      **rest.slice(*element_class::PERMITTED_KEYS)
    )
  end
end
