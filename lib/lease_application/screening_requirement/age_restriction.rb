class LeaseApplication::ScreeningRequirement::AgeRestriction <
  LeaseApplication::ScreeningRequirement
  attr_reader :background_check

  def initialize(lease_application, background_check)
    super(lease_application)
    @background_check = background_check
  end

  def name
    'Age Restriction'
  end

  def configured
    age_restriction
  end

  def meets_requirement?
    return :yes if age_restriction.nil?
    return :maybe if age.nil?

    if age >= age_restriction
      :yes
    else
      :no
    end
  end

  delegate :age, to: :background_check
  delegate :age_restriction, to: :configuration

  alias result age
end
