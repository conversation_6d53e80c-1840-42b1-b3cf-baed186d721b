##
# Used by +griddler+ to field incoming http email posts from mailgun. Should
# eventually dispatch to specific handlers when we have more inbound email
# functionality. Sends a receipt after successfully saving the
# +Invoice Processing Email+.
class EmailProcessor
  ALLOWED_INCOMING_EMAIL_DOMAINS = %w[invoices.revela.co].freeze

  attr_reader :email, :customer

  def initialize(email)
    @email = email
    @customer = find_customer
  end

  def process
    if customer.present?
      customer.activate!
      invoice_processing_email = parse_invoice_processing_email

      InvoiceProcessingMailer.receipt(invoice_processing_email).deliver_later

      begin
        InvoiceProcessing::Email::Parse.call(invoice_processing_email)
      rescue StandardError => e
        Rails.logger.warn e
      end
    else
      recipient = email.from[:email]
      destination = email.to.first[:email]
      InvoiceProcessingMailer.unknown_customer(recipient, destination)
                             .deliver_later
    end
  end

  def parse_invoice_processing_email
    attributes = {
      to: combined_to,
      from: email.from[:email],
      body: email_body,
      subject: email.subject
    }

    InvoiceProcessing::Email.new(attributes).tap do |invoice_processing_email|
      invoice_processing_email.customer = Customer.current

      assigner = InvoiceProcessing::Assigner.new(invoice_processing_email)
      assigner.assign

      email.attachments.each do |attachment|
        invoice_processing_email.attachments.build(upload: attachment)
      end

      invoice_processing_email.save!
    end
  rescue ActiveRecord::RecordInvalid => e
    [e.record, e.record.attachments].flatten.each do |record|
      Rails.logger.error record.errors.full_messages
    end

    raise
  end

  private

  def recipient_emails
    Array(email.to) + Array(email.cc) + Array(email.bcc)
  end

  def find_customer
    recipient_emails.detect do |to|
      parsed_address = Email::AddressParser.new to[:email]

      next nil unless parsed_address.valid?
      next nil unless ALLOWED_INCOMING_EMAIL_DOMAINS.include?(parsed_address.domain)

      break Customer.find_by(subdomain: parsed_address.username)
    end
  end

  def combined_to
    recipient_emails.pluck(:email).join(', ')
  end

  def email_body
    decoded_body(email.raw_html, 'html') || decoded_body(email.raw_body, 'text')
  end

  def decoded_body(raw, type)
    return nil unless raw

    # raw.force_encoding(email.charsets[type]).encode('UTF-8')

    raw
  rescue EncodingError => e
    Rails.logger.warn(e.message)
    raw
  end
end
