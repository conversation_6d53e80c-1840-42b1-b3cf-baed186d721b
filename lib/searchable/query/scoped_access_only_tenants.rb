# This generator will only check property manager scope on the Tenants model.
class Searchable::Query::ScopedAccessOnlyTenants < Searchable::Query::Generator
  def self.generate(index_boosts:, # rubocop:disable Metrics/MethodLength
                    escaped:,
                    phrase_slop:,
                    default_operator:,
                    scope_to:)
    {
      query: Searchable::Query.with_index_boost(index_boosts:) do # rubocop:disable Metrics/BlockLength
        {
          bool: {
            must: {
              query_string: Searchable::Query.query_string(escaped:, phrase_slop:,
                                                           default_operator:)
            },
            filter: {
              bool: {
                must: [
                  {
                    term: {
                      tenant: Customer.current_subdomain.underscore
                    }
                  },
                  {
                    bool: {
                      should: [
                        *Searchable::Query.scoped_access_filters(scope_to),
                        *tenant_klass_filter(scope_to)
                      ]
                    }
                  }
                ]
              }
            }
          }
        }
      end,
      highlight: { fields: { '*': {} } }
    }
  end

  def self.tenant_klass_filter(scope_to)
    return unless Feature.enabled?(:filter_search_by_scope, Customer.current) && scope_to.present?
    return if scope_to.top_level?

    [{
      bool: {
        must_not: [
          {
            term: {
              _index: Tenant.index_name
            }
          }
        ]
      }
    }]
  end
end
