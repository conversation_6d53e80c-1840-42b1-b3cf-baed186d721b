##
# Extends `audited`'s Audit.
#
# Notable adds:
# - audited_change_references: a JSON blob of `_id` field to hash of `name` and
#   `gid` fields, e.g.
#   {
#     'payable_account_id' => [
#       { note: old value here
#         'name' => 'HVAC Maintenance',
#         'gid' => 'gid://revela/Plutus::Expense/9001'
#       },
#       { note: new value here, mimicking how audited stores changes
#         'name' => 'HVAC Maintenance',
#         'gid' => 'gid://revela/Plutus::Expense/9001'
#       }
#     ]
#   }
class Audited::CustomAudit < Audited::Audit
  before_create :set_username
  before_create :add_names

  def description
    return action.titleize unless action == 'update'

    [].tap do |arr|
      if audited_changes.length > 1
        audited_changes.each do |change|
          arr.push multi_print(change)
        end
      else
        arr.push print_change(audited_changes.first)
      end
    end.join("\n")
  end

  def user_url
    user&.url unless user.is_a?(AdminUser)
  end

  def resource_url
    if associated.is_a?(Lease)
      routes.leasing_lease_path(associated)
    elsif auditable.is_a?(Lease)
      routes.leasing_lease_path(auditable)
    end
  end

  def auditable_type_name
    type_name(auditable_type)
  end

  def associated_type_name
    type_name(associated_type)
  end

  private

  def type_name(klass_name)
    klass_name = case klass_name
                 when /^Plutus::/ then klass_name.gsub('Plutus::', 'Journal ')
                 when /^Lease::/ then klass_name.gsub('Lease::', '')
                 else klass_name
                 end

    klass_name.titleize.downcase
  end

  def routes
    Rails.application.routes.url_helpers
  end

  def multi_print(change)
    [
      change.first.humanize.capitalize,
      'was changed from',
      change.second.first.to_s.humanize.downcase,
      'to',
      change.second.second.to_s.humanize.downcase
    ].join(' ')
  end

  def print_change(change)
    [
      change.first.humanize.capitalize,
      'in',
      auditable_type,
      auditable_id,
      'from',
      change.second.first.to_s.humanize.downcase,
      'to',
      change.second.second.to_s.humanize.downcase
    ].join(' ')
  end

  def set_username
    val = if auditable.respond_to?(:auditable_name)
            auditable.auditable_name
          elsif auditable.respond_to?(:name)
            auditable.name
          else
            auditable.to_s
          end
    self.reference_name = val
  end

  def get_from_changes(key)
    tmp = if audited_changes.key? key
            audited_changes[key]
          elsif action == 'create'
            auditable.send(key)
          else
            [auditable.send(key), auditable.send(key)]
          end

    tmp.is_a?(Array) ? tmp : [tmp]
  end

  def add_names
    return unless %w[create update].include?(action)

    return if audited_changes.blank?

    return unless defined?(audited_change_references)

    assocs = audited_changes.select { |k, _| k.match?(/_(id|type)$/) }
    already_done = []

    assocs.each do |key_id, _|
      key = key_id.gsub(/_(id|type)$/, '')

      next if already_done.include? key

      next unless auditable._reflections.key? key

      id_key, type_key = %w[_id _type].map { |p| "#{key}#{p}" }
      ref = auditable._reflections[key]

      klasses = if ref.polymorphic?
                  get_from_changes(type_key).map { |t| t&.safe_constantize }
                else
                  [ref.klass, ref.klass]
                end

      value_ids = get_from_changes(id_key)
      rows = value_ids.map.with_index do |vid, idx|
        vid.blank? ? vid : klasses[idx].find_by(id: vid)
      end

      rows = rows.map do |obj|
        if obj.present?
          { 'name' => obj.auditable_name, 'gid' => obj.to_gid.to_s }
        end
      end

      rows = rows[0] if action == 'create'
      self.audited_change_references ||= {}
      self.audited_change_references[id_key] = rows
      already_done << key
    end
  end
end
