class ActionsMenu::ChartOfAccounts < ActionsMenu::Base
  attr_reader :chart

  def initialize(chart, user)
    super
    @chart = chart
  end

  def items
    [add_account, edit, export, remove]
  end

  private

  def add_account
    {
      text: 'Add Account',
      icon: 'plus',
      link: new_organization_chart_of_accounts_account_path(chart),
      permission: can.add_accounts_to_organization_charts_of_accounts?
    }
  end

  def edit
    {
      text: 'Edit',
      icon: 'edit',
      link: edit_organization_chart_of_accounts_path(chart),
      permission: can.update_organization_charts_of_accounts?
    }
  end

  def export
    {
      text: 'Export',
      icon: 'excel file outline icon',
      link: organization_chart_of_accounts_path(chart, format: :xlsx),
      permission: can.export_organization_charts_of_accounts?
    }
  end

  def remove
    {
      text: 'Remove',
      icon: 'remove',
      link: organization_chart_of_accounts_path(chart),
      disabled: chart.journals.any?,
      permission: can.destroy_organization_charts_of_accounts?,
      options: {
        method: :delete,
        data: { confirm: 'Are you sure?' }
      }
    }
  end
end
