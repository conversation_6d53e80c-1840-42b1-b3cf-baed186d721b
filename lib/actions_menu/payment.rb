class ActionsMenu::Payment < ActionsMenu::Base
  attr_reader :payment

  def initialize(payment, user)
    @payment = payment
    super(payment, user)
  end

  def items
    [edit, print_check, apply, unapply, refund, void, delete]
  end

  private

  def edit
    {
      text: 'Edit',
      link: edit_accounting_payment_path(payment),
      icon: 'edit',
      disabled: house_director?,
      permission: user.can.update_accounting_payments?
    }
  end

  def print_check
    return unless payment.check_printable?

    {
      text: 'Print Check',
      link: check_accounting_payment_path(payment),
      icon: 'print',
      disabled: house_director?,
      permission: user.can.print_check_for_accounting_payments?
    }
  end

  def apply
    disabled = payment.credit.zero? || house_director?

    {
      text: 'Apply',
      icon: 'linkify',
      link: '#',
      options: { id: 'apply-payment-link' },
      disabled: disabled,
      permission: user.can.apply_and_unapply_accounting_payments?
    }
  end

  def unapply
    {
      text: 'Unapply',
      icon: 'unlink',
      link: accounting_payment_apply_path(payment),
      disabled: payment.invoice_payments.none? || house_director?,
      options: {
        data: { confirm: 'Unapply payment?', method: :delete }
      },
      permission: user.can.apply_and_unapply_accounting_payments?
    }
  end

  def refund
    return unless Payment::Refundable.refundable?(payment)

    {
      text: 'Refund',
      icon: 'undo',
      link: refund_accounting_payment_path(payment),
      options: {
        data: { confirm: 'Are you sure?' }
      },
      disabled: house_director?,
      permission: user.can.refund_accounting_payments?
    }
  end

  def void
    return unless Payment::Voidable.voidable?(payment)

    {
      text: 'Void',
      icon: 'remove',
      link: void_accounting_payment_path(payment),
      options: {
        data: { confirm: 'Are you sure?' }
      },
      disabled: house_director?,
      permission: user.can.void_accounting_payments?
    }
  end

  def delete
    {
      text: 'Delete',
      icon: 'trash',
      link: accounting_payment_path(payment),
      options: {
        data: { confirm: 'Are you sure?' },
        method: :delete
      },
      disabled: house_director?,
      permission: user.can.destroy_accounting_payments?
    }
  end
end
