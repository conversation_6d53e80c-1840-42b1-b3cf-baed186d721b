class ActionsMenu::Employee < ActionsMenu::Base
  include ActionsMenu::Archiving

  attr_reader :employee, :user

  alias resource employee

  def initialize(employee, user:)
    @employee = employee
    @user = user
  end

  def items
    [edit, password_reset, archive, unarchive]
  end

  private

  def edit
    return nil if employee.archived?

    return nil unless administrator?

    {
      text: 'Edit',
      icon: 'edit',
      link: edit_organization_employee_path(employee),
      permission: user.can.update_organization_employees?
    }
  end

  def password_reset
    return nil if employee.archived?

    return nil unless administrator?

    {
      text: 'Reset Password',
      icon: 'key',
      link: send_password_reset_organization_employee_path(employee),
      options: {
        data: {
          confirm: "Send Password Reset Email to #{employee.email}?"
        }
      },
      permission: user.can.reset_password_for_organization_employees?
    }
  end

  def resource_path
    [:organization, :employee, id: employee.id]
  end

  def can_archive?
    administrator? && employee != user
  end

  def archive_permission?
    user.can.archive_organization_employees?
  end

  def administrator?
    user.role.administrator?
  end
end
