class ActionsMenu::Collections::Eviction < ActionsMenu::Base
  attr_reader :eviction

  def initialize(eviction)
    @eviction = eviction
  end

  def items
    [packet, edit, close, reopen]
  end

  private

  def packet
    {
      text: 'Packet',
      icon: 'download',
      link: operations_collections_evicting_packet_path(eviction),
      options: { data: { background_download: true } }
    }
  end

  def edit
    {
      text: 'Edit',
      icon: 'edit',
      modal: :edit_eviction_modal
    }
  end

  def close
    return nil unless eviction.open?

    {
      text: 'Close',
      icon: 'times',
      modal: :close_eviction_modal
    }
  end

  def reopen
    return nil unless eviction.closed?

    {
      text: 'Reopen',
      icon: 'undo alternate',
      link: reopen_operations_collections_eviction_path(eviction),
      options: { data: { confirm: 'Reopen eviction?' } }
    }
  end
end
