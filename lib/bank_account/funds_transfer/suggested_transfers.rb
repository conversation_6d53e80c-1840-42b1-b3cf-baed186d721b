class BankAccount::FundsTransfer::SuggestedTransfers
  include BankAccount::FundsTransfer::SuggestedTransfers::Exportable

  def initialize(funds_transfer:)
    @funds_transfer = funds_transfer
  end

  def transfers
    items.group_by(&:source_bank_account).map do |bank_account, items|
      BankAccount::FundsTransfer::SuggestedTransfer.new(
        destination_bank_account: funds_transfer.bank_account,
        source_bank_account: bank_account,
        items: items.sort_by(&:name)
      )
    end
  end

  private

  attr_reader :funds_transfer

  def source_klasses
    [
      BankAccount::FundsTransfer::Sources::DirectPayments,
      BankAccount::FundsTransfer::Sources::Manual,
      BankAccount::FundsTransfer::Sources::MerchantProcessingRevenue,
      BankAccount::FundsTransfer::Sources::Passthrough
    ]
  end

  def sources
    source_klasses.map do |klass|
      klass.new(funds_transfer: funds_transfer)
    end
  end

  def items
    sources.flat_map(&:items)
  end
end
