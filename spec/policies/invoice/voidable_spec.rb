require 'rails_helper'

RSpec.describe Invoice::Voidable do
  describe '#voidable?' do
    let(:result) { described_class.new(invoice) }

    let(:invoice) do
      create(:rent_invoice)
    end

    it 'is not voidable once paid' do
      create(:invoice_payment, invoice: invoice)
      invoice.reload
      expect(result).not_to be_voidable
    end

    it 'is not voidable once waived' do
      Invoice::Waive.call(invoice)
      expect(result).not_to be_voidable
    end

    it 'is voidable for open invoices without payments' do
      expect(result).to be_voidable
    end
  end
end
