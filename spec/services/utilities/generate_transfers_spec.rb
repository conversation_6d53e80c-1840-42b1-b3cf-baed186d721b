require 'rails_helper'

RSpec.describe Utilities::GenerateTransfers, type: :service do
  subject(:result) do
    described_class.call(tenant: tenant, service_location: property)
  end

  let!(:utility) { create(:vendor, kind: :utility) }
  let!(:tenant) { create(:tenant) }
  let(:responsibility) { nil }

  let!(:property) do
    create(:property,
           water_utility: utility,
           water_utility_responsibility: responsibility)
  end

  it { is_expected.to be_successful }

  describe 'a utility transfer' do
    subject(:transfer) { result.utility_transfers.first }

    context 'with no responsibility' do
      it { is_expected.to be_nil }
    end

    context 'with owner responsibility' do
      let(:responsibility) { :water_utility_owner_responsible }

      it { is_expected.to be_nil }
    end

    context 'with tenant responsibility' do
      let(:responsibility) { :water_utility_tenant_responsible }

      its(:tenant) { is_expected.to eq(tenant) }

      its(:utility) { is_expected.to eq(utility) }

      its(:service_location) { is_expected.to eq(property) }

      its(:kind) { is_expected.to eq('water') }
    end
  end
end
