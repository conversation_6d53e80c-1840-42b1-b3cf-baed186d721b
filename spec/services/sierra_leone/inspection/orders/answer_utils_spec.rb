require 'rails_helper'

RSpec.describe SierraLeone::Inspection::Orders::AnswerUtils do
  let(:dummy_class) do
    Class.new do
      include SierraLeone::Inspection::Orders::AnswerUtils
    end
  end
  let(:instance) { dummy_class.new }

  describe '#coerce_sl_response' do
    let(:question) { double('Question') }

    context 'when options are visibility options' do
      before do
        allow(question).to receive(:options).and_return(['Visible', 'Not Visible'])
      end

      it 'returns "Visible" when response is true' do
        expect(instance.send(:coerce_sl_response, question: question,
                                                  sl_response: true)).to eq('Visible')
      end

      it 'returns "Not Visible" when response is false' do
        expect(instance.send(:coerce_sl_response, question: question,
                                                  sl_response: false)).to eq('Not Visible')
      end
    end

    context 'when options are yes/no options' do
      before do
        allow(question).to receive(:options).and_return(['Yes', 'No'])
      end

      it 'returns "Yes" when response is true' do
        expect(instance.send(:coerce_sl_response, question: question,
                                                  sl_response: true)).to eq('Yes')
      end

      it 'returns "No" when response is false' do
        expect(instance.send(:coerce_sl_response, question: question,
                                                  sl_response: false)).to eq('No')
      end
    end

    context 'when options are yes/no/na options' do
      before do
        allow(question).to receive(:options).and_return(['Yes', 'No', 'N/A'])
      end

      it 'returns "Yes" when response is true' do
        expect(instance.send(:coerce_sl_response, question: question,
                                                  sl_response: true)).to eq('Yes')
      end

      it 'returns "No" when response is false' do
        expect(instance.send(:coerce_sl_response, question: question,
                                                  sl_response: false)).to eq('No')
      end

      it 'returns "N/A" when response is nil' do
        expect(instance.send(:coerce_sl_response, question: question,
                                                  sl_response: nil)).to eq('N/A')
      end
    end

    context 'when options are not predefined' do
      before do
        allow(question).to receive(:options).and_return(['Other', 'Options'])
      end

      it 'returns stripped response when response is a string' do
        expect(instance.send(:coerce_sl_response, question: question,
                                                  sl_response: ' Some Value ')).to eq('Some Value')
      end

      it 'returns nil when response is nil' do
        expect(instance.send(:coerce_sl_response, question: question, sl_response: nil)).to be_nil
      end
    end
  end

  describe '#coerce_struct' do
    it 'returns the value if it is already an OpenStruct' do
      struct = OpenStruct.new(value: 'test', photo_filenames: ['photo.jpg'])
      expect(instance.send(:coerce_struct, struct)).to eq(struct)
    end

    it 'wraps non-OpenStruct values in an OpenStruct' do
      result = instance.send(:coerce_struct, 'test')
      expect(result).to be_a(OpenStruct)
      expect(result.value).to eq('test')
    end

    it 'wraps nil in an OpenStruct' do
      result = instance.send(:coerce_struct, nil)
      expect(result).to be_a(OpenStruct)
      expect(result.value).to be_nil
    end
  end
end
