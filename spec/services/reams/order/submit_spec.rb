require 'rails_helper'

RSpec.describe REAMS::Order::Submit do
  it 'is successful' do
    allow_any_instance_of(REAMS::Service).to receive(:post).and_return(
      OpenStruct.new(
        body: {
          'ResultMessage' => 'SUCCESS'
        }
      )
    )

    # Silence
    require 'mini_magick'
    MiniMagick.warnings = false
    allow(Shrine).to receive(:deprecation).and_return(true)

    create(:reams_configuration)

    inspection = create(:inspection, status: :in_review, comments: 'General comments')

    record = inspection.records.sole

    template = inspection.template

    question_one = create(:inspection_question, template:, reams_id: 'IsCrawlSpaceFlooded')

    response_one = create(:inspection_response,
                          record:,
                          question: question_one,
                          body: 'Yes',
                          note: 'Basement is flooded')

    question_two = create(:inspection_question, template:, reams_id: 'inspectionOn')

    response_two = create(:inspection_response,
                          record:,
                          question: question_two,
                          body: '2025-02-22',
                          note: 'Inspection date')

    _non_reams_response = create(:inspection_response, record:, body: 'No')

    photo = response_one.photos.create!(upload: File.open('public/images/sticky.jpg'))

    reams_data = JSON.parse(File.read(absolute_fixture('reams/order_data.json')))

    order = create(:reams_order, inspection:, data: reams_data)

    order.pending_submission!

    result = described_class.call(order:)

    expect(result).to be_successful

    order.reload

    expect(order).to be_completed
    expect(order.submitted_photo_ids).to eq([photo.id])
  end
end
