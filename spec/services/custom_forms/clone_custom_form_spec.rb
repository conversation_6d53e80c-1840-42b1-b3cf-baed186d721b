require 'rails_helper'

RSpec.describe CustomForms::CloneCustomForm do
  let!(:custom_form) { CustomForms::Template::EventRegistration::V1.new('name').create! }

  it 'clones the custom form' do
    expect { described_class.new(custom_form: custom_form).call }
      .to change { CustomForms::Form.distinct.count(:id) }.by(1)
  end

  it 'clones the underlying fields' do
    expect { described_class.new(custom_form: custom_form).call }
      .to change {
        CustomForms::FormField.distinct.count(:id)
      }.by(4).and change {
        # Four new elements (different tables, so they can have the same
        # element_id, hence element_type)
        CustomForms::FormField.distinct.pluck(:element_type, :element_id).count
      }.by(4).and change {
        CustomForms::Element::ShortText.distinct.count(:id)
      }.by(2).and change {
        CustomForms::Element::Email.distinct.count(:id)
      }.by(1).and change {
        CustomForms::Element::PhoneNumber.distinct.count(:id)
      }.by(1)
  end

  it 'clones the automations defined for template' do
    expect { described_class.new(custom_form: custom_form).call }
      .to change {
        CustomForms::AutomationSettings::EventDateTime::V1.distinct.count(:form_id)
      }.by(1)
  end

  it 'does not clone automations not defined for template' do
    expect { described_class.new(custom_form: custom_form).call }.not_to change {
      CustomForms::AutomationSettings::Payment::V1.count
    }
  end

  it 'is valid' do
    cloned_form = described_class.new(custom_form: custom_form).call
    expect(cloned_form).to be_successful
  end

  it 'has a new form_token after cloning' do
    cloned_form = described_class.new(custom_form: custom_form).call
    expect(cloned_form.form.token).not_to eq(custom_form.token)
  end

  it 'names the form based on source form name' do
    cloned_form = described_class.new(custom_form: custom_form).call
    expect(cloned_form.form.name).to eq "Copy of #{custom_form.name}"
  end

  context 'with automations' do
    let!(:custom_form) do
      create(:revenue_account)
      create(:merchant_account, :credit_card)
      CustomForms::Template::EventRegistrationWithPayment::V1.new('name').create!
    end

    it 'clones the payment automation' do
      expect { described_class.new(custom_form: custom_form).call }
        .to change {
              CustomForms::AutomationSettings::EventDateTime::V1.count +
                CustomForms::AutomationSettings::Payment::V1.count
            }.by(2)
    end
  end

  context 'with submissions' do
    before do
      create(:custom_forms_submission, form: custom_form)
    end

    it 'does not clone the submissions' do
      expect(custom_form.submissions.count).to eq(1)
      expect(custom_form.submissions_count).to eq(1)

      result = described_class.new(custom_form: custom_form).call
      cloned_form = result.form

      expect(cloned_form.submissions.count).to eq(0)
      expect(cloned_form.submissions_count).to eq(0)
    end
  end
end
