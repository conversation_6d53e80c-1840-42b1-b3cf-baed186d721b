require 'rails_helper'

# rubocop:disable RSpec/MultipleMemoizedHelpers
RSpec.describe Taxes::Nelco::Transmit::Preparers::Payee do
  subject(:preparer) { described_class.for(submission) }

  let(:dummy_form) do
    Class.new do
      def render(parent, *_args, **_kwargs)
        # rubocop:disable Style/SymbolProc
        Nokogiri::XML::Builder.with(parent) do |xml|
          xml.form1099dummy
        end
        # rubocop:enable Style/SymbolProc
      end
    end.new
  end

  let(:submission) do
    create(:taxes_nelco_submission, :with_batch, irs_filing: filing)
  end

  let(:filing) do
    for_tag = payee.is_a?(Vendor) ? :for_vendor : :for_owner
    create(:taxes_irs_filing, for_tag, payee: payee)
  end

  let(:address) do
    build(:address,
          line_one: 'PAYEE ADDRESS LINE ONE',
          line_two: 'PAYEE ADDRESS LINE TWO',
          city: 'PAYEE CITY',
          region: 'CT',
          postal_code: '06232-5439',
          country: 'United States')
  end

  let(:taxpayer_identification) do
    build(:taxpayer_identification, tin: '*********', tin_type: :ssn)
  end

  let(:contact_names) do
    {
      first_name: 'JOSEPH',
      last_name: 'SMITH'
    }
  end
  let(:email) { '<EMAIL>' }
  let(:phone) { '+****************' }

  let(:payee_xml) do
    File.read('spec/fixtures/taxes/nelco/transmit/preparers/payee.xml')
  end

  before do
    allow(submission)
      .to receive(:nelco_recipient_id).and_return(123_000_456)
  end

  describe '#render' do
    context 'when payee is a vendor' do
      let(:payee) do
        create(:vendor,
               name: 'PLANET EXPRESS',
               address: address,
               taxpayer_identification: taxpayer_identification,
               vendor_contacts: [build(:vendor_contact,
                                       phone: phone,
                                       email: email,
                                       **contact_names)])
      end

      it { is_expected.to produce_xml(payee_xml).with(1, dummy_form) }
    end

    context 'when payee is an owner' do
      let(:payee) do
        create(:company,
               name: 'PLANET EXPRESS',
               customer_managed: false,
               ownerships: ownerships,
               taxpayer_identification: taxpayer_identification,
               phone: phone,
               address: address)
      end

      let(:ownerships) do
        [build(:ownership, owner: build(:owner, **contact_names,
                                        email: email), amount: 1)]
      end

      it { expect(preparer.nelco_recipient_id).to eq 123_000_456 }

      it { is_expected.to produce_xml(payee_xml).with(1, dummy_form) }
    end
  end

  describe 'validations' do
    before { preparer.validate }

    let(:payee) do
      create(:vendor,
             address: address,
             taxpayer_identification: taxpayer_identification,
             vendor_contacts: [build(:vendor_contact,
                                     phone: phone,
                                     email: email,
                                     **contact_names)])
    end

    context 'without an address' do
      let(:address) { nil }

      it { is_expected.to have_validation_error_on(:address) }
    end
  end
end
# rubocop:enable RSpec/MultipleMemoizedHelpers
