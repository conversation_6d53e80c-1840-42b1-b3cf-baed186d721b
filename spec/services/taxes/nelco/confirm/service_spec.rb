require 'rails_helper'

RSpec.describe Taxes::Nelco::Confirm::Service do
  subject(:validate!) { service.call.validate! }

  let!(:nelco_account) do
    create(:taxes_nelco_account,
           password: 'Blz1everyday',
           username: 'revelaPropertyManager19')
  end

  let(:service) { described_class.new(batches: batches) }

  context 'when getting an successful response', :vcr do
    let(:batches) do
      create_list(:taxes_nelco_batch, 1, nelco_batch_oid: '398925')
    end

    it { expect { validate! }.not_to raise_error Taxes::Nelco::Transmit::Error }
  end

  context 'when getting an error response', :vcr do
    let(:batches) { [] }

    it { expect { validate! }.to raise_error Taxes::Nelco::Transmit::Error }
  end
end
