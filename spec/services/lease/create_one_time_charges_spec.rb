require 'rails_helper'

RSpec.describe Lease::CreateOneTimeCharges do
  let(:unit) { create(:unit) }
  let(:property) { unit.property }
  let(:lease_membership) { create(:lease_membership) }
  let(:rent_income) { property.company.rent_income_account }
  let(:move_in_fees) do
    create(:revenue_account, tenant: property.company.chart_of_accounts)
  end

  def base_lease
    Lease.create!(
      unit: unit,
      lease_memberships: [lease_membership],
      start_date: 3.weeks.from_now,
      end_date: 1.year.from_now
    )
  end

  def create_charge_entry(lease, name:, amount_cents:, recurring: nil, account: nil)
    entry_attributes = {
      name: name,
      recurring: recurring,
      amount: Money.new(amount_cents),
      account: account
    }

    lease.charge_schedule.entries.create!(entry_attributes) do |charge|
      charge.allocations.build(
        amount: Money.new(amount_cents),
        lease_membership: lease_membership
      )
    end
  end

  describe '#call' do
    context 'with original setup' do
      let(:lease) do
        base_lease.tap do |l|
          create_charge_entry(l, name: 'Monthly Rent', amount_cents: 900_00, account: rent_income,
                                 recurring: true)
          create_charge_entry(l, name: 'Move In Fees', amount_cents: 125_00,
                                 account: move_in_fees, recurring: false)
        end
      end

      before { described_class.call(lease) }

      describe 'invoices for one time charges' do
        subject(:invoice) { lease_membership.tenant.payable_invoices.first }

        its(:description) { is_expected.to eq('Move In Fees') }

        it 'has the correct amount' do
          expect(invoice.amount.format).to eq('$125.00')
        end

        its(:post_date) { is_expected.to eq(Time.zone.today) }

        its(:due_date) { is_expected.to eq(lease.start_date) }

        it 'does not create a monthly rent invoice' do
          expect(Invoice.count).to eq(1)
        end
      end
    end
  end
end
