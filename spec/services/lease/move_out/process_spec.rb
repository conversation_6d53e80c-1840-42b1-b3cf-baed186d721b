require 'rails_helper'

RSpec.describe Lease::MoveOut::Process, type: :service do
  let(:lease) do
    memberships = build_pair(:lease_membership, lease: nil)
    create(:lease, lease_memberships: memberships, kind: :month_to_month)
  end

  let(:memberships) { lease.lease_memberships }

  let(:today) { Time.zone.today.to_date }
  let(:move_out_date) { today - 3.days }
  let(:walk_through_date) { today - 2.days }
  let(:termination_date) { today - 1.day }
  let(:termination_reason) { :force_majeure }
  let(:termination_description) { 'Tornado' }

  let(:expense_account) { create(:expense_account) }

  let(:damage) do
    create(:charge_preset, kind: :damage,
                           amount: '$500.00',
                           account: expense_account,
                           configuration: lease.property.configuration)
  end

  let(:move_out) do
    create(:lease_move_out, lease: lease,
                            move_out_date: move_out_date,
                            walk_through_date: walk_through_date,
                            terminate_lease: true,
                            termination_date: termination_date,
                            termination_reason: termination_reason,
                            termination_description: termination_description,
                            lease_memberships: memberships,
                            charge_preset_ids: [damage.id]).tap do |move_out|
      move_out.memberships.first.create_address!(
        line_one: '1024 Washington Blvd.',
        line_two: 'Suite 301',
        city: 'Detroit',
        postal_code: '48226',
        region: 'Michigan'
      )
    end
  end

  before do
    membership = memberships.first

    # Create a security deposit, which will be partly refunded
    SecurityDepositHelper.create_deposit(membership, charged: '$1250.00', paid: '$1250.00')

    # Simulate a historical refund, which should not impact move out credit clearing
    clearing_account = membership.accounting_context.chart_of_accounts.clearing_account
    create(:journal_entry,
           amount: '$25.00',
           lease_membership: membership,
           credit_account: clearing_account)
  end

  describe '#call' do
    subject { result }

    let!(:result) { described_class.call(move_out: move_out) }

    it { is_expected.to be_successful }

    describe 'the move out' do
      subject { move_out.reload }

      it { is_expected.to be_processed }
    end

    describe 'the lease' do
      subject { move_out.lease.reload }

      it { is_expected.to be_terminated }

      it { is_expected.to be_fixed }

      its(:end_date) { is_expected.to eq(termination_date) }

      it { is_expected.to be_force_majeure }

      its(:termination_description) do
        is_expected.to eq(termination_description)
      end
    end

    describe 'the membership' do
      subject(:membership) { memberships.first.reload }

      it { is_expected.to be_move_out_processed }

      its(:move_out_date) { is_expected.to eq(move_out_date) }

      describe 'the forwarding address' do
        subject(:address) { membership.tenant.forwarding_address }

        its(:line_one) { is_expected.to eq('1024 Washington Blvd.') }

        its(:city_state_zip) { is_expected.to eq('Detroit, Michigan 48226') }
      end

      describe 'the damage invoice' do
        subject(:invoice) { membership.tenant.payable_invoices.second }

        its(:amount) { is_expected.to eq(damage.amount) }

        its(:description) { is_expected.to eq('Move Out Damages') }

        its(:post_date) { is_expected.to eq(move_out_date) }

        describe 'the line item' do
          subject(:line_item) { invoice.line_items.first }

          its(:description) { is_expected.to eq(damage.name) }
        end

        describe 'the accounting entry' do
          subject(:entry) do
            Plutus::Entry.find_by(commercial_document: invoice)
          end

          its(:description) { is_expected.to eq('Move Out Damages') }

          its(:lease_membership_id) { is_expected.to eq(membership.id) }

          its(:property_id) { is_expected.to eq(lease.property.id) }

          describe 'the credit amount' do
            subject(:amount) { entry.credit_amounts.first }

            its(:amount) { is_expected.to eq(damage.amount.cents) }

            its(:account) { is_expected.to eq(expense_account) }
          end
        end
      end

      describe 'the refund payable' do
        subject(:invoice) { membership.tenant.receivable_invoices.last }

        its(:description) { is_expected.to eq('Refund Tenant Credit') }

        # 1250 - 500 = 750 (Importantly, ignoring the historical refund when clearing credit)
        its(:amount) { is_expected.to eq(Monetize.parse('$750.00')) }
      end
    end
  end
end
