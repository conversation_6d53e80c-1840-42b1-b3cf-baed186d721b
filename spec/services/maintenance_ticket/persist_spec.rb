require 'rails_helper'

RSpec.describe MaintenanceTicket::Persist do
  let(:property) { create(:property) }

  let(:user) { create(:property_manager) }

  let(:result) { described_class.call(params, user) }

  let(:photo) do
    create(:attachment, upload_file_name: 'stuff.png',
                        direct_upload_url: 'files.example.com/stuff.png')
  end

  let(:photo_two) do
    create(:attachment, upload_file_name: 'stuff2.png',
                        direct_upload_url: 'files.example.com/stuff2.png')
  end

  let(:valid_params) do
    maint_params = attributes_for(:maintenance_ticket)
    maint_params[:tags] = ['Plumbing']

    ActionController::Parameters.new(
      attachments: [photo.direct_upload_url],
      maintenance_ticket: maint_params,
      regarding: "property-#{property.id}"
    )
  end

  let(:invalid_params) do
    ActionController::Parameters.new(maintenance_ticket: { subject: '' })
  end

  describe 'creating a ticket' do
    context 'with valid attributes' do
      let(:params) { valid_params }

      it 'is successful' do
        expect(result).to be_successful
      end

      it 'persists the ticket' do
        expect(result.ticket).to be_persisted
      end

      it 'assigns the opener' do
        expect(result.ticket.opened_by).to eq(user)
      end

      it 'regards' do
        expect(result.ticket.regarding).to eq property
      end

      it 'adds attachments' do
        expect(result.ticket.photos).to eq([photo])
      end

      it 'adds a schedule'

      it 'adds tags' do
        expect(result.ticket.tags).to eq([Tag.find_by(tag: 'Plumbing')])
      end

      it 'creates notifications' do
        ticket = result.tick
        expect(NotificationJob).to have_been_enqueued.with(ticket)
      end
    end

    context 'with invalid attributes' do
      let(:params) { invalid_params }

      it 'is not successful' do
        expect(result).not_to be_successful
      end

      it 'has errors' do
        expect(result.errors).to be_present
      end
    end
  end

  describe 'updating a ticket' do
    let(:ticket) { create(:maintenance_ticket) }

    context 'with valid attributes' do
      let(:params) { valid_params.merge(id: ticket.id) }

      it 'is successful' do
        expect(result).to be_successful
      end

      it 'updates the ticket' do
        params[:maintenance_ticket][:subject] = 'New Subject'
        expect { result }.to(change { ticket.reload.subject })
      end

      it 'adds photos' do
        res = result
        add_photo_params = ActionController::Parameters.new(
          id: res.ticket.id,
          attachments: [photo_two.direct_upload_url],
          maintenance_ticket: { subject: 'New Subject' }
        )

        described_class.call(add_photo_params, user)
        expect(res.ticket.reload.photos.order(:id)).to eq([photo, photo_two])
      end
    end

    context 'with invalid attributes' do
      let(:params) { invalid_params.merge(id: ticket.id) }

      it 'is not successful' do
        expect(result).not_to be_successful
      end

      it 'does not update the ticket' do
        expect { result }.not_to(change { ticket.reload.subject })
      end

      it 'has errors' do
        expect(result.errors).to be_present
      end
    end
  end
end
