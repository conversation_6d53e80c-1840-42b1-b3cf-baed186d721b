require 'rails_helper'

require_relative 'collections_phone_provisioning_context'

RSpec.describe Collections::Communications::SendSms, :vcr do
  include_context 'collections phone provisioning'

  let!(:communication) { create(:collections_communication, channel: :sms) }

  before { communication.tenant.update!(phone: '+***********') }

  def twilio_messages
    Twilio::REST::Api::V2010::AccountContext::MessageList
  end

  context 'with no previous communication' do
    it 'sends a preamble and a balance message' do
      expect_any_instance_of(twilio_messages).to \
        receive(:create).twice.and_call_original

      result = described_class.call(communication)

      expect(result).to be_successful
    end
  end

  context 'with a previous communication' do
    before do
      create(:collections_communication,
             channel: :sms,
             tenant: communication.tenant)
    end

    it 'sends just a balance message' do
      expect_any_instance_of(twilio_messages).to \
        receive(:create).once.and_call_original

      result = described_class.call(communication)

      expect(result).to be_successful
    end
  end
end
