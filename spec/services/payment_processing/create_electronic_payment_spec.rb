require 'rails_helper'

RSpec.describe PaymentProcessing::CreateElectronicPayment do
  let(:lease_membership) { create(:lease_membership) }
  let(:tenant) { lease_membership.tenant }
  let(:property) { lease_membership.lease.unit.property }

  let(:rent) do
    create(:rent_invoice, seller: property,
                          buyer: tenant,
                          membership: lease_membership,
                          amount: '$335.00')
  end

  let(:late_fee) do
    create(:rent_invoice, seller: property,
                          buyer: tenant,
                          membership: lease_membership,
                          description: 'Late Fee',
                          amount: '$50.00')
  end

  let(:merchant_account) { nil }

  let(:payment_method) { nil }

  def create_payment(amount)
    payment_params = ActionController::Parameters.new(
      payment: {
        amount: amount,
        date: Date.parse('5-9-2016'),
        description: 'Tenant Rent Payment'
      }
    )

    invoices = [rent, late_fee]

    described_class.call(
      params: payment_params,
      payee: lease_membership.property,
      invoices:,
      accounting_context: lease_membership.accounting_context,
      merchant_account:,
      payment_method:
    )
  end

  it 'applies a payment to invoices' do
    expect(create_payment('$385.00')).to be_successful

    expect(rent.reload).to be_paid
    expect(late_fee.reload).to be_paid
  end

  it 'handles partial payment' do
    expect(create_payment('$375.00')).to be_successful

    expect(rent.reload).to be_paid
    expect(late_fee.reload).not_to be_paid
    expect(late_fee.balance.format).to eq('$10.00')
  end

  it 'credits for overpayment' do
    expect(create_payment('$400.00')).to be_successful

    expect(rent.reload).to be_paid
    expect(late_fee.reload).to be_paid
    expect(tenant.balance).to eq(Monetize.parse('-$15.00'))
  end

  context 'with a merchant account and payment method', :vcr do
    let(:merchant_account) { create(:merchant_account, :zeamster, :ach) }

    let(:payment_method) { create(:bank_account, :with_zeamster, owner: tenant) }

    before do
      create(:merchant_account_contact, :zeamster, merchant_account:, account_holder: tenant)
    end

    it 'creates a zeamster transaction' do
      result = create_payment('$3.50')

      payment = result.payment

      transaction = payment.zeamster_transaction

      expect(transaction).to be_present
    end

    context 'when there is a network timeout' do
      before do
        allow_any_instance_of(Zeamster::Transaction::Create).to receive(:post).and_raise(Faraday::TimeoutError)
      end

      it 'does not create a payment' do
        expect { create_payment('$3.50') }.to raise_error(Faraday::TimeoutError)

        expect(Payment.count).to eq(0)
      end

      it 'sends a slack notification' do
        payer_name = tenant.name
        payee_name = property.name

        expected_message = /Timeout in Zeamster::Transaction::Create: alever payment from #{payer_name} to #{payee_name} for \$3\.50/i

        expect do
          expect { create_payment('$3.50') }.to raise_error(Faraday::TimeoutError)
        end.to have_enqueued_job(
          Slack::SendSystemNotificationJob
        ).with(
          message: expected_message,
          channel: '#system'
        )
      end
    end
  end
end
