require 'rails_helper'

RSpec.describe Plutus::Entry::Reverse do
  subject(:result) do
    described_class.call(source) do |entry|
      entry.date = date
    end
  end

  let(:date) { Time.zone.today.next_month.beginning_of_month }

  let!(:source) { create(:journal_entry) }

  describe 'the result' do
    it { is_expected.to be_successful }

    describe 'the entry' do
      subject(:entry) { result.entry }

      its(:description) { is_expected.to eq("Reverse #{source.description}") }

      its(:date) { is_expected.to eq(date) }

      its(:journal) { is_expected.to eq(source.journal) }

      its(:debit_account_ids) { is_expected.to eq(source.credit_account_ids) }

      its(:credit_account_ids) { is_expected.to eq(source.debit_account_ids) }
    end
  end
end
