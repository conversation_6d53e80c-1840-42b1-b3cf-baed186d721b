require 'rails_helper'

RSpec.describe Tunisia::CustomerToken::Create do
  it 'creates a token from a verification token and code', :vcr do
    company = create(:company, :tunisia_customer)
    verification_token = 'a_verification_token'
    verification_code = '000001'

    result = described_class.new(
      company: company,
      verification_token: verification_token,
      verification_code: verification_code
    ).call

    expect(result).to be_successful

    token = result.token

    expect(token).to match(/v2\.public/)
  end
end
