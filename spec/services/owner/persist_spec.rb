require 'rails_helper'

RSpec.describe Owner::Persist do
  let(:valid_params) do
    ActionController::Parameters.new(
      owner: attributes_for(:owner).except(
        :password, :password_confirmation
      )
    )
  end

  let(:invalid_params) do
    ActionController::Parameters.new(owner: { first_name: '' })
  end

  let(:result) { described_class.call(params) }

  describe 'creating a new owner' do
    context 'with valid params' do
      let(:params) { valid_params }

      it 'is successful' do
        expect(result).to be_successful
      end

      it 'persists an owner' do
        expect(result.owner).to be_persisted
      end
    end

    context 'with invalid params' do
      let(:params) { invalid_params }

      it 'is not successful' do
        expect(result).not_to be_successful
      end

      it 'has errors' do
        expect(result.errors).to be_present
      end
    end
  end

  describe 'updating an owner' do
    let(:owner) { create(:owner) }

    context 'with valid params' do
      let(:params) { valid_params.merge(id: owner.id) }

      it 'is successful' do
        expect(result).to be_successful
      end

      it 'updates an owner' do
        expect { result }.to change { owner.reload.name }
      end
    end

    context 'with invalid params' do
      let(:params) { invalid_params.merge(id: owner.id) }

      it 'is not successful' do
        expect(result).not_to be_successful
      end

      it 'has errors' do
        expect(result.errors).to be_present
      end

      it 'does not update the owner' do
        expect { result }.not_to change { owner.reload.name }
      end
    end
  end
end
