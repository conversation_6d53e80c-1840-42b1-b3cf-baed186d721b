require 'rails_helper'

RSpec.describe 'collections sms messages' do
  before { host! 'alever.lvh.me' }

  let(:communication) { create(:collections_communication, channel: :sms) }

  let(:path) do
    status_callback_telephony_collections_communications_sms_message_path(
      communication
    )
  end

  it 'handles sms failures status callbacks' do
    params = { MessageStatus: 'failed' }

    perform_enqueued_jobs { post path, params: params }

    expect(communication.reload).to be_failed
  end
end
