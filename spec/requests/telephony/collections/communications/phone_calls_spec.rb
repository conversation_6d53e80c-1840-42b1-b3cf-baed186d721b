require 'rails_helper'

RSpec.describe 'collections phone calls' do
  before { host! 'alever.lvh.me' }

  let!(:communication) { create(:collections_communication, channel: :phone) }

  it 'responds with twiml' do
    get telephony_collections_communications_phone_call_path(communication)

    expect(response).to be_successful
    expect(response.body).to include('Hello')
  end

  it 'handles an update callback for call failed' do
    params = {
      CallStatus: 'failed'
    }

    path = status_callback_telephony_collections_communications_phone_call_path(
      communication
    )

    perform_enqueued_jobs { post path, params: params }

    expect(response).to be_successful

    expect(communication.reload).to be_failed
  end
end
