require 'swagger_helper'

require_relative 'rswag_context'

RSpec.describe 'api/v2/lease_memberships' do
  include_context 'rswag api v2'

  let!(:lease_membership) do
    create(:lease_membership).tap do |lease_membership|
      api_user.property_memberships.create!(
        target: lease_membership.lease.property
      )
    end
  end

  path '/api/v2/lease_memberships' do
    get('list lease memberships') do
      response(200, 'successful') do
        run_test!
      end
    end
  end

  path '/api/v2/lease_memberships/{id}' do
    parameter name: :id, in: :path, type: :integer, description: 'id'

    get('show lease membership') do
      response(200, 'successful') do
        let(:id) { lease_membership.id }

        run_test!
      end
    end
  end
end
