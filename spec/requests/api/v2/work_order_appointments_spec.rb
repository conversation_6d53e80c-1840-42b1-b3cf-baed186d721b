require 'swagger_helper'

require_relative 'rswag_context'

RSpec.describe 'api/v2/work_order_appointments' do
  include_context 'rswag api v2'

  let!(:appointment) { create(:maintenance_ticket_appointment) }

  before { api_user.property_memberships.create!(target: appointment.maintenance_ticket.property) }

  path '/api/v2/work_order_appointments' do
    get('list work order appointments') do
      response(200, 'successful') do
        run_test!
      end
    end
  end

  path '/api/v2/work_order_appointments/{id}' do
    parameter name: :id, in: :path, type: :integer, description: 'id'

    get('show work order appointments') do
      response(200, 'successful') do
        let(:id) { appointment.id }

        run_test!
      end
    end
  end
end
