require 'swagger_helper'

require_relative 'rswag_context'

RSpec.describe 'api/v2/owners' do
  include_context 'rswag api v2'

  # Create ownership to ensure company available to user exists
  let!(:owner) do
    ownership = create(:ownership)

    api_user.property_memberships.create!(target: ownership.entity)

    ownership.owner
  end

  path '/api/v2/owners' do
    get('list owners') do
      response(200, 'successful') do
        run_test!
      end
    end
  end

  path '/api/v2/owners/{id}' do
    parameter name: :id, in: :path, type: :integer, description: 'id'

    get('show owner') do
      response(200, 'successful') do
        let(:id) { owner.id }

        run_test!
      end
    end
  end
end
