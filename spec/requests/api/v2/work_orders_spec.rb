require 'swagger_helper'

require_relative 'rswag_context'

RSpec.describe 'api/v2/work_orders' do
  include_context 'rswag api v2'

  let!(:work_order) do
    create(:maintenance_ticket).tap do |work_order|
      vendor = create(:vendor)
      work_order.create_assignment!(user: create(:property_manager))
      work_order.vendor_assignments.create!(vendor: create(:vendor))
      api_user.property_memberships.create!(target: work_order.property)
    end
  end

  path '/api/v2/work_orders' do
    get('list work orders') do
      response(200, 'successful') do
        run_test!
      end
    end
  end

  path '/api/v2/work_orders?include=unit,property,assigned_employees,assigned_vendors' do
    get('list work orders with includes') do
      response(200, 'successful', document: false) do
        run_test!
      end
    end
  end

  path '/api/v2/work_orders/{id}' do
    parameter name: :id, in: :path, type: :integer, description: 'id'

    get('show work order') do
      response(200, 'successful') do
        let(:id) { work_order.id }

        run_test!
      end
    end
  end
end
