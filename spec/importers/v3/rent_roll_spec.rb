require 'rails_helper'

require_relative 'shared'

RSpec.describe Importers::V3::RentRoll do
  it_behaves_like 'a v3 importer'

  describe '#import' do
    let(:file) { File.new(fixture_path) }

    context 'with a single family rent roll' do
      let(:fixture_path) { 'spec/fixtures/importing/rent_roll_singlefamily.xlsx' }

      let!(:property) { create(:property, name: '1800 Polly') }

      let!(:unit) { create(:unit, property: property) }

      let(:chart_of_accounts) { property.configuration.chart_of_accounts }

      let!(:revenue) do
        create(:revenue_account, gl_code: 4100, tenant: chart_of_accounts)
      end

      let(:options) { { scope: property.portfolio } }

      let(:result) { described_class.call(file: file, options: options) }

      describe 'the result' do
        subject { result }

        it { is_expected.to be_successful }

        describe 'a lease' do
          subject(:lease) { result.leases.second }

          its(:unit) { is_expected.to eq(unit) }

          its(:start_date) { is_expected.to eq(Date.new(2012, 1, 1)) }

          its(:end_date) { is_expected.to eq(Time.zone.today.end_of_month) }

          it { is_expected.to be_month_to_month }

          its(:amount) { is_expected.to eq(Monetize.parse('$350.00')) }

          describe 'a lease membership' do
            subject(:membership) { lease.lease_memberships.first }

            its(:move_in_date) { is_expected.to eq(Date.new(2012, 1, 1)) }

            its(:name) { is_expected.to eq('Michael Jones') }
          end
        end

        describe 'a fixed lease' do
          subject(:lease) { result.leases.first }

          it { is_expected.to be_fixed }

          its(:end_date) { is_expected.to eq(Date.new(2011, 12, 31)) }
        end
      end
    end
  end
end
