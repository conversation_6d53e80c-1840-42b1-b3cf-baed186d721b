require 'rails_helper'

RSpec.describe 'landlord verification', :js do
  scenario 'a landlord opens a landlord verification email' do
    landlord_verification = create(:landlord_verification)

    perform_enqueued_jobs do
      LandlordVerificationMailer.send_request(
        landlord_verification
      ).deliver_later
    end

    open_email landlord_verification.landlord_information.landlord_email

    current_email.click_link 'Verify Information'

    expect(page).to have_content 'Verification'
  end

  scenario 'a landlord completes landlord verification' do
    landlord_verification = create(:landlord_verification)

    visit landlord_verification_path(landlord_verification)

    click_on 'Next'

    fill_in 'Name', with: (landlord_name = 'Updated Landlord Name')
    fill_in 'Email', with: (landlord_email = '<EMAIL>')
    fill_in 'Phone', with: (landlord_phone = '************')

    click_on 'Next'

    fill_in 'First Name', with: (tenant_first_name = 'Updated First')
    fill_in 'Last Name', with: (tenant_last_name = 'Updated Last')

    # TODO: Address

    click_on 'Next'

    fill_in 'Move In Date', with: (move_in_date = 2.years.ago.to_date)
    fill_in 'Move Out Date', with: (move_out_date = 1.year.ago.to_date)
    fill_in 'Monthly Rent Amount', with: (rent = Monetize.parse('$1,000.00'))
    select_dropdown 'Number Of Occupants', (occupant_count = 2)
    select_dropdown 'Condition at Move Out', 'Good'
    select_dropdown 'Number of Late Rent Payments', (late_rent_count = 5)
    select_dropdown 'Was Notice to Vacate Given?', 'Yes'
    select_dropdown 'Were They Taken to Court?', 'No'

    fill_in 'Were Damages Deducted from the Deposit?', with: (deposit_information = 'Deposit')
    fill_in 'Any Additional Information', with: (additional_information = 'Info')

    click_on 'Finish'

    expect(page).to have_content(/thank you/i)

    landlord_verification.reload

    expect(landlord_verification).to be_submitted

    # Landlord
    expect(landlord_verification.landlord_information.landlord_name).to eq(landlord_name)
    expect(landlord_verification.landlord_information.landlord_email).to eq(landlord_email)
    expect(landlord_verification.landlord_information.landlord_phone).to eq(landlord_phone)

    # Tenant
    expect(landlord_verification.tenant_information.first_name).to eq(tenant_first_name)
    expect(landlord_verification.tenant_information.last_name).to eq(tenant_last_name)

    # Lease
    expect(landlord_verification.lease_information.move_in_date).to eq(move_in_date)
    expect(landlord_verification.lease_information.move_out_date).to eq(move_out_date)
    expect(landlord_verification.lease_information.monthly_rent).to eq(rent)
    expect(landlord_verification.lease_information.late_rent_count).to eq(late_rent_count)
    expect(landlord_verification.lease_information).to be_move_out_condition_good
    expect(landlord_verification.lease_information.number_of_occupants).to eq(occupant_count)
    expect(landlord_verification.lease_information.notice_given).to eq(true)
    expect(landlord_verification.lease_information.taken_to_court).to eq(false)
    expect(landlord_verification.lease_information.return_full_security_deposit).to eq(deposit_information)
    expect(landlord_verification.lease_information.additional_information).to eq(additional_information)
  end
end
