require 'rails_helper'

RSpec.describe 'taskbar activities', :js,
               capybara_login: :property_manager do
  def visit_activities
    visit operations_pulse_path
    find('a', text: 'Activities').click
  end

  scenario 'a user does not have any tasks' do
    visit_activities
    expect(page).to have_content(/no scheduled activities/i)
  end

  describe 'project task activities' do
    scenario 'a user sees an assigned project task' do
      task = create(:task, assigned_members: [manager])

      visit_activities

      expect(page).to have_content(task.name)
      expect(page).to have_content(task.description)

      click_on task.name
      expect(page).to have_current_path(
        operations_project_task_path(task.project, task)
      )
    end
  end
end
