require 'rails_helper'

RSpec.describe 'import portfolio entity beginning balances', :js,
               capybara_login: :property_manager do
  # Accounts
  let!(:rent) do
    create(:revenue_account, tenant: chart, name: 'Rent Income', gl_code: 4000)
  end
  let!(:cash) do
    create(:asset_account, tenant: chart, name: 'Cash', gl_code: 1010)
  end
  let!(:receivable) do
    create(:asset_account, tenant: chart,
                           name: 'Accounts Receivable', gl_code: 1000)
  end

  let(:chart) { create(:chart_of_accounts) }
  let(:configuration) { create(:configuration, chart_of_accounts: chart) }
  let(:portfolio) do
    create(:portfolio, setup: false, configuration: configuration)
  end

  let!(:entity) { create(:company, portfolio: portfolio) }

  let(:fixture) { absolute_fixture('importing/beginning_balances.xlsx') }

  scenario 'a user imports a spreadsheet of beginning entity balances' do
    visit portfolio_setup_path(portfolio)

    within('.vertical.orange.pointing.menu') { click_on 'Entities' }
    click_link entity.name
    within('.ui.secondary.menu') { click_link 'Accounting' }

    attach_file 'Beginning Balances', fixture
    click_on 'Upload'
    expect(page).to have_content(/successful/i)

    expect(Money.new(rent.balance).format).to eq('$100.00')
    expect(Money.new(cash.balance).format).to eq('$40.00')
    expect(Money.new(receivable.balance).format).to eq('$60.00')
  end
end
