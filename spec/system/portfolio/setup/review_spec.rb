require 'rails_helper'

RSpec.describe 'finish portfolio setup', :js,
               capybara_login: :property_manager do
  let!(:portfolio) { create(:portfolio, setup: false) }

  scenario 'a user finishes portfolio setup' do
    visit portfolio_setup_path(portfolio)

    click_on 'Review'

    expect do
      click_on 'Finish'
      expect(page).to have_content(/portfolio setup completed/i)
    end.to change { portfolio.reload.setup? }.to(true)
  end
end
