require 'rails_helper'

RSpec.describe 'seven day demand letter', :js,
               capybara_login: :property_manager do
  let(:tenant) { create(:resident) }
  let(:property) { tenant.current_property }

  scenario 'a user sends a seven day demand letter' do
    allow_any_instance_of(Paperclip::Attachment).to receive(:save).and_return(true)

    visit tenant_path(tenant)

    click_actions

    open_actions_submenu 'Email Document'

    perform_enqueued_jobs do
      click_action_item 'Seven Day Notice'

      sleep(1)

      expect(page).to have_content(/sent to #{tenant.email}/i)
    end

    open_email(tenant.email)
    email_attachment = current_email.attachments.first

    expect(email_attachment.filename).to eq('Seven Day Notice.pdf')

    document = tenant.attachments.last
    expect(document.filename).to eq('Seven_Day_Notice.pdf')

    event = Contact::TimelineEntry.last
    expect(event.author).to eq(manager)
    expect(event.regarding).to eq(tenant)
    expect(event.unit).to eq(tenant.current_unit)
  end
end
