require 'rails_helper'

RSpec.describe 'portfolio properties', :js, capybara_login: :property_manager do
  context 'existing property' do
    let(:unit) { create(:unit_with_leases, lease_count: 1) }
    let(:lease) { unit.leases.first }
    let(:tenant) { unit.current_lease.primary_tenant }
    let(:property) { unit.property }

    let!(:archived_unit) do
      create(:unit_with_leases, property: property, lease_count: 1).tap do |u|
        u.archive!(3.days.ago)
      end
    end

    before { visit property_path(property) }

    scenario 'a user looks at a property units table' do
      expect(page).to have_content(unit.name)
      expect(page).to have_content(unit.price.format)
    end

    scenario 'a user edits a property' do
      click_actions
      click_action_item 'Edit'

      fill_in 'Name', with: 'Luxor'

      fill_in 'Year Built', with: '1994'

      fill_in_address line_one: '3900 South Las Vegas Blvd',
                      line_two: '',
                      city: 'Las Vegas',
                      postal_code: '89119',
                      region: 'Nevada'

      rental_expiration = 1.year.from_now.to_date
      fill_in 'Rental License Expiration Date', with: rental_expiration

      fill_in 'Notes', with: 'This property is so cool'

      check_checkbox 'Preleasing'

      fill_in_additions_dropdown 'Subdivision', with: 'Woodlands'

      click_on 'Update Property'

      expect(page).to have_content(/property updated successfully/i)

      property.reload

      expect(property.reload).to have_attributes(
        name: 'Luxor',
        year_built: 1994,
        subdivision: 'Woodlands',
        notes: 'This property is so cool'
      )
      expect(property.address.full_street_address).to eq(
        '3900 South Las Vegas Blvd, Las Vegas, Nevada, United States'
      )
      expect(property.rental_license_expiration_date).to eq(rental_expiration)
      expect(property).to be_preleasing
    end

    context 'archiving a property' do
      let!(:scheduled_payment) do
        create(:scheduled_payment,
               lease_membership: tenant.lease_memberships.last)
      end

      before do
        click_actions
        click_action_item 'Archive'

        click_button 'Archive'

        expect(page).to have_content(/archived/i)
      end

      it 'archives the property' do
        expect(property.reload).to be_archived
      end

      it 'archives the unit' do
        expect(unit.reload).to be_archived
        expect(unit.self_archived_at).to be_nil
        expect(unit.archived_at_from).to eq property
      end

      it 'leaves archived units alone' do
        expect(archived_unit.reload).to be_archived
        expect(archived_unit.self_archived_at).to eq archived_unit.archived_at
        expect(archived_unit.archived_at_from).to be_nil
      end

      it 'does not archive the lease' do
        expect(lease.reload).not_to be_archived
      end

      it 'removes scheduled payments' do
        expect(ScheduledPayment.where(id: scheduled_payment.id)).to be_none
      end
    end

    context 'a user unarchives a property' do
      before do
        property.archive!

        visit property_path(property)

        click_actions
        click_action_item 'Unarchive'

        expect(page).to have_content(/unarchived/i)
      end

      it 'unarchives the property' do
        expect(property.reload).not_to be_archived
      end

      it 'unarchives the units' do
        expect(unit.reload).not_to be_archived
        expect(unit.self_archived_at).to be_nil
        expect(unit.archived_at_from).to be_nil
      end

      it 'leaves archived units archived' do
        expect(archived_unit.reload).to be_archived
        expect(archived_unit.self_archived_at).to eq archived_unit.archived_at
        expect(archived_unit.archived_at_from).to be_nil
      end
    end
  end
end
