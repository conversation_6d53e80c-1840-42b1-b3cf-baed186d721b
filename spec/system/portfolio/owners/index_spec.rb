require 'rails_helper'

RSpec.describe 'portfolio owners index', :js,
               capybara_login: :property_manager do
  let!(:owner_one) { create(:owner, tags: ['Tag One']) }

  let!(:owner_two) { create(:owner, kind: :prospective) }

  let(:owners) { [owner_one, owner_two] }

  before { visit owners_path }

  describe 'filtering' do
    scenario 'a user filters by name' do
      fill_in 'Search...', with: owner_one.name

      expect(page).to have_no_content(owner_two.name)
      expect(page).to have_content(owner_one.name)
    end

    scenario 'a user filters by email' do
      fill_in 'Search...', with: owner_one.email

      expect(page).to have_no_content(owner_two.name)
      expect(page).to have_content(owner_one.name)
    end

    scenario 'a user filters by kind' do
      filter_index 'Type', with: 'Active'

      expect(page).to have_no_content(owner_two.name)
      expect(page).to have_content(owner_one.name)
    end

    scenario 'a user filters by tag' do
      filter_index 'Tag', with: 'Tag One'

      expect(page).to have_no_content(owner_two.name)
      expect(page).to have_content(owner_one.name)
    end
  end

  describe 'sorting' do
    scenario 'a user sorts by name' do
      sort_index 'Name'

      names = owners.sort_by(&:last_name).map(&:name)

      expect(names.first).to appear_before(names.last)
    end

    scenario 'a user sorts by email' do
      sort_index 'Email'

      emails = owners.map(&:email).sort

      expect(emails.first).to appear_before(emails.last)
    end

    scenario 'a user sorts by phone' do
      sort_index 'Phone'

      phones = owners.map(&:formatted_phone).sort

      expect(phones.first).to appear_before(phones.last)
    end
  end
end
