require 'rails_helper'

RSpec.describe 'email replies', :js,
               capybara_login: :property_manager do
  let!(:email) do
    create(:messaging_email, sender: build(:tenant),
                             deliveries_attributes: [{ recipient: manager }])
  end

  scenario 'a user replys to an email' do
    visit messaging_email_path(email)

    click_on 'Reply'

    fill_in 'Subject', with: (reply_subject = 'The Subject')
    fill_in_rich_text with: (reply_body = 'The Reply')

    perform_enqueued_jobs do
      click_on 'Send'
      expect(page).to have_content(/sent successfully/i)
    end

    reply = email.replies.last!

    expect(page).to have_content(reply.body.to_plain_text)

    expect(reply.thread_id).to eq(email.thread_id)
    expect(reply.sender).to eq(manager)
    expect(reply.recipients.first).to eq(email.sender)
    expect(reply.subject).to eq(reply_subject)
    expect(reply.body.to_plain_text).to eq(reply_body)
  end
end
