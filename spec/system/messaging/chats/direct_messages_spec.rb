require 'rails_helper'

RSpec.describe 'chat direct messages', :js, capybara_login: :property_manager do
  xscenario 'an employee messages a colleague' do
    colleague = create(:property_manager)

    chats_page = Pages::ChatsPage.new
    chats_page.visit
    chat_page = chats_page.click_user(colleague)
    chat_page.send_message('Hello')

    # TODO: action cable doesnt run in poltergeist
    sleep(1)

    message = Message.last
    expect(message.body).to eq('Hello')
  end
end
