require 'rails_helper'

RSpec.describe 'tenant waitlist activity feed', :js,
               capybara_login: :property_manager do
  scenario 'a user sees that a tenant was added and removed from a waitlist' do
    lead = create(:lead)
    entry = create(:waitlist_entry, applicant: lead)
    entry.archive!

    visit leasing_lead_path(lead)

    expect(page).to have_content(/added to the .* waitlist/i)
    expect(page).to have_content(/removed from the .* waitlist/i)
    expect(page).to have_content(entry.floorplan.name)
    expect(page).to have_content(entry.floorplan.property.name)
  end
end
