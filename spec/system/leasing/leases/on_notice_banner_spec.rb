require 'rails_helper'

RSpec.describe 'active eviction banner', :js,
               capybara_login: :property_manager do
  let(:lease) { create(:lease, :notice_given) }

  scenario 'a user sees a banner for on-notice' do
    visit leasing_lease_path(lease)

    expect(page).to have_content(/A notice of non renewal was recorded /i)
  end

  scenario 'a user sees a banner for both eviction and on-notice' do
    create(:collections_eviction, lease: lease)

    visit leasing_lease_path(lease)

    expect(page).to have_content(/A notice of non renewal was recorded /i)
  end
end
