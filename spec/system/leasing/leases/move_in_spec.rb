require 'rails_helper'

RSpec.describe 'lease move ins', :js,
               capybara_login: :property_manager do
  let(:start_date) { Time.zone.today + 1.month }
  let(:end_date) { Time.zone.today + 13.months }

  let(:lease) do
    memberships = build_pair(:lease_membership,
                             move_in_date: start_date,
                             move_out_date: end_date)

    memberships.second.role = :co_tenant

    create(:lease, start_date: start_date,
                   end_date: end_date,
                   lease_memberships: memberships)
  end

  let(:memberships) { lease.lease_memberships }

  scenario 'a user moves in a tenant early' do
    one, two = memberships

    visit leasing_lease_path(lease)

    click_actions
    click_action_item 'Move In'

    toggle_checkbox two.name # Deselect second tenant

    click_on 'Submit'

    expect(page).to have_content(/successful/i)

    expect(one.reload.move_in_date).to eq(Time.zone.today)
    expect(two.reload.move_in_date).to eq(start_date)
  end
end
