require 'rails_helper'

require_relative 'owner_accounting_activity_context'

RSpec.describe 'owner statements', :js,
               capybara_login: :owner do
  include_context 'owner accounting activity'

  scenario 'a user looks at an owner statement for this month' do
    create(:report, name: 'Owner Statement', owner_visible: true)

    # TODO: Remove or configure
    # allow_any_instance_of(Reports::V3::OwnerStatement::PropertySection).to \
    #   receive(:display_class).and_return(Reports::V3::OwnerStatement::PropertyTransactionsTree)

    # TODO: Remove if we can get the report not to insert accounts
    disable_feature_flag(:reports_read_replica)

    visit owners_reports_path
    click_on 'Owner Statement'
    fill_in 'Start Date', with: start_date
    fill_in 'End Date', with: end_date
    page.send_keys :enter, :escape

    chill do
      expect(page).to have_content(company.name)
      expect(page).to have_content('Dates from 01/01/19 to 01/31/19')
    end

    # Entity Cash Summary
    within(find_all('.paper table')[0]) do
      expect(page).to have_content('$1.00')   # Beginning Balance
      expect(page).to have_content('$650.00') # Cash In
      expect(page).to have_content('$285.00') # Cash Out
      expect(page).to have_content('$175.00') # Disbursemens
      expect(page).to have_content('$191.00') # Ending Balance
      expect(page).to have_content('$65.00')  # Unpaid Expenses
      expect(page).to have_content('$126.00') # Net Balance
    end

    # Property Income Detail
    within(find_all('.paper table')[1]) do
      # Rent
      # expect(page).to have_content(rent_income.name)
      expect(page).to have_content(tenant.name)
      expect(page).to have_content(rent_payment.amount.format)
      expect(page).to have_content(rent_payment.description)
      expect(page).to have_content(
        rent_payment.date.strftime(Reports::V3::DATE_FORMAT)
      )

      # Landscaping
      # expect(page).to have_content(landscaping_expense.name)
      expect(page).to have_content(landscaping_payment.amount.format)
      expect(page).to have_content(landscaping_payment.description)
      expect(page).to have_content(
        landscaping_payment.date.strftime(Reports::V3::DATE_FORMAT)
      )
      expect(page).to have_link(
        'Landscaping',
        href: owners_invoice_path(landscaping_invoice)
      )
    end
  end
end
