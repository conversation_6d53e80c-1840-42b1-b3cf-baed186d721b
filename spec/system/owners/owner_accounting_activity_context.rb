#
# Creates sample owner accounting activity for use in specs.
# This creates a beginning balance of $1.00 from 2018,
# then in january additional activity so that during Jan 2019:
#
#  $1.00     Owner Deposit
#  $650.00   Cash In - Rent
#  $285.00   Cash Out - Landscaping
#  $65.00    Unpaid Expense
#  $175.00   Owner Disbursement
#  $191.00   Ending Cash Balance
#  $65.00    Remaining Unpaid Expenses
#  $126.00   Net Balance
#
#  Additionally these transactions are created on a second property:
#  $620.00   Unpaid Rent
#  $129.98   Unpaid Expense
#
#  Additionally, company9 et. al. are for a different owner but same chart
#  of accounts to ensure accounting is done properly.

RSpec.shared_context 'owner accounting activity' do
  let(:start_date) { Date.new(2019, 1, 1) }
  let(:end_date) { Date.new(2019, 1, 31) }

  let!(:management_company) { create(:company) }
  let!(:trust_bank_account) { create(:bank_account, owner: management_company) }

  # stuff for paid invoices
  let!(:company) { create(:company, customer_managed: true) }
  let!(:ownership) { create(:ownership, owner: owner, entity: company) }
  let!(:property) { create(:property, company: company, name: 'AA Place') }
  let!(:unit) { create(:unit, property: property) }
  let!(:membership) { create(:lease_membership, unit: unit) }
  let!(:tenant) { membership.tenant }
  # for testing property transfer:
  let!(:company2) { create(:company, customer_managed: true) }

  let!(:chart) { company.chart_of_accounts }
  let!(:owner_cash_account) { chart.owner_cash_account }
  let!(:due_from_client_entity_account) { chart.due_from_client_entity_account }
  let!(:accounts_receivable) { chart.accounts_receivable }
  let!(:accounts_payable) { chart.accounts_payable }
  let!(:rent_income) { create(:revenue_account, tenant: chart) }
  let!(:landscaping_expense) { create(:expense_account, tenant: chart) }
  let!(:hardware_expense) { create(:expense_account, tenant: chart) }
  let!(:maintenance_income) { create(:revenue_account, tenant: chart) }

  # stuff for other journal company, same chart
  let!(:owner9) { create(:owner) }
  let!(:company9) do
    create(:company, customer_managed: true, chart_of_accounts: chart)
  end
  let!(:ownership9) { create(:ownership, owner: owner9, entity: company9) }
  let!(:property9) { create(:property, company: company9, name: 'K9 Place') }
  let!(:unit9) { create(:unit, property: property9) }
  let!(:membership9) { create(:lease_membership, unit: unit9) }
  let!(:tenant9) { membership.tenant }

  # stuff for open invoices
  let!(:open_company) do
    create(:ownership, owner: owner).entity.tap do |company|
      company.update!(chart_of_accounts: chart)
    end
  end
  let!(:open_property) { create(:property, company: open_company, name: 'ZZ Place') }
  let!(:open_unit) { create(:unit, property: open_property) }
  let!(:open_membership) { create(:lease_membership, unit: open_unit) }
  let!(:open_tenant) { open_membership.tenant }

  # other stuff
  let!(:rent_amount) { Monetize.parse('$650.00') }
  let!(:open_rent_amount) { Monetize.parse('$620.00') }
  let!(:landscaping_amount) { Monetize.parse('$350.00') }
  let!(:landscaping_payment_amount) { Monetize.parse('$285.00') }
  let!(:open_hardware_amount) { Monetize.parse('$129.98') }

  let(:rent_invoice) { @rent_invoice }
  let(:rent_payment) { @rent_payment }
  let(:landscaping_invoice) { @landscaping_invoice }
  let(:landscaping_payment) { @landscaping_payment }

  before do
    # Create a beginning balance entry
    Plutus::Entry.create!(
      kind: :manual_entry,
      date: Date.new(2018, 12, 31),
      description: 'Owner Deposit',
      debits: [{ account: due_from_client_entity_account, amount: 100 }],
      credits: [{ account: owner_cash_account, amount: 100 }],
      **company.accounting_context.entry_params
    )

    # Disbursement entry
    Plutus::Entry.create!(
      kind: :manual_entry,
      date: end_date - 1.day,
      description: 'Owner Disbursement',
      debits: [{ account: owner_cash_account, amount: 175_00 }],
      credits: [{ account: due_from_client_entity_account, amount: 175_00 }],
      **company.accounting_context.entry_params
    )

    # Create rent income
    @rent_invoice = Invoice.create!(
      post_date: start_date,
      due_date: start_date + 5.days,
      description: 'January Rent',
      buyer: tenant,
      buyer_lease_membership: membership,
      seller: property,
      line_items_attributes: [
        {
          unit_price: rent_amount,
          quantity: 1,
          description: 'One Month Rent',
          receivable_account_id: rent_income.id
        }
      ]
    )
    @rent_payment = Payment.create!(
      date: start_date + 8.days,
      credit_bank_account: trust_bank_account,
      description: rent_invoice.description,
      payer: tenant,
      payer_lease_membership: membership,
      payee: property,
      amount: rent_amount
    ) do |payment|
      payment.invoice_payments.build(invoice: rent_invoice, amount: rent_amount)
    end

    # Partial expense
    @landscaping_invoice = Invoice.create!(
      post_date: start_date + 3.days,
      due_date: start_date + 33.days,
      description: 'Landscaping',
      buyer: property,
      seller: management_company,
      line_items_attributes: [
        {
          unit_price: landscaping_amount,
          quantity: 1,
          description: 'Landscaping',
          payable_account_id: landscaping_expense.id,
          receivable_account_id: maintenance_income.id
        }
      ]
    )
    @landscaping_payment = Payment.create!(
      date: start_date + 5.days,
      debit_bank_account: trust_bank_account,
      description: 'Landscaping',
      amount: landscaping_payment_amount,
      payer: property,
      payee: management_company
    ) do |payment|
      payment.invoice_payments.build(
        invoice: landscaping_invoice,
        amount: landscaping_payment_amount
      )
    end

    # transfer property ownership (should be a no-op)
    prop2 = property.amoeba_dup
    prop2.company_id = company2.id
    prop2.save!
    property.archive!(end_date)

    # company9 entry balance
    Plutus::Entry.create!(
      kind: :manual_entry,
      date: Date.new(2018, 12, 31),
      description: 'Owner Deposit',
      debits: [{ account: due_from_client_entity_account, amount: 1 }],
      credits: [{ account: owner_cash_account, amount: 1 }],
      **company9.accounting_context.entry_params
    )

    # company9 rent
    @rent_invoice = Invoice.create!(
      post_date: start_date,
      due_date: start_date + 5.days,
      description: 'January Rent',
      buyer: tenant9,
      buyer_lease_membership: membership9,
      seller: property9,
      line_items_attributes: [
        {
          unit_price: rent_amount,
          quantity: 1,
          description: 'One Month Rent',
          receivable_account_id: rent_income.id
        }
      ]
    )
    @rent_payment = Payment.create!(
      date: start_date + 8.days,
      description: rent_invoice.description,
      payer: tenant9,
      payer_lease_membership: membership9,
      payee: property9,
      amount: rent_amount
    ) do |payment|
      payment.invoice_payments.build(invoice: @rent_invoice, amount: rent_amount)
    end

    # unpaid rent
    @open_rent_invoice = Invoice.create!(
      post_date: start_date,
      due_date: start_date + 5.days,
      description: 'January Rent',
      buyer: open_tenant,
      buyer_lease_membership: open_membership,
      seller: open_property,
      line_items_attributes: [
        {
          unit_price: open_rent_amount,
          quantity: 1,
          description: 'One Month Rent',
          receivable_account_id: rent_income.id
        }
      ]
    )

    # unpaid hardware invoice
    @hardware_invoice = Invoice.create!(
      post_date: start_date + 3.days,
      due_date: start_date + 33.days,
      description: "Hardware from Gemmen's",
      buyer: open_property,
      seller: management_company,
      line_items_attributes: [
        {
          unit_price: open_hardware_amount,
          quantity: 1,
          description: 'Bolts and stuff',
          payable_account_id: hardware_expense.id,
          receivable_account_id: maintenance_income.id
        }
      ]
    )
  end
end
