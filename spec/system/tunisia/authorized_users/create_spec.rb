require 'rails_helper'

RSpec.describe 'tunisia authorized user form',
               :js, capybara_login: :property_manager do
  let(:bank_account) { create(:bank_account, :tunisia) }

  let(:phone) { '**********' }

  scenario 'user pre-fills the form using employee dropdown', :vcr do
    bank_account.owner.update!(tunisia_customer_id: '1497756')

    manager.update!(first_name: '<PERSON>', last_name: '<PERSON><PERSON><PERSON><PERSON>', email: '<EMAIL>')
    manager.confirm

    visit organization_bank_account_path(bank_account)

    click_actions
    click_on 'Add Authorized User'

    expect(page).to have_content(/Add Authorized User/i)

    within_modal do
      fill_in_two_factor with: '000001'

      click_button 'Submit'
    end

    wait_for_modal_to_close

    select_dropdown 'Select Employee', manager.name

    wait_for_ajax

    fill_in 'phone_field', with: phone

    click_on 'Submit'

    expect(page).to have_content('Authorized User Added Successfully')

    visit organization_tunisia_authorized_users_path(bank_account)

    expect(page).to have_content('(*************')
  end
end
