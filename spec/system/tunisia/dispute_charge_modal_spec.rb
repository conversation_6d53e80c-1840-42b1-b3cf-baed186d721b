require 'rails_helper'

RSpec.describe 'tunisia dispute charge',
               :js, capybara_login: :property_manager do
  before { visit organization_bank_account_path(bank_account) }

  context 'with a non unit bank account' do
    let(:bank_account) { create(:bank_account) }

    scenario 'a user does not see the dispute charge action' do
      click_actions

      within_actions_menu do
        expect(page).to have_no_content('Dispute Charge')
      end
    end
  end

  context 'with a unit bank account' do
    let(:bank_account) { create(:bank_account, :tunisia) }

    scenario 'a user opens up dispute charge modal with contact information' do
      click_actions
      click_on 'Dispute Charge'

      within_modal do
        expect(page).to have_content(
          'To dispute a charge from your account activity'
        )
      end
    end
  end
end
