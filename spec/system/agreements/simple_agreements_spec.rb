require 'rails_helper'

RSpec.describe 'simple agreements', :js,
               capybara_login: :property_manager do
  scenario 'a user archives a simple agreement' do
    agreement = create(:simple_agreement, :with_property) # TODO: without property
    create(:payment_plan, lease_membership: nil, simple_agreement: agreement)

    visit leasing_agreement_path(agreement, type: agreement.agreement_type.slug)

    expect do
      click_actions
      accept_confirm { click_action_item 'Archive' }
      expect(page).to have_content(/archived successfully/i)
      expect(agreement.reload).to be_archived
    end.to change { ScheduledPayment.count }.by(-2)

    click_actions
    click_action_item 'Unarchive'
    expect(page).to have_content(/unarchived successfully/i)
    expect(agreement.reload).not_to be_archived
  end
end
