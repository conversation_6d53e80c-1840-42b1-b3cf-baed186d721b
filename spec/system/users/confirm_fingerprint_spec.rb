require 'rails_helper'

RSpec.describe 'user invites', :js do
  let(:fingerprint) { create(:user_login_fingerprint, confirmed_at: nil) }
  let(:vendor_contact) { create(:vendor_contact) }

  context 'when with valid token' do
    before do
      visit users_login_fingerprint_path(fingerprint.token)
    end

    it 'has the fingerprint info' do
      expect(page).to have_content(fingerprint.created_at.to_fs(:human_date))
      expect(page).to have_content(fingerprint.ip_address)
    end

    describe 'confirming' do
      it 'requires the checkbox' do
        click_on('Confirm')
        wait_for_ajax
        expect(page).to have_content(/must be checked to continue/)
        expect(fingerprint.reload.confirmed_at).to be_nil
      end

      it 'succeeds when checkbox is acknowledged' do
        check_checkbox(find('[for$=confirm]').text)
        click_on('Confirm')
        wait_for_ajax
        expect(page).to have_content(/New Login Successfully Confirmed/)
        expect(fingerprint.reload.confirmed_at).not_to be_nil
      end
    end
  end

  context 'when with an invalid token' do
    before do
      visit users_login_fingerprint_path(fingerprint.token + '3')
    end

    it 'does not show fingerprint info' do
      expect(page).to have_no_content(fingerprint.created_at.to_fs(:human_date))
      expect(page).to have_no_content(fingerprint.ip_address)
    end

    it 'shows the invalid token error message and prevents confirming' do
      expect(page).to have_content('Invalid Token')
      click_on('Confirm')
      wait_for_ajax
      expect(page).to have_content('Invalid Token')
      expect(fingerprint.reload.confirmed_at).to be_nil
    end
  end

  context 'when the token is expired' do
    let(:fingerprint) do
      travel_to 15.minutes.ago do
        create(:user_login_fingerprint, confirmed_at: nil)
      end
    end

    before do
      visit users_login_fingerprint_path(fingerprint.token)
    end

    it 'shows the expired token error message and prevents confirming' do
      expect(page).to have_content('Token Expired')
      click_on('Confirm')
      wait_for_ajax
      expect(page).to have_content('Token Expired')
      expect(fingerprint.reload.confirmed_at).to be_nil
    end
  end

  context 'when the token is already confirmed' do
    let(:fingerprint) { create(:user_login_fingerprint) }

    before do
      visit users_login_fingerprint_path(fingerprint.token)
    end

    it 'indicates that the token is already confirmed' do
      expect(page).to have_content('Token Already Confirmed')
    end
  end
end
