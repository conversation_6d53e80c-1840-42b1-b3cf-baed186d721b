require 'rails_helper'

RSpec.describe 'scheduled maintenance tickets', :js,
               capybara_login: :property_manager do
  # TODO: this should be handled from the ticket page itself to make it
  # configurable.
  xscenario 'a user creates a recurring maintenance ticket' do
    resident = create(:resident)
    tagging = create(:tagging)
    tag = tagging.tag
    subject = 'Broken Dishwasher'
    description = 'It does not fill with water'

    visit new_maintenance_ticket_path

    fill_in 'Subject', with: subject
    fill_in 'Description', with: description

    select_dropdown 'Regarding', resident.name
    select_dropdown 'Urgency', 'Minor', wait: false
    select_dropdown 'Tags', tag, wait: false

    click_on 'Recurring'
    fill_in 'Starting on', with: Time.zone.today

    click_on 'Create Ticket'

    ticket = MaintenanceTicket.last
    expect(ticket.regarding).to eq(resident)
    expect(ticket.opened_by).to eq(manager)
    expect(ticket.subject).to eq(subject)
    expect(ticket.description).to eq(description)
    expect(ticket.tags.first).to eq(tag)
    expect(ticket).to be_minor
    expect(ticket.schedule.to_s).to eq('Every 2 weeks')
  end
end
