require 'rails_helper'

RSpec.describe 'request estimate owner approval', :js,
               capybara_login: :property_manager do
  let!(:estimate) { create(:maintenance_estimate) }

  let(:property) { estimate.service_area }

  let!(:owner) { create(:full_ownership, entity: property.company).owner }

  scenario 'a user requests owner approval for an estimate' do
    visit maintenance_estimate_path(estimate)

    click_actions
    click_action_item 'Request Owner Approval'

    expect(page).to have_content(/approval requested/i)

    # Timeline entry
    # TODO: timeline entry

    # Button no longer available
    click_actions
    expect(page).to have_no_content(/request owner approval/i)

    # Request
    request = Approvals::Request.last!
    expect(request).to be_maintenance_estimate
    expect(request.requested_by).to eq(manager)
    expect(request.approver).to eq(owner)
    expect(request.approvable).to eq(estimate)

    # Owner Approvable
    approvables = Approvals::Query.maintenance_estimate_needs_approval_by(owner)
    expect(approvables).to eq([estimate])

    # Owner Notification
    notification = Notification.last!
    expect(notification.user).to eq(owner)
    expect(notification.resource).to eq(estimate)
    expect(notification.description).to \
      eq("Estimate for Inspection of #{property.name}")
    expect(notification.link).to include(Customer.current_subdomain)
    expect(notification.link).to \
      include(owners_approval_request_path(request.token))
  end
end
