require 'rails_helper'

require_relative '../../../shared/owner_work_order_context'

RSpec.describe 'owner work order approval', :js,
               capybara_login: :property_manager do
  include_context 'owner work order approvals'

  let(:trait) { nil }

  before do
    work_order = perform_enqueued_jobs do
      create(*[:maintenance_ticket, trait].compact, property: property)
    end

    visit maintenance_ticket_path(work_order)
  end

  scenario 'an employee sees that owner approval was requested' do
    expect(page).to have_content(/sample owner was sent an approval request/i)
  end

  context 'with an approved work order' do
    let(:trait) { :owner_approved }

    scenario 'an employee sees that the work order was approved' do
      expect(page).to have_content(/sample owner approved this work order/i)

      expect(page).to have_content(/my approval comment/i)
    end
  end

  context 'with a rejected work order' do
    let(:trait) { :owner_rejected }

    scenario 'an employee sees that the work order was rejected' do
      expect(page).to have_content(/sample owner rejected this work order/i)

      expect(page).to have_content(/my rejection comment/i)
    end
  end

  context 'with an expired request' do
    let(:prevent_expiration) { false }

    scenario 'an employee sees that the approval request expired' do
      expect(page).to have_content(/did not respond to the approval request/i)
    end
  end
end
