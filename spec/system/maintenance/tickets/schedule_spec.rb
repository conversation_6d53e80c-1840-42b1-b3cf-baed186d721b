require 'rails_helper'

RSpec.describe 'scheduling work orders', :js,
               capybara_login: :property_manager do
  scenario 'a user schedules a vendor on a work order' do
    work_order = create(:work_order, :assigned)

    visit maintenance_ticket_path(work_order)

    click_actions
    click_action_item 'Schedule'

    scheduled_for = 7.days.from_now.change(sec: 0, usec: 0)

    perform_enqueued_jobs do
      within_modal do
        fill_in 'Date', with: scheduled_for

        select_dropdown 'Appointment Window', '2 Hours'

        click_on 'Submit'
      end

      expect(page).to have_content(/successful/i)
    end

    work_order.reload
    expect(work_order).to be_scheduled

    # Appointment
    appointment = work_order.appointment
    expect(appointment.scheduled_for).to eq(scheduled_for)
    expect(appointment.window).to eq(2.hours)

    # Timeline event
    event = work_order.events.last!
    expect(event.author).to eq(manager)
  end

  context 'with a scheduled work order' do
    let!(:work_order) { create(:work_order, :scheduled) }

    before { visit maintenance_ticket_path(work_order) }

    def within_appointment_segment(&)
      within('.yellow.clearing.segment', &)
    end

    scenario 'a user updates a scheduled appointment' do
      new_date = 7.days.from_now.change(sec: 0, usec: 0)

      within_appointment_segment do
        click_on 'Adjust'
      end

      perform_enqueued_jobs do
        within_modal do
          fill_in 'Date', with: new_date

          click_on 'Submit'
        end

        expect(page).to have_content(/successful/i)
      end

      expect(work_order.appointment.reload.scheduled_for).to eq(new_date)
    end

    scenario 'a user removes a scheduled appointment' do
      perform_enqueued_jobs do
        within_appointment_segment do
          accept_confirm { click_on 'Cancel' }
        end

        expect(page).to have_content(/successful/i)
      end

      expect(work_order.reload).not_to be_scheduled
      expect(work_order.appointment).to be_nil
    end
  end
end
