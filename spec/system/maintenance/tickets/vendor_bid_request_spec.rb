require 'rails_helper'

RSpec.describe 'maintenance ticket vendor bid requests', :js,
               capybara_login: :property_manager do
  scenario 'a user requests a vendor bid' do
    ticket = create(:maintenance_ticket)
    vendor = create(:vendor)
    create(:vendor_contact, vendor: vendor) # Email
    vendor.vendor_contacts.first.update!(email: nil)

    message = 'My Message'

    visit maintenance_ticket_bids_path(ticket)

    within '#bids-index' do
      find_by_id('bid-request-button').click
    end

    expect do
      perform_enqueued_jobs do
        within '#bid-request' do
          select_dropdown 'Vendor', vendor.name

          fill_in 'Message', with: message

          click_on 'Request'

          expect(page).to have_content(/email can't be blank/i)

          vendor.vendor_contacts.first.update!(email: '<EMAIL>')

          click_on 'Request'
        end
        expect(page).to have_content(/bid request sent/i)
      end
    end.to change { ActionMailer::Base.deliveries.count }.by(1)

    # Request
    request = Maintenance::BidRequest.last!
    expect(request.maintenance_ticket).to eq(ticket)
    expect(request.requested_by).to eq(manager)
    expect(request.invites.first.vendor).to eq(vendor)
    expect(request.message).to eq(message)
    expect(request.invites.first).to be_pending

    # Timeline Event
    event = ticket.events.vendor_bid_requested.last!
    expect(event.author).to eq(manager)
    expect(event.vendor_assignee).to eq(vendor)
    expect(event.body).to eq(message)

    within '#bids-index' do
      expect(page).to have_content(vendor.name)
      expect(page).to have_content('Requested')
    end
  end

  context 'with a submitted bid' do
    let!(:bid) { create(:maintenance_bid, amount: '$150.00') }

    before do
      create(:vendor_contact, vendor: bid.vendor) # Email

      visit maintenance_ticket_bids_path(bid.maintenance_ticket)

      click_on 'View'
    end

    scenario 'a user reject a bid' do
      reason = 'Too high'

      perform_enqueued_jobs do
        within_modal do
          click_on 'Reject...'

          fill_in 'Reason', with: reason

          click_on 'Reject'
        end

        expect(page).to have_content(/bid rejected/i)
      end

      bid.reload
      expect(bid).to be_rejected
      expect(bid.rejection.comment).to eq(reason)

      # Ticket Event
      event = bid.maintenance_ticket.events.vendor_bid_rejected.last!
      expect(event.author).to eq(manager)
      expect(event.bid).to eq(bid)
      expect(event.body).to eq(reason)

      expect(page).to have_content('Rejected')

      # Vendor Email
      mail = ActionMailer::Base.deliveries.last
      expect(mail.to).to eq([bid.email])
    end

    scenario 'a user accepts a bid' do
      message = 'Thanks, go ahead'

      perform_enqueued_jobs do
        within_modal do
          click_on 'Approve...'

          fill_in 'Message', with: message

          check_checkbox 'Invite Vendor'

          check_checkbox 'Prepare Estimate'

          click_on 'Approve'
        end

        expect(page).to have_content(/bid approved/i)
      end

      bid.reload
      expect(bid).to be_approved
      expect(bid.approval.comment).to eq(message)

      # Ticket Event
      event = bid.maintenance_ticket.events.vendor_bid_approved.last!
      expect(event.author).to eq(manager)
      expect(event.bid).to eq(bid)
      expect(event.body).to eq(message)

      expect(page).to have_content('Approved')

      # Vendor Invite
      invite = bid.maintenance_ticket.vendor_assignments.last!
      expect(invite.created_by).to eq(manager)
      expect(invite.vendor).to eq(bid.vendor)
      expect(invite.message).to eq(message)

      # Vendor Email
      mail = ActionMailer::Base.deliveries.last
      expect(mail.to).to eq([bid.vendor.email])

      # User should be on the estimate screen
      expect(page).to have_content('Prepare Estimate')
    end
  end
end
