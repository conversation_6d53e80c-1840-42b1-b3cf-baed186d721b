require 'rails_helper'

RSpec.describe 'evictions', :js, capybara_login: :property_manager do
  scenario 'a user moves a lease to evictions' do
    lease = create(:lease)

    visit leasing_lease_path(lease)

    click_actions
    click_action_item(/\AEvict\Z/)

    fill_in 'Date Filed', with: (date_filed = Time.zone.today)
    fill_in 'Amount', with: (amount = '$1,234.56')
    fill_in 'Anticipated Eviction Date',
            with: (anticipated_date = Time.zone.tomorrow)

    click_on 'Submit'

    expect(page).to have_content(/successful/i)

    eviction = lease.active_eviction
    expect(eviction.overdue_balance.format).to eq(amount)
    expect(eviction.date_filed).to eq(date_filed)
    expect(eviction.anticipated_date).to eq(anticipated_date)
  end

  scenario 'a user closes an eviction' do
    eviction = create(:collections_eviction)

    close_date = 3.days.ago.to_date

    visit operations_collections_eviction_path(eviction)

    click_actions
    click_action_item 'Close'

    within_modal do
      fill_in 'Close Date', with: close_date
      select_dropdown 'Outcome', 'Vacated Prior to Eviction'
      click_on 'Save'
    end

    expect(page).to have_content(/successful/i)

    eviction.reload

    expect(eviction).to be_closed
    expect(eviction).to be_outcome_vacated
    expect(eviction.close_date).to eq(close_date)
  end
end
