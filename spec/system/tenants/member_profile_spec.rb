require 'rails_helper'

RSpec.describe 'tenant portal member profile', :js, capybara_login: :tenant do
  let!(:membership) do
    create(:simple_agreement).tap do |agreement|
      agreement.memberships.first.update!(tenant: tenant)
    end
  end

  let(:guardian) { membership.memberships.guardian.first.tenant }

  before do
    disable_feature_flag :onboarding_setup
    tenant.update!(date_of_birth: nil)

    allow(CustomerSpecific::Behavior).to \
      receive(:require_completed_member_profile?).and_return(true)
  end

  context 'when sae' do
    before do
      allow_any_instance_of(Tenants::MemberProfilesController).to \
        receive(:sae_member_profile?).and_return(true)
    end

    scenario 'a member is redirected to complete their member profile' do
      visit tenants_dashboard_path

      expect(page).to have_content(/please complete your member profile/i)
    end

    scenario 'a member completes their profile' do
      visit tenants_member_profile_path

      fill_in 'University Email', with: '<EMAIL>'
      select_dropdown 'Year In School', 'Sophomore'
      fill_in 'Student ID Number', with: (id_number = '987654321')
      fill_in 'Social Security Number', with: (ssn = '123456789')
      fill_in 'Date of Birth', with: '2/22/1994'
      fill_in_address

      within '.guardian-fields' do
        fill_in 'First Name', with: 'Parent'
        fill_in 'Last Name', with: 'Orguardian'
        fill_in 'Email', with: '<EMAIL>'
        fill_in 'Phone', with: '************'
      end

      click_on 'Save'

      expect(page).to have_content(/successful/i)

      tenant.reload

      expect(tenant.date_of_birth).to eq(Date.new(1994, 2, 22))
      expect(tenant.tin).to eq(ssn)
      expect(tenant.tin_type).to eq('ssn')
      expect(tenant.meta(:student_id_number)).to eq(id_number)
      expect(tenant.meta(:completed_member_profile)).to eq(true)
      expect(tenant.agreed_to_sms_at).to be_present

      expect(guardian.name).to eq('Parent Orguardian')
      expect(guardian.email).to eq('<EMAIL>')
      expect(guardian.phone).to eq('+12813308004')
      expect(guardian.meta(:completed_member_profile)).to eq(true)
      expect(guardian.address).to be_present
      expect(guardian.address.id).not_to eq(tenant.address.id)
    end
  end

  context 'when adpi' do
    before do
      allow_any_instance_of(Tenants::MemberProfilesController).to \
        receive(:adpi_member_profile?).and_return(true)
    end

    scenario 'a member completes their profile' do
      date_of_birth = Date.new(1994, 2, 22)
      ssn = '123456789'
      guardian_first_name = 'Sample'
      guardian_last_name = 'Guardian'
      guardian_email = '<EMAIL>'
      guardian_phone = '+12813308004'
      school_address_line_one = '123 Main St'
      school_address_city = 'Houston'
      school_address_region = 'TX'
      school_address_postal_code = '77002'
      forwarding_address_line_one = '123 Main St'
      forwarding_address_city = 'Dallas'
      forwarding_address_region = 'TX'
      forwarding_address_postal_code = '75201'

      visit tenants_member_profile_path

      click_on 'Save'

      [
        'Address',
        'Date of Birth',
        'School address line one',
        'School address city',
        'School address region',
        'School address postal code'
      ].each do |required|
        expect(page).to have_content(/#{required} cannot be blank/i)
      end

      expect(page).to have_content(/social security number must be 9 digits/i)

      fill_in 'Date of Birth', with: date_of_birth
      fill_in 'Social Security Number', with: ssn

      # Guardian
      within '.guardian-fields' do
        fill_in 'First Name', with: guardian_first_name
        fill_in 'Last Name', with: guardian_last_name
        fill_in 'Email', with: guardian_email
        fill_in 'Phone', with: guardian_phone
      end

      # School Address
      within '.school-address-fields' do
        fill_in 'Street Address Line One', with: school_address_line_one
        fill_in 'City', with: school_address_city
        fill_in 'State', with: school_address_region
        fill_in 'Zip Code', with: school_address_postal_code
      end

      # Forwarding Address
      within '.forwarding-address-fields' do
        fill_in 'Street Address Line One', with: forwarding_address_line_one
        fill_in 'City', with: forwarding_address_city
        fill_in 'State', with: forwarding_address_region
        fill_in 'Zip Code', with: forwarding_address_postal_code
      end

      click_on 'Save'

      expect(page).to have_content(/successful/i)

      tenant.reload

      # Forwarding Address
      expect(tenant.forwarding_address).to have_attributes(
        line_one: forwarding_address_line_one,
        line_two: nil,
        city: forwarding_address_city,
        region: forwarding_address_region,
        postal_code: forwarding_address_postal_code
      )

      # Metadata
      expect(tenant.metadata_values).to include(
        'completed_member_profile' => true,
        'school_address_line_one' => school_address_line_one,
        'school_address_city' => school_address_city,
        'school_address_region' => school_address_region,
        'school_address_postal_code' => school_address_postal_code
      )

      # Taxpayer Identification
      expect(tenant.tin).to eq(ssn)

      # Guardian
      expect(guardian).to have_attributes(
        first_name: guardian_first_name,
        last_name: guardian_last_name,
        email: guardian_email,
        phone: guardian_phone
      )
    end
  end
end
