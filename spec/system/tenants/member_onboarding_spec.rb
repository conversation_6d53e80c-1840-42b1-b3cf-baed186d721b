require 'rails_helper'

require_relative '../../shared/electronic_signatures_context'

# rubocop:disable RSpec/MultipleMemoizedHelpers
RSpec.describe 'Configurable Member Onboarding', :js, capybara_login: :tenant do
  include_context 'electronic signatures'
  before { create(:resident) }

  let(:tenant) { create(:tenant) }
  let(:lease_membership) do
    create(:lease_membership,
           tenant: tenant, role: :primary_tenant,
           lease: create(:lease, unit: property.units.first))
  end

  describe 'sending to onboarding' do
    context 'when customer does not require onboarding' do
      before do
        allow(CustomerSpecific::Behavior).to \
          receive(:require_completed_member_profile?).and_return(false)
      end

      it 'just goes to the dashboard' do
        visit tenants_dashboard_path
        expect(page).to have_current_path(tenants_dashboard_path)
      end
    end

    context 'when customer requires onboarding' do
      before do
        allow(CustomerSpecific::Behavior).to \
          receive(:require_completed_member_profile?).and_return(true)
      end

      context 'when feature is not enabled' do
        before { disable_feature_flag(:onboarding_setup) }

        it "just goes to the member info 'paywall'" do
          visit tenants_dashboard_path
          expect(page).to have_current_path(tenants_member_profile_path)
        end

        context 'when member profile been marked completed' do
          before { tenant.meta!('completed_member_profile', true) }

          it 'just goes to the dashboard' do
            visit tenants_dashboard_path
            expect(page).to have_current_path(tenants_dashboard_path)
          end
        end
      end

      context 'when the OnboardingSetup feature is enabled' do
        before { enable_feature_flag(:onboarding_setup) }

        context 'when member profile has been marked completed' do
          before { tenant.meta!('completed_member_profile', true) }

          it 'just goes to the dashboard' do
            visit tenants_dashboard_path
            expect(page).to have_current_path(tenants_dashboard_path)
          end
        end

        context 'when member profile has NOT been marked completed' do
          before do
            tenant.meta!('completed_member_profile', false)
            [Tenants::DashboardController, Tenants::AccountsController].each do |klass|
              allow_any_instance_of(klass).to(receive(:onboarding_wizard)).and_return(wizard) # rubocop:disable RSpec/AnyInstance
            end
          end

          # rubocop:disable RSpec/NestedGroups
          context 'when there is no onboarding' do
            let(:wizard) { nil }

            it 'just goes to the dashboard' do
              visit tenants_dashboard_path
              expect(page).to have_current_path(tenants_dashboard_path)
            end
          end

          context 'when there is an onboarding' do
            let(:wizard) do
              step = instance_double(
                MemberOnboarding::Tenants::Wizard::Steps::Welcome,
                show_path: tenants_account_path
              )

              instance_double(
                MemberOnboarding::Tenants::Wizard,
                current_step: step
              )
            end

            it 'goes to path indicated by the wizard' do
              visit tenants_dashboard_path
              expect(page).to have_current_path(wizard.current_step.show_path)
            end
          end
          # rubocop:enable RSpec/NestedGroups
        end
      end
    end
  end

  describe 'onboarding steps' do
    let(:portfolio) { Portfolio.first }

    let(:information_collection_config) do
      build(:member_onboarding_information_collection,
            nickname: true, address: false, student_id: false, school_year: true,
            collections_information: true,
            drivers_license_number: true,
            additional_questions: [
              { 'name' => 'Name of Pet', 'type' => 'text', 'required' => true }
            ])
    end

    let(:guarantor_config) do
      build(:member_onboarding_guarantor,
            address: true, drivers_license_number: true,
            collections_information: true)
    end

    let(:membership_agreement_config) do
      build(:member_onboarding_membership_agreement, require_member_signature: true)
    end

    let(:risk_release_config) { build(:member_onboarding_risk_release) }

    let!(:simple_agreement_membership) { nil } # rubocop:disable RSpec/LetSetup

    let(:auto_enroll_damage_waiver?) { false }

    before do
      allow(CustomerSpecific::Behavior).to \
        receive(:require_completed_member_profile?).and_return(true)

      if auto_enroll_damage_waiver?
        allow(CustomerSpecific::Behavior).to receive(:auto_enroll_damage_waiver?).and_return(true)
      end
      create(:agreement_type, name: 'Membership', document_template: create(:document))
      enable_feature_flag(:onboarding_setup)
      create(:member_onboarding_assignment, configuration: onboarding, tenant: tenant)
      tenant.update(lead_property_id: property.id, date_of_birth: nil)
      tenant.meta!('completed_member_profile', false)
      # rubocop:disable RSpec/AnyInstance
      allow_any_instance_of(MemberOnboarding::CreateLease).to \
        receive(:generate_document).and_return(true)
      # rubocop:enable RSpec/AnyInstance
      MemberOnboarding::Tenants::Wizard.clear!(cohort, tenant)

      visit tenants_dashboard_path
    end

    let(:lease_agreement_config) do
      build(:member_onboarding_lease_agreement,
            require_member_signature: true, require_guarantor_signature: true)
    end

    let(:rent_preset) do
      create(:charge_preset, configuration: portfolio.configuration, kind: :rent,
                             name: 'Room and Board')
    end
    let(:parking_preset) do
      create(:charge_preset, configuration: portfolio.configuration, kind: :incidental,
                             name: 'Parking')
    end
    let(:membership_preset) do
      create(:charge_preset, configuration: portfolio.configuration, kind: :rent, name: 'Dues')
    end

    let(:charge_config) do
      build(:member_onboarding_charge, charge_memberships:
        [
          build(:member_onboarding_charge_membership,
                kind: :lease, recurring: true, charge_preset: rent_preset,
                start_date: lease_agreement_config.lease_start_date,
                end_date: lease_agreement_config.lease_end_date),
          build(:member_onboarding_charge_membership,
                kind: :lease, charge_preset: parking_preset, recurring: false),
          build(:member_onboarding_charge_membership,
                recurring: false, kind: :membership, charge_preset: membership_preset)
        ])
    end
    let(:property) { portfolio.properties.first }

    let(:property_memberships) do
      [build(:member_onboarding_property_membership, property: portfolio.properties.first)]
    end

    let(:onboarding) do
      create(:member_onboarding_configuration,
             information_collection: information_collection_config,
             guarantor: guarantor_config,
             membership_agreement: membership_agreement_config,
             lease_agreement: lease_agreement_config,
             charge: charge_config,
             risk_release: risk_release_config,
             property_memberships: property_memberships)
    end

    let(:cohort) { MemberOnboarding::Tenants::ConfiguredCohort.new(onboarding, property) }

    describe 'welcome' do
      it 'shows the welcome page' do
        progress
        expect(page).to have_current_path(
          welcome_tenants_member_onboarding_path(onboarding)
        )
      end

      it 'leads to the member information page' do
        progress(:welcome)

        expect(page).to have_content('Member Information')

        expect(page).to have_current_path(
          member_profile_tenants_member_onboarding_path(onboarding)
        )
      end
    end

    describe 'member profile' do
      it 'shows the member information page' do
        progress(:welcome)
        # existing info
        expect(page).to have_field('First Name', disabled: true, with: tenant.first_name)
        expect(page).to have_field('Last Name', disabled: true, with: tenant.last_name)
        expect(page).to have_field('Phone', disabled: false, with: tenant.phone.phony_formatted)
        expect(page).to have_field('Email', disabled: true, with: tenant.email)
        click_next

        # validation
        expect(page).to have_current_path(
          member_profile_tenants_member_onboarding_path(onboarding)
        )
        ['Social Security Number', 'Date of birth', 'School Year', 'SMS',
         'Name of Pet'].each do |message|
          expect_error_message(message)
        end

        # accepts valid profile
        fill_out_member_profile
        click_next

        # goes to next step
        expect(page).to have_current_path(
          guarantor_profile_tenants_member_onboarding_path(onboarding)
        )
      end

      it 'allows updating phone number' do
        progress(:welcome)

        # Verify phone field is editable
        expect(page).to have_field('Phone', disabled: false)

        # Clear and update phone number
        phone_field = find_field('Phone')
        phone_field.set('')
        fill_in 'Phone', with: '(*************'
        fill_out_member_profile
        click_next

        # Progress through remaining steps to trigger submission
        progress(:guarantor_profile, :risk_management_program, :membership_agreement,
                 :completion_summary)

        # Verify the phone was saved
        tenant.reload
        expect(tenant.phone).to eq('+15551234567')
      end

      it 'requires phone number to proceed' do
        progress(:welcome)

        # Clear phone number
        phone_field = find_field('Phone')
        phone_field.set('')
        fill_out_member_profile
        click_next

        # Should show error and stay on same page
        expect(page).to have_current_path(
          member_profile_tenants_member_onboarding_path(onboarding)
        )
        expect_error_message('Phone number is required')
      end
    end

    describe 'guarantor profile' do
      context 'when a previous existing guarantor does not exist' do
        it 'shows the guarantor profile page' do
          progress(:welcome, :member_profile)
          expect(page).to have_content('Guarantor or Guardian Information')

          fill_in 'Street Address Line One', with: '1234 Easy St'
          click_next

          ['First Name', 'Last Name', 'Phone', 'Email', 'Forwarding Address'].each do |message|
            expect_error_message(message)
          end

          fill_out_guarantor_profile
          click_next
          wait_for_ajax

          expect(page).to have_css('h2', text: 'Risk Management Program')
        end
      end

      context 'when a previous existing guarantor exists' do
        let(:previous_guarantor) do
          create(
            :tenant,
            first_name: 'Hermes', last_name: 'Conrad',
            phone: '+17342223333', email: '<EMAIL>',
            taxpayer_identification: build(
              :taxpayer_identification, tin: '*********', tin_type: :ssn),
            forwarding_address: build(
              :address, line_one: '222 Sugarcane St', city: 'Jamaica',
                        region: 'NY', postal_code: '48108'),
            metadata: build(
              :metadata, data: { drivers_license_number: 'NY111111111' })
          )
        end

        before do
          create(:lease_membership,
                 lease: lease_membership.lease,
                 tenant: previous_guarantor,
                 role: :guarantor)
        end

        it 'shows the previous guarantor' do
          progress(:welcome, :member_profile)
          expect(page).to have_content('Guarantor or Guardian Information')

          expect(page).to have_field('Legal First Name', with: 'Hermes')
          expect(page).to have_field('Legal Last Name', with: 'Conrad')
          expect(page).to have_field('Phone', with: '+17342223333')
          expect(page).to have_field('Email', with: '<EMAIL>')
          expect(page).to have_field('Street Address Line One', with: '222 Sugarcane St')
          expect(page).to have_field('City', with: 'Jamaica')
          expect(page).to have_field('State', with: 'NY')
          expect(page).to have_field('Zip Code', with: '48108')
          expect(page).to have_field('Drivers License Number', with: 'NY111111111')
          expect(page).to have_field('Date of Birth', with: '')
          expect(page).to have_field('Social Security Number', placeholder: '***-**-4444')

          click_next
          expect_error_message('Date of Birth')

          fill_in 'Date of Birth', with: '03/22/1967'
          click_next
          expect(page).to have_css('h2', text: 'Risk Management Program')
        end
      end

      describe 'loading found guarantor information' do
        let!(:existing_guardian) do
          create(:tenant, first_name: 'Harold', last_name: 'Zoidberg',
                          email: '<EMAIL>', date_of_birth: '09/22/1968',
                          confirmed_at: Time.zone.now)
        end

        it 'loads the the found guarantor info' do
          progress(:welcome, :member_profile)

          expect(page).to have_field('Legal Last Name', with: '')
          expect(page).to have_field('Date of Birth', with: '')

          fill_in 'Email', with: existing_guardian.email
          fill_in 'Legal First Name', with: 'John' # should prefer this over 'Harold'
          click_next

          # prioritizes updated first name over existing stored first name
          expect(page).to have_field('Legal First Name', with: 'John')
          expect(page).to have_field('Legal Last Name', with: 'Zoidberg')
          expect(page).to have_field('Date of Birth', with: 'September 22, 1968')
        end

        it 'hides the ssn once filled out' do
          progress(:welcome, :member_profile)

          fill_in 'Social Security Number', with: '***********'
          click_next
          expect(page).to have_field('Social Security Number', placeholder: '***-**-6666', with: '')
          saved_tin = MemberOnboarding::Tenants::Wizard
                      .find_or_create(tenant).steps['guarantor_profile']
                      .profile_attributes
                      .dig(:taxpayer_identification_attributes, :tin)
          expect(saved_tin).to eq('***********')
        end

        describe 'preventing assigning people who are invalid valid guarantors' do
          def expect_invalid_guarantor_error(lookup_email)
            progress(:welcome, :member_profile)
            fill_in 'Email', with: lookup_email
            click_next
            expect(page).to have_content('Cannot set member as guarantor')
          end

          it { expect_invalid_guarantor_error(tenant.email) }
          it { expect_invalid_guarantor_error(create(:lease_membership).tenant.email) }
          it {
            expect_invalid_guarantor_error(
              create(:agreements_simple_agreement_membership).tenant.email
            )
          }
        end
      end

      describe 'handling skip guarantor' do
        context 'when guarantor is skippable' do
          let(:guarantor_config) do
            build(:member_onboarding_guarantor,
                  address: true, drivers_license_number: true,
                  collections_information: false, optional: true)
          end

          let(:lease_agreement_config) do
            build(:member_onboarding_lease_agreement,
                  require_member_signature: true, require_guarantor_signature: false)
          end

          it 'expects guarantor profile details until the member opts to skip' do
            progress(:welcome, :member_profile)
            expect(page).to have_content('Guarantor or Guardian Information')

            fill_in 'Street Address Line One', with: '1234 Easy St'
            click_next

            ['First Name', 'Last Name', 'Phone', 'Email', 'Forwarding Address'].each do |message|
              expect_error_message(message)
            end

            check_checkbox('Skip Guarantor/Guardian Profile')
            click_next

            expect(find('h2', text: 'Risk Management Program')).to be_present
          end
        end

        context 'when guarantor is not skippable' do
          it 'does not give the option of skipping the guarantor' do
            progress(:welcome, :member_profile)
            fill_in 'Street Address Line One', with: '1234 Easy St'
            expect(page).to have_no_content(%r{Skip Guarantor/Guardian Profile?})
          end
        end
      end
    end

    describe 'risk release' do
      context 'when risk release is not required' do
        it 'shows the options' do
          progress(:welcome, :member_profile, :guarantor_profile)
          expect(page).to have_content 'Risk Management Program'
          expect(page).to have_content 'Yes, accept coverage'
          expect(page).to have_content 'No, I will provide my own insurance'
          expect(page).to have_content '$300.00', count: 2
          click_next

          # submit
          expect { progress(:membership_agreement) }.to(
            change { RiskRelease::Enrollment.count }.by(1).and(
              change { Invoice.where('description ILIKE ?', 'Damage Waiver%').count }.by(1)
            )
          )
        end

        context 'when it is configured for two_installments' do
          let(:risk_release_config) do
            build(:member_onboarding_risk_release,
                  billing_frequency: :two_installments,
                  installments: build_list(:member_onboarding_risk_release_installment, 2))
          end

          it 'creates both installments' do
            progress(:welcome, :member_profile, :guarantor_profile)
            expect(page).to have_content 'Risk Management Program'
            click_radio('Yes, accept coverage', 'true')
            click_next

            # submit
            expect { progress(:membership_agreement) }.to(
              change { RiskRelease::Enrollment.count }.by(1).and(
                change { Invoice.where('description ILIKE ?', 'Damage Waiver%').count }.by(2)
              )
            )
          end
        end
      end

      context 'when risk release is required' do
        let(:risk_release_config) do
          build(:member_onboarding_risk_release, enrollment_required: true)
        end

        it 'shows the confirmation checkbox' do
          progress(:welcome, :member_profile, :guarantor_profile)
          expect(page).to have_content 'Risk Management Program'
          checkbox_text =
            'I accept coverage for property damage protection for a total fee of $300.00'
          expect(page).to have_content(checkbox_text)

          uncheck_checkbox(checkbox_text)
          click_next
          expect_error_message('Must accept Risk Release when onboarding')

          check_checkbox(checkbox_text)
          click_next

          # submit
          expect { progress(:membership_agreement) }.to(
            change { RiskRelease::Enrollment.count }.by(1).and(
              change { Invoice.where('description ILIKE ?', 'Damage Waiver%').count }.by(1)
            )
          )
        end
      end
    end

    describe 'membership agreement' do
      it 'shows the membership agreement page' do
        progress(:welcome, :member_profile, :guarantor_profile, :risk_release)

        click_next
        expect_error_message('Please agree to use electronic signatures.')

        check_checkbox('I agree to use electronic records and signatures.')
        click_next
        expect_error_message('Full name')
        expect_error_message('Signature')

        fill_in 'Full Name', with: 'Amanda Ripley-McClaren'
        fill_in_signature
        click_next
      end

      it 'creates the expected things' do
        progress(:welcome, :member_profile, :guarantor_profile, :risk_release)

        expect do
          fill_out_membership_agreement
          click_next
        end
          .to change {
            LeaseMembership.where(role: :primary_tenant, tenant: tenant).count
          }.by(1).and change {
            Agreements::SimpleAgreement::Membership.where(role: :primary, tenant: tenant).count
          }.by(1)

        tenant.reload
        expect(tenant.meta('year_in_school')).to eq('junior')
        expect(tenant.meta('name_of_pet')).to eq('Fluffsy')
        expect(tenant.taxpayer_identification.tin).to eq('*********')

        lease = LeaseMembership.where(role: :primary_tenant, tenant: tenant).last.lease
        guarantor = lease.primary_guarantor
        expect(guarantor).to have_attributes({ first_name: 'Hubert', last_name: 'Farnsworth',
                                               phone: '+1**********', email: '<EMAIL>' })
        expect(guarantor.forwarding_address).to \
          have_attributes(line_one: '1234 Easy St.', line_two: 'Apt 1.', city: 'MI',
                          region: 'Ann Arbor', postal_code: '48108')
        expect(guarantor.meta('drivers_license_number')).to eq('MI111111111')
        expect(tenant.meta('drivers_license_number')).to eq('**********')
      end

      context 'when an agreement does NOT need a guarantor signature' do
        let(:membership_agreement_config) do
          build(:member_onboarding_membership_agreement, require_member_signature: true)
        end

        it 'does not request a signature from a guarantor' do
          progress(:welcome, :member_profile, :guarantor_profile, :risk_release)

          fill_out_membership_agreement
          click_next

          created_agreement = Agreements::SimpleAgreement::Membership
                              .where(role: :primary, tenant: tenant).last.simple_agreement

          expect(created_agreement.electronic_signatures.where(recipient: tenant).count).to eq(1)
          expect(created_agreement.electronic_signatures.where.not(recipient: tenant).count).to eq(0)
        end
      end

      context 'when an agreement does need a guarantor signature' do
        let(:membership_agreement_config) do
          build(:member_onboarding_membership_agreement, require_member_signature: true,
                                                         require_guarantor_signature: true)
        end

        it 'does request a signature from a guarantor' do
          progress(:welcome, :member_profile, :guarantor_profile, :risk_release)

          fill_out_membership_agreement
          click_next

          created_agreement = Agreements::SimpleAgreement::Membership
                              .where(role: :primary, tenant: tenant).last.simple_agreement

          electronic_signatures = created_agreement.electronic_signatures.where(recipient_type: 'Tenant')
          expect(electronic_signatures.where(recipient: tenant).count).to eq(1)
          expect(electronic_signatures.where.not(recipient: tenant).count).to eq(1)
        end
      end

      context 'when an agreement does NOT need a countersigner' do
        let(:membership_agreement_config) do
          build(:member_onboarding_membership_agreement, require_member_signature: true)
        end

        it 'does not request a signature from a countersigner' do
          progress(:welcome, :member_profile, :guarantor_profile, :risk_release)

          fill_out_membership_agreement
          click_next

          created_agreement = Agreements::SimpleAgreement::Membership
                              .where(role: :primary, tenant: tenant).last.simple_agreement

          expect(created_agreement.electronic_signatures.where(recipient: tenant).count).to eq(1)
          expect(created_agreement.electronic_signatures.where.not(recipient: tenant).count).to eq(0)
        end
      end

      context 'when an agreement does need a countersigner' do
        let(:countersigner) { create(:property_manager) }
        let(:membership_agreement_config) do
          build(:member_onboarding_membership_agreement, require_member_signature: true,
                                                         require_countersigner: true,
                                                         countersigner: countersigner)
        end

        it 'requests a signature from a countersigner' do
          progress(:welcome, :member_profile, :guarantor_profile, :risk_release)

          fill_out_membership_agreement
          click_next

          created_agreement = Agreements::SimpleAgreement::Membership
                              .where(role: :primary, tenant: tenant).last.simple_agreement

          expect(created_agreement.electronic_signatures.where(recipient: tenant).count).to eq(1)
          expect(created_agreement.electronic_signatures.where(recipient: countersigner).count).to eq(1)
          expect(
            created_agreement.electronic_signatures
            .where.not(recipient: [tenant, countersigner])
            .count
          ).to eq(0)
        end
      end

      context 'when an agreement needs the default countersigner' do
        let(:countersigner) { create(:property_manager) }
        let(:membership_agreement_config) do
          build(:member_onboarding_membership_agreement, require_member_signature: true,
                                                         require_countersigner: true)
        end
        let(:primary_countersigner) { create(:property_manager) }

        before do
          allow_any_instance_of(Customer).to receive(:primary_countersigner).and_return(primary_countersigner)
        end

        it 'requests a signature from the default countersigner' do
          progress(:welcome, :member_profile, :guarantor_profile, :risk_release)

          fill_out_membership_agreement
          click_next

          created_agreement = Agreements::SimpleAgreement::Membership
                              .where(role: :primary, tenant: tenant).last.simple_agreement

          expect(created_agreement.electronic_signatures.where(recipient: primary_countersigner).count).to eq(1)
          expect(created_agreement.electronic_signatures.where(recipient: tenant).count).to eq(1)
          expect(
            created_agreement.electronic_signatures
              .where.not(recipient: [tenant, primary_countersigner])
              .count
          ).to eq(0)
        end
      end

      context 'when an agreement already exists' do
        let!(:simple_agreement) do
          create(:simple_agreement, property: property, memberships:
            build_list(:agreements_simple_agreement_membership, 1, tenant: tenant, role: :primary))
        end

        it 'uses the existing agreement to bill dues, have a document, and get signed' do
          progress(:welcome, :member_profile, :guarantor_profile, :risk_release)
          expect do
            fill_out_membership_agreement
            click_next
          end.to change {
            LeaseMembership.where(role: :primary_tenant, tenant: tenant).count
          }.by(1).and not_change {
            Agreements::SimpleAgreement::Membership.where(role: :primary, tenant: tenant).count
          }.and change {
            simple_agreement.reload.electronic_signatures.where(recipient: tenant).count
          }.and change {
            simple_agreement.reload.unexecuted_lease_copy
          }.from(nil).and change {
            Invoice.joins(:line_items).where(
              line_items: { charge_preset_id: membership_preset.id }
            ).count
          }.by(1)
        end
      end

      context 'when an overlapping lease already exists' do
        before do
          # existing lease - 6mo ago - 6mo from now
          # onboarding lease start date - today - 1 year from now
          create(:lease_membership, tenant: tenant, property: property)
        end

        it 'still creates a new lease' do
          progress(:welcome, :member_profile, :guarantor_profile, :risk_release)
          expect do
            fill_out_membership_agreement
            click_next
          end.to change {
            LeaseMembership.where(role: :primary_tenant, tenant: tenant).count
          }.by(1).and change {
            Agreements::SimpleAgreement::Membership.where(role: :primary, tenant: tenant).count
          }.by(1).and change {
            Invoice.joins(:line_items).where(
              line_items: { charge_preset_id: membership_preset.id }
            ).count
          }.by(1)
        end
      end

      context 'when the membership agreement does NOT require a signature' do
        let(:membership_agreement_config) do
          build(:member_onboarding_membership_agreement, require_member_signature: false)
        end

        it 'does not go to the membership agreement signing page but does create one on submit' do
          progress(:welcome, :member_profile)

          expect { progress(:guarantor_profile, :risk_release) }.to change {
            LeaseMembership.where(role: :primary_tenant, tenant: tenant).count
          }.by(1).and change {
            Agreements::SimpleAgreement::Membership.where(role: :primary, tenant: tenant).count
          }.by(1)

          expect(page).to have_current_path(
            lease_agreement_tenants_member_onboarding_path(onboarding)
          )

          created_agreement = Agreements::SimpleAgreement::Membership
                              .where(role: :primary, tenant: tenant).last.simple_agreement
          expect(created_agreement.electronic_signatures).to be_empty
        end
      end

      context 'when include_damage_waiver is true' do
        let(:auto_enroll_damage_waiver?) { true }

        let(:membership_agreement_config) do
          build(:member_onboarding_membership_agreement,
                require_member_signature: true,
                include_damage_waiver: true)
        end

        let(:risk_release_config) { nil }

        it 'displays additional message and required damage waiver acknowledgement checkbox' do
          progress(:welcome, :member_profile, :guarantor_profile)
          expect(page).to have_content(
            "By completing onboarding, you'll be enrolled in Greek House Shield"
          )

          expect(page).to have_content(
            'I acknowledge that I will be enrolled in the Greek House Shield program'
          )

          fill_out_membership_agreement
          click_next

          expect(page).to have_content('Please acknowledge enrollment in Greek House Shield')

          check_checkbox('I acknowledge that I will be enrolled in the Greek House Shield program')
          click_next

          expect(page).to \
            have_current_path(lease_agreement_tenants_member_onboarding_path(onboarding))
        end

        it 'creates risk release enrollment' do
          start_date = Date.new(2025, 8, 1)
          end_date = Date.new(2025, 12, 31)

          allow(Rails.configuration).to receive(:damage_waiver_defaults).and_return(
            alever: {
              start_date: start_date,
              end_date: end_date
            }
          )

          progress(:welcome, :member_profile, :guarantor_profile)

          fill_out_membership_agreement
          check_checkbox('I acknowledge that I will be enrolled in the Greek House Shield program')

          click_next

          expect(page).to \
            have_current_path(lease_agreement_tenants_member_onboarding_path(onboarding))

          created_agreement_membership = Agreements::SimpleAgreement::Membership
                                         .where(role: :primary, tenant: tenant)
                                         .last
          created_lease_membership = LeaseMembership
                                     .where(role: :primary_tenant, tenant: tenant)
                                     .last

          expect(RiskRelease::Enrollment.count).to eq(1)

          expect(RiskRelease::Enrollment.last).to have_attributes(
            start_date: start_date,
            end_date: end_date,
            simple_agreement_membership: created_agreement_membership,
            lease_membership: created_lease_membership
          )
        end
      end
    end

    describe 'lease agreement' do
      it 'shows the lease agreement page' do
        progress(:welcome, :member_profile, :guarantor_profile,
                 :risk_release, :membership_agreement)
        expect(find('h2', text: 'Lease Agreement')).to be_present
        fill_out_lease
        click_next
        sleep 3
        expect(Lease.last.lease_memberships.guarantor.first.tenant.first_name).to eq('Hubert')
        expect(find('h2', text: 'Onboarding Complete')).to be_present
      end

      context 'when an agreement does NOT need a countersigner' do
        let(:lease_agreement_config) do
          build(:member_onboarding_lease_agreement, require_member_signature: true)
        end

        it 'does not request a signature from a countersigner' do
          progress(:welcome, :member_profile, :guarantor_profile, :risk_release)

          fill_out_membership_agreement
          click_next

          lease_membership = tenant.lease_memberships.last.lease

          expect(lease_membership.electronic_signatures.where(recipient: tenant).count).to eq(1)
          expect(lease_membership.electronic_signatures.where.not(recipient: tenant).count).to eq(0)
        end
      end

      context 'when an agreement does need a countersigner' do
        let(:countersigner) { create(:property_manager) }
        let(:lease_agreement_config) do
          build(:member_onboarding_lease_agreement, require_member_signature: true,
                                                    require_countersigner: true,
                                                    countersigner: countersigner)
        end

        it 'requests a signature from a countersigner' do
          progress(:welcome, :member_profile, :guarantor_profile, :risk_release)

          fill_out_membership_agreement
          click_next

          lease_membership = tenant.lease_memberships.last.lease

          expect(lease_membership.electronic_signatures.where(recipient: tenant).count).to eq(1)
          expect(lease_membership.electronic_signatures.where(recipient: countersigner).count).to eq(1)
          expect(
            lease_membership.electronic_signatures
            .where.not(recipient: [tenant, countersigner])
            .count
          ).to eq(0)
        end
      end

      context 'when an agreement needs the default countersigner' do
        let(:countersigner) { create(:property_manager) }
        let(:lease_agreement_config) do
          build(:member_onboarding_lease_agreement, require_member_signature: true,
                                                    require_countersigner: true)
        end
        let(:primary_countersigner) { create(:property_manager) }

        before do
          allow_any_instance_of(Customer).to receive(:primary_countersigner).and_return(primary_countersigner)
        end

        it 'requests a signature from the default countersigner' do
          progress(:welcome, :member_profile, :guarantor_profile, :risk_release)

          fill_out_membership_agreement
          click_next

          lease_membership = tenant.lease_memberships.last.lease

          expect(lease_membership.electronic_signatures.where(recipient: primary_countersigner).count).to eq(1)
          expect(lease_membership.electronic_signatures.where(recipient: tenant).count).to eq(1)
          expect(
            lease_membership.electronic_signatures
              .where.not(recipient: [tenant, primary_countersigner])
              .count
          ).to eq(0)
        end
      end
    end

    describe 'completion summary' do
      def expect_correct_onboarding_completion_page
        progress(:welcome, :member_profile, :guarantor_profile,
                 :risk_release, :membership_agreement, :lease_agreement)
        expect(page).to have_current_path \
          completion_summary_tenants_member_onboarding_path(onboarding)
        expect(page).to have_content('Onboarding Complete')
        expect(tenant.reload.meta(:completed_member_profile)).to be(true)
        expect(tenant.onboarding_completions.where(configuration: onboarding).count).to eq(1)

        expect(MemberOnboarding::Tenants::Wizard.hydrate(cohort, tenant)).to be_nil

        click_on 'Done'
        sleep 1
        expect(page).to have_current_path(tenants_rent_invoices_path)
      end

      context 'when explicitly assigned' do
        it 'shows the completion summary page' do
          expect_correct_onboarding_completion_page
        end
      end

      context 'when assigned through default onboarding' do
        it 'shows the completion summary page' do
          # Assign tenant through default onboarding
          onboarding.member_assignments.find_by(tenant: tenant).destroy!
          property = onboarding.portfolio.properties.first
          create(:agreements_simple_agreement_membership,
                 tenant: tenant,
                 property: property)
          create(:member_onboarding_property_membership,
                 enhanced: false,
                 property: property)

          expect_correct_onboarding_completion_page
        end
      end
    end

    context 'when cohort changes midstream' do
      let(:new_onboarding) do
        create(:member_onboarding_configuration, :guarantor,
               portfolio: portfolio,
               property_memberships: build_list(:member_onboarding_property_membership, 1,
                                                property: portfolio.properties.first))
      end
      let(:new_assignment!) do
        tenant.active_onboarding_assignment.update!(configuration: new_onboarding)
      end

      it 'shows the member information page' do
        progress(:welcome)

        fill_out_member_profile
        new_assignment!
        click_next
        sleep 2
        expect(page).to have_current_path welcome_tenants_member_onboarding_path(new_onboarding.id)
      end
    end
  end

  def progress(*steps)
    visit tenants_dashboard_path
    # This ensures the page loads before we try to interact with it
    expect(page).to have_content('Member Onboarding')
    steps.each do |step_id|
      case step_id
      when :welcome
        expect(find('.header', text: 'Welcome!')).to be_present
      when :member_profile
        expect(find('h2', text: 'Member Information')).to be_present
        fill_out_member_profile
      when :guarantor_profile
        expect(find('h2', text: 'Guarantor or Guardian Information')).to be_present
        fill_out_guarantor_profile
      when :risk_release
        expect(find('h2', text: 'Risk Management Program')).to be_present
        fill_out_risk_release
      when :skip_guarantor_profile
        expect(find('h2', text: 'Guarantor or Guardian Information')).to be_present
        check_checkbox('Skip Guarantor/Guardian Profile')
      when :membership_agreement
        expect(find('h2', text: 'Membership Agreement')).to be_present
        fill_out_membership_agreement
      when :lease_agreement
        expect(find('h2', text: 'Lease Agreement')).to be_present
        fill_out_lease
      end

      click_next
    end
  end

  def click_next
    perform_enqueued_jobs do
      click_on 'Next'
      wait_for_ajax
      sleep(1)
    end
  end

  def expect_error_message(message)
    within('.error.message') do
      expect(page).to have_content(/#{message}/i)
    end
  end

  def fill_in_address(
    line_one: '1234 Easy St.', line_two: 'Apt 1.', city: 'MI',
    region: 'Ann Arbor', postal_code: '48108'
  )
    fill_in 'Street Address Line One', with: line_one
    fill_in 'Street Address Line Two', with: line_two
    fill_in 'City', with: city
    fill_in 'Zip Code', with: postal_code
    fill_in 'State', with: region
  end

  def fill_out_guarantor_profile
    fill_in('Legal First Name', with: 'Hubert')
    fill_in('Legal Last Name', with: 'Farnsworth')
    fill_in('Phone', with: '**********')
    fill_in('Email', with: '<EMAIL>')
    fill_in('Drivers License Number', with: 'MI111111111')
    fill_in('Social Security Number', with: '123456789')
    fill_in('Date of Birth', with: '09/29/1955')
    fill_in_address
  end

  def fill_out_member_profile
    expect(page).to have_no_content(/Student Id/i)
    select_dropdown 'School Year', 'Junior'
    fill_in 'Social Security Number', with: '*********'
    fill_in 'Date of Birth', with: Date.parse('August 1, 2003')
    fill_in 'Name of Pet', with: 'Fluffsy'
    fill_in('Drivers License Number', with: '**********')
    check_checkbox(/text messages/)
  end

  def fill_out_risk_release
    click_radio('No, I will provide my own insurance', 'false')
  end

  def fill_out_lease
    fill_out_membership_agreement # same electronic signature fields for lease & agreement
  end

  def fill_out_membership_agreement
    fill_in 'Full Name', with: 'Amanda Ripley-McClaren'
    fill_in_signature
    check_checkbox('I agree to use electronic records and signatures.')
  end
end
# rubocop:enable RSpec/MultipleMemoizedHelpers
