require 'rails_helper'

RSpec.describe 'tenant portal payments', :js, capybara_login: :tenant do
  let(:lease_membership) { tenant.lease_memberships.last }

  let!(:merchant_account) do
    customer_account = create(:bank_account,
                              owner: tenant.current_property.company)

    create(:merchant_account, :profit_stars, :ach,
           bank_account: customer_account)
  end

  let!(:payment_method) do
    create(:bank_account, :with_profit_stars,
           owner: tenant, merchant_account: merchant_account)
  end

  scenario 'a tenant sets up scheduled payments' do
    visit tenants_account_path

    one_hundred_dollars = Monetize.parse('$100.00')

    create(:charge_schedule_entry_allocation,
           lease_membership: lease_membership,
           amount: one_hundred_dollars)

    click_on 'Add Scheduled Payment'

    expect(page).to have_content 'New Scheduled Payment'

    date = Time.zone.today.next_month.beginning_of_month

    select_dropdown 'Payment Method', payment_method.name

    click_on 'Create'

    expect(page).to have_content(
      /scheduled payment created successfully/i
    )

    scheduled_payment = ScheduledPayment.last!

    expect(scheduled_payment.description).to eq('Monthly Rent')
    expect(scheduled_payment).to be_recurring
    expect(scheduled_payment.date).to eq(date)
    expect(scheduled_payment.amount).to eq(one_hundred_dollars)
    expect(scheduled_payment.payment_method).to eq(payment_method)
    expect(scheduled_payment.lease_membership).to eq(lease_membership)
    expect(scheduled_payment.property).to eq(lease_membership.property)
  end

  context 'with only an agreement' do
    scenario 'a tenant adds a scheduled payment for an agreement' do
      tenant.lease_memberships.destroy_all

      property = merchant_account.bank_account.owner.properties.first

      simple_agreement = create(:simple_agreement, property: property)
      simple_agreement.memberships.first.update!(tenant: tenant)

      visit tenants_account_path

      click_on 'Add Scheduled Payment'

      expect(page).to have_content 'New Scheduled Payment'

      select_dropdown 'Payment Method', payment_method.name

      fill_in 'Amount', with: '$100.00'

      click_on 'Create'

      expect(page).to have_content(
        /scheduled payment created successfully/i
      )

      scheduled_payment = ScheduledPayment.last!

      expect(scheduled_payment.lease_membership).to be_nil
      expect(scheduled_payment.simple_agreement).to eq(simple_agreement)
      expect(scheduled_payment.property).to eq(simple_agreement.property)
    end
  end

  context 'existing scheduled payment' do
    let!(:scheduled_payment) do
      create(:scheduled_payment, source: tenant,
                                 payment_method: payment_method,
                                 lease_membership: lease_membership,
                                 status: status)
    end

    let(:status) { :idle }

    scenario 'a tenant sees the scheduled payment in their account' do
      visit tenants_account_path

      expect(page).to have_content(scheduled_payment.description)
    end

    scenario 'a tenant sees the upcoming scheduled payment on the dashboard' do
      visit tenants_dashboard_path

      expect(page).to have_content(scheduled_payment.amount.format)
      expect(page).to have_content(
        scheduled_payment.date.strftime('%B %-e, %Y')
      )
    end

    scenario 'a user edits a scheduled payment' do
      visit tenants_account_scheduled_payment_path(scheduled_payment)

      click_on 'Edit'

      expect(page).to have_content 'Edit Scheduled Payment'

      date = Time.zone.tomorrow
      fill_in_calendar '#scheduled_payment_date', with: date

      toggle_checkbox 'Recurring Monthly'

      click_on 'Update'

      expect(page).to have_content(/updated successfully/i)

      scheduled_payment.reload
      expect(scheduled_payment.date).to eq(date)
      expect(scheduled_payment).to be_recurring
    end

    scenario 'a user only changes the description' do
      original_date = 3.months.ago.to_date

      scheduled_payment.update!(date: original_date)

      visit tenants_account_scheduled_payment_path(scheduled_payment)

      click_on 'Edit'

      fill_in 'Description', with: (updated_description = 'Updated Description')

      click_on 'Update'

      expect(page).to have_content(/updated successfully/i)

      scheduled_payment.reload
      expect(scheduled_payment.date).to eq(original_date)
      expect(scheduled_payment.description).to eq(updated_description)
    end

    scenario 'a tenant removes a scheduled payment' do
      visit tenants_account_scheduled_payment_path(scheduled_payment)

      click_on 'Edit'

      expect do
        accept_confirm { click_on 'Delete' }
        expect(page).to have_content(/successful/i)
      end.to change { ScheduledPayment.count }.by(-1)
    end

    context 'failed scheduled payment' do
      let(:status) { :failed }

      scenario 'a user retries a scheduled payment' do
        visit tenants_account_scheduled_payment_path(scheduled_payment)

        expect(page).to have_content(/failed/i)

        expect(PaymentProcessing::CreateElectronicPayment).to receive(:call) do
          OpenStruct.new(successful?: true, payment: create(:payment))
        end

        accept_confirm { click_on 'Retry' }

        expect(page).to have_content(/successfull/i)
      end
    end
  end
end
