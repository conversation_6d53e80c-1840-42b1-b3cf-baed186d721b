require 'rails_helper'

RSpec.describe 'invoice approval change requests', :js,
               capybara_login: :property_manager do
  scenario 'a user requests changes on a pending approval' do
    create(:approvals_rule, action: :invoice_payment, approver_sources: manager)

    other_user = create(:property_manager)

    invoice = create(:vendor_invoice)

    message = 'Update the description'

    visit accounting_payables_invoice_path(invoice)

    click_on 'Request Changes'

    within_modal do
      select_dropdown 'User', other_user.name

      click_on 'Submit'

      expect(page).to have_content(/can't be blank/i)

      fill_in 'Message', with: message

      click_on 'Submit'
    end

    expect(page).to have_content(/successful/i)

    request = Approvals::ChangeRequest.last!

    expect(request).to have_attributes(
      approvable_id: invoice.id,
      created_by_id: manager.id,
      requested_from_id: other_user.id,
      message: message,
      pending: true
    )

    notification = Notification.last!
    expect(notification).to have_attributes(user: request.requested_from)
  end

  scenario 'a user completes a change request' do
    request = create(:approvals_change_request, requested_from: manager)

    invoice = request.approvable

    visit accounting_payables_invoice_path(invoice)

    expect(page).to have_content(request.message)

    click_on 'Resubmit'

    expect(page).to have_content(/successful/i)

    expect(request.reload).not_to be_pending

    notification = Notification.last!
    expect(notification).to have_attributes(user: request.created_by)
  end
end
