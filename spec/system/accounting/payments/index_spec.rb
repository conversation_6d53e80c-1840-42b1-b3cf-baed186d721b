require 'rails_helper'

RSpec.describe 'payments index', :elasticsearch, :js,
               capybara_login: :property_manager do
  let(:payment_one) do
    create(:payment, description: 'Payment One',
                     date: 1.day.ago,
                     kind: :credit,
                     status: :pending)
  end

  let(:payment_two) do
    create(:payment, description: 'Payment Two',
                     date: 2.days.ago,
                     kind: :check,
                     check_number: '9876')
  end

  let!(:payments) do
    perform_enqueued_jobs do
      [payment_one, payment_two]
    end
  end

  before { visit accounting_payments_path }

  describe 'filtering' do
    scenario 'by description' do
      fill_in 'Search...', with: payment_one.description

      expect(page).to have_no_content(payment_two.description)
      expect(page).to have_content(payment_one.description)
    end

    scenario 'by payer' do
      filter_index_search 'Payer', with: payment_one.payer.name

      expect(page).to have_no_content(payment_two.description)
      expect(page).to have_content(payment_one.description)
    end

    scenario 'by payee' do
      filter_index_search 'Payee', with: payment_one.payee.name

      expect(page).to have_no_content(payment_two.description)
      expect(page).to have_content(payment_one.description)
    end

    scenario 'by payment method' do
      filter_index 'Method', with: 'Card Payment'

      expect(page).to have_no_content(payment_two.description)
      expect(page).to have_content(payment_one.description)
    end

    scenario 'by status' do
      filter_index 'Status', with: 'Pending'

      expect(page).to have_no_content(payment_two.description)
      expect(page).to have_content(payment_one.description)
    end

    scenario 'by check number' do
      fill_in 'Search...', with: payment_two.check_number

      expect(page).to have_no_content(payment_one.description)
      expect(page).to have_content(payment_two.description)
    end

    scenario 'by date range'
  end

  describe 'sorting' do
    scenario 'by date' do
      sort_index 'Date'

      descriptions = payments.sort_by(&:date).map(&:description)

      expect(descriptions.first).to appear_before(descriptions.last)
    end

    scenario 'by description' do
      sort_index 'Description'

      descriptions = payments.map(&:description).sort

      expect(descriptions.first).to appear_before(descriptions.last)
    end

    scenario 'by amount' do
      sort_index 'Amount'

      descriptions = payments.sort_by(&:amount).map(&:description)

      expect(descriptions.first).to appear_before(descriptions.last)
    end
  end
end
