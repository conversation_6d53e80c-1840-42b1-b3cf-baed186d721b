require 'rails_helper'

RSpec.describe 'deposit batches', :js, capybara_login: :property_manager do
  let!(:bank_account) { create(:bank_account) }

  scenario 'a user creates a new deposit batch' do
    name = 'My Batch'
    date = 2.days.from_now.to_date
    amount = '$150.00'
    items = 3

    visit organization_bank_account_path(bank_account)

    click_on 'New Payment Batch'

    within '.visible.modal' do
      fill_in 'Name', with: name
      fill_in 'Expected Statement Date', with: date
      fill_in 'Expected Total Amount', with: amount
      fill_in 'Expected Number of Items', with: items
      click_on 'Submit'
    end

    expect(page).to have_content(name)

    batch = DepositBatch.last!
    expect(batch.name).to eq(name)
    expect(batch.expected_date).to eq(date)
    expect(batch.expected_amount.format).to eq(amount)
    expect(batch.expected_count).to eq(3)
  end

  scenario 'a user deletes a deposit batch' do
    batch = create(:deposit_batch)

    visit organization_bank_account_deposit_batch_path(batch.bank_account, batch)

    expect do
      accept_confirm do
        find('.trash.icon').click
      end

      expect(page).to have_content(/deleted successfully/i)
    end.to change { DepositBatch.count }.by(-1)
  end
end
