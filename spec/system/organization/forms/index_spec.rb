require 'rails_helper'

RSpec.describe 'forms index', :js, capybara_login: :property_manager do
  context 'when no forms created yet' do
    before { visit organization_forms_path }

    scenario 'should show empty state' do
      expect(page).to have_content('Seamlessly manage event registration with our custom forms')
      expect(page).to have_link('Create Form')
    end
  end

  context 'when has forms created' do
    let!(:form_two) { create(:custom_forms_form, name: 'b test form', published_at: 2.days.ago) }
    let!(:form_one) { create(:custom_forms_form, name: 'a test form', published_at: 2.days.ago) }
    let!(:form_three) { create(:custom_forms_form, name: 'c test form', published_at: 2.days.ago) }
    let!(:archived_form) { create(:custom_forms_form, archived_at: 7.days.ago, name: 'd test form') }

    let(:forms) { [form_one, form_two, archived_form] }

    before { visit organization_forms_path }

    context 'when sorting' do
      scenario 'by name' do
        sort_index 'Name'

        expect('a test form').to appear_before('b test form')
        expect('b test form').to appear_before('c test form')
      end
    end

    context 'when filtering' do
      scenario 'by search' do
        expect(page).to have_content(form_one.name)
        expect(page).to have_content(form_two.name)

        fill_in 'Search...', with: form_one.name

        expect(page).to have_no_content(form_two.name)
        expect(page).to have_content(form_one.name)
      end

      scenario 'by archived' do
        expect(page).to have_no_content(archived_form.name) # Unarchived is the default filter
        expect(page).to have_content(form_one.name)
        expect(page).to have_content(form_two.name)

        filter_index 'archived', with: 'Archived'

        expect(page).to have_content(archived_form.name)
        expect(page).to have_no_content(form_one.name)
        expect(page).to have_no_content(form_two.name)
      end
    end
  end

  describe 'with different form status' do
    context 'with unpublished form' do
      it 'shows the correct status' do
        create(:custom_forms_form)
        visit organization_forms_path

        expect(page).to have_content('Draft')
        expect(page).to have_no_link('Copy Link')
        expect(page).to have_no_link('Send Invite')
      end
    end

    context 'with published but closed form' do
      it 'shows the correct status' do
        create(:custom_forms_form, published_at: 2.days.ago, start_date: Time.zone.tomorrow)
        visit organization_forms_path

        expect(page).to have_content('Closed')
        expect(page).to have_link('Copy Link')
        expect(page).to have_link('Send Invite')
      end
    end

    context 'with active form' do
      it 'shows the correct status' do
        create(:custom_forms_form, published_at: 2.days.ago)
        visit organization_forms_path

        expect(page).to have_content('Active')
        expect(page).to have_link('Copy Link')
        expect(page).to have_link('Send Invite')
      end
    end

    context 'with archived form' do
      it 'shows the correct status' do
        form = create(:custom_forms_form, published_at: 2.days.ago, archived_at: Time.zone.now)
        visit organization_forms_path

        filter_index 'archived', with: 'Archived'

        expect(page).to have_content('Archived')
        expect(page).to have_content(form.name)
        expect(page).to have_no_link('Copy Link')
        expect(page).to have_no_link('Send Invite')
      end
    end
  end

  describe 'Index page copy link' do
    let!(:form) { create(:custom_forms_form, published_at: 2.days.ago) }

    before do
      visit organization_forms_path
    end

    it 'has a Copy Link button with correct data attributes for StimulusJS' do
      expect(page).to have_css('td[data-controller="copyable-link"]', text: 'Copy Link')
      link = find('td[data-controller="copyable-link"] a')
      expect(link['href']).to include("/forms/#{form.token}")
    end
  end

  describe 'send invite' do
    let!(:form) { create(:custom_forms_form, published_at: 2.days.ago) }

    before { visit organization_forms_path }

    it 'redirects to new email page with prefilled fields' do
      click_on 'Send Invite'

      expect(page).to have_field('Subject', with: form.name)

      trix_editor = find('trix-editor')
      within trix_editor do
        expect(page).to have_content("Please fill out the following form: #{new_custom_forms_submission_url(form.token, subdomain: Customer.current_subdomain)}")
      end
    end
  end

  describe 'when archiving' do
    let!(:form) { create(:custom_forms_form) }
    let!(:form2) { create(:custom_forms_form) }

    describe 'with one form' do
      it 'archives one form' do
        visit organization_forms_path

        expect(page).to have_content(form.name)
        expect(page).to have_content(form2.name)

        select_row form.name

        find('.item', text: 'Archive').click

        within_modal do
          expect(page).to have_content 'Bulk Archiving'
          expect(page).to have_content '1 Form Selected'
          expect(page).to have_content(/archive the selected form?/i)
          click_button 'Submit'
        end

        expect(page).to have_content(/1 Form Archived Successfully/i)
        expect(form.reload).to be_archived
        expect(form2.reload).not_to be_archived
      end
    end

    describe 'with multiple forms' do
      it 'archives all selected forms' do
        visit organization_forms_path

        expect(page).to have_content(form.name)
        expect(page).to have_content(form2.name)

        select_all_rows

        find('.item', text: 'Archive').click

        within_modal do
          expect(page).to have_content 'Bulk Archiving'
          expect(page).to have_content '2 Forms Selected'
          expect(page).to have_content(/archive the selected forms?/i)
          click_button 'Submit'
        end

        expect(page).to have_content(/2 Forms Archived Successfully/i)
        expect(form.reload).to be_archived
        expect(form2.reload).to be_archived
      end
    end

    describe 'with already archived form' do
      it 'updates archvied_at' do
        form.archive!

        initial_archived_at = form.archived_at

        visit organization_forms_path
        filter_index 'archived', with: 'Archived'

        expect(page).to have_content(form.name)

        select_row form.name

        find('.item', text: 'Archive').click

        within_modal do
          expect(page).to have_content 'Bulk Archiving'
          expect(page).to have_content '1 Form Selected'
          expect(page).to have_content(/archive the selected form?/i)
          click_button 'Submit'
        end

        expect(page).to have_content(/1 Form Archived Successfully/i)
        expect(form.reload).to be_archived
        expect(form.archived_at).not_to eq(initial_archived_at)
        expect(form2.reload).not_to be_archived
      end
    end
  end

  describe 'when unarchiving' do
    let!(:form) { create(:custom_forms_form).tap(&:archive!) }
    let!(:form2) { create(:custom_forms_form).tap(&:archive!) }

    describe 'with one form' do
      it 'unarchives one form' do
        visit organization_forms_path
        filter_index 'archived', with: 'Archived'

        expect(page).to have_content(form.name)
        expect(page).to have_content(form2.name)

        select_row form.name

        find('.item', text: 'Unarchive').click

        expect(page).to have_content(/1 Form Unarchived Successfully/i)
        expect(form.reload).not_to be_archived
        expect(form2.reload).to be_archived
      end
    end

    describe 'with multiple forms' do
      it 'unarchives all selected forms' do
        visit organization_forms_path
        filter_index 'archived', with: 'Archived'

        expect(page).to have_content(form.name)
        expect(page).to have_content(form2.name)

        select_all_rows

        find('.item', text: 'Unarchive').click

        expect(page).to have_content(/2 Forms Unarchived Successfully/i)

        expect(form.reload).not_to be_archived
        expect(form2.reload).not_to be_archived
      end
    end

    describe 'with already unarchived form' do
      it 'does nothing' do
        form.unarchive!

        visit organization_forms_path

        expect(page).to have_content(form.name)

        select_row form.name

        find('.item', text: 'Unarchive').click

        expect(page).to have_content(/1 Form Unarchived Successfully/i)
        expect(form.reload).not_to be_archived
      end
    end
  end
end
