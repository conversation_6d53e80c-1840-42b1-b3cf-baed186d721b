require 'rails_helper'

RSpec.describe 'forms create', :js, capybara_login: :property_manager do
  context 'when no forms created yet' do
    before { visit organization_forms_path }

    scenario 'click on create form button on empty state' do
      click_on 'Create Form'

      within_modal do
        expect(page).to have_content('Form Title')

        fill_in 'Form Title', with: 'New Form Title'
        select_dropdown 'Template', /^Event Registration$/

        click_on 'Submit'
      end

      expect(page).to have_content('New Form Title')

      form = CustomForms::Form.last!
      expect(form.name).to eq('New Form Title')
    end

    scenario 'click on new form button' do
      click_on 'New Form'

      within_modal do
        expect(page).to have_content('Form Title')

        fill_in 'Form Title', with: 'New Form Title 2'
        select_dropdown 'Template', /^Event Registration$/

        click_on 'Submit'
      end

      expect(page).to have_content('New Form Title 2')

      form = CustomForms::Form.last!
      expect(form.name).to eq('New Form Title 2')
    end

    scenario 'missing required field' do
      form_count = CustomForms::Form.count

      click_on 'Create Form'

      within_modal do
        expect(page).to have_content('Form Title')

        select_dropdown 'Template', /^Event Registration$/

        click_on 'Submit'
      end

      expect(page).to have_content("Form Title can't be blank")

      expect(CustomForms::Form.count).to eq form_count
    end

    scenario 'event_registration_v1' do
      click_on 'New Form'

      within_modal do
        expect(page).to have_content('Form Title')

        fill_in 'Form Title', with: 'New Form Title'
        select_dropdown 'Template', /^Event Registration$/

        click_on 'Submit'
      end

      expect(page).to have_content('New Form Title')

      form = CustomForms::Form.last!

      expect(page).to have_current_path edit_organization_form_path(form)
    end

    scenario 'event_registration_with_payment_v1' do
      create(:merchant_account, :credit_card)
      create(:revenue_account)

      click_on 'New Form'

      within_modal do
        expect(page).to have_content('Form Title')

        fill_in 'Form Title', with: 'New Form Title'
        select_dropdown 'Template', /^Event Registration with Payment$/

        click_on 'Submit'
      end

      expect(page).to have_content('New Form Title')

      form = CustomForms::Form.last!

      payment_setting = CustomForms::AutomationSettings::Payment::V1.find_by(form_id: form.id)
      expect(payment_setting).to be_present
    end

    scenario 'blank_v1' do
      click_on 'New Form'

      within_modal do
        expect(page).to have_content('Form Title')

        fill_in 'Form Title', with: 'New Form Title'
        select_dropdown 'Template', /^Blank Form$/

        click_on 'Submit'
      end

      expect(page).to have_content('New Form Title')

      form = CustomForms::Form.last!

      expect(page).to have_current_path edit_organization_form_path(form)
    end
  end
end
