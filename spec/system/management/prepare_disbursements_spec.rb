require 'rails_helper'

RSpec.describe 'prepare disbursements', :js,
               capybara_login: :property_manager do
  let(:effective_date) { 2.days.ago.to_date }
  let(:post_date) { 1.day.ago.to_date }

  scenario 'a user prepares a disbursement for an entity' do
    property = create(:property)
    company = property.company
    company.update!(customer_managed: true)

    allow_any_instance_of(Accounting::CashActivity).to \
      receive(:cash_balance_on_date).with(effective_date) { Money.new(250_00) }

    visit manage_companies_path

    row = find('tr', text: company.name)

    within(row) do
      find('.ui.checkbox').click
    end

    click_on_batch_action_button 'Prepare Disbursements'

    fill_in 'Activity Through', with: effective_date
    fill_in 'Post Date', with: post_date
    fill_in 'Payables Batch Name', with: (batch_name = 'Disbursement Batch')

    perform_enqueued_jobs do
      click_on 'Submit'
      expect(page).to have_content('Preparing Disbursements')
    end

    expect(page).to have_content('1 Invoice')

    batch = Payment::Batch.find_by!(name: batch_name)
    invoice = batch.invoices.first!
    expect(invoice.post_date).to eq(post_date)
    expect(invoice.seller).to eq(property)
    expect(invoice.buyer).to eq(Customer.current.client_entity)
    expect(invoice.amount.format).to eq('$250.00')
  end
end
