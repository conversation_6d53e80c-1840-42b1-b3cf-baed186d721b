require 'rails_helper'

RSpec.describe 'user fingerprints', :js,
               capybara_login: :property_manager do
  let(:login_fingerprint) { manager.login_fingerprints.first }

  describe 'with approved fingerprint' do
    scenario 'user access service as normal and no notification enqueued' do
      visit operations_pulse_path
      expect(page).to have_content(/Operations Pulse/i)

      expect_any_instance_of(User::LoginFingerprint).to \
        receive(:send_email)
      expect_any_instance_of(User::LoginFingerprint).not_to \
        receive(:resend_email)

      manager.login_fingerprints.first.destroy
      visit operations_pulse_path
      expect_logged_out_with_message
      expect(manager.login_fingerprints.first.confirmed_at).to be_nil

      login
      expect_logged_out_with_message
      expect(manager.login_fingerprints.first.confirmed_at).to be_nil

      # Expect email to be resent since expired
      login
      expect_logged_out_with_message
      expect(manager.login_fingerprints.first.confirmed_at).to be_nil

      # Expect error message to only show up once
      visit property_manager_session_path
      expect_logged_out_without_message

      manager.login_fingerprints.first.update!(confirmed_at: Time.zone.now)
      login
      expect(page).to have_content(/Operations Pulse/i)
      expect(page).to have_content(/Signed in successfully/i)

      find('.dropdown', text: "Hello, #{manager.first_name}").hover
      click_on 'Logout'
      expect_logged_out_without_message
    end
  end

  describe 'with expired fingerprint' do
    scenario 'user receives a new email' do
      manager.login_fingerprints.first.update!(confirmed_at: nil)
      manager.login_fingerprints.first.update!(confirmation_sent_at: 15.minutes.ago)

      expect_any_instance_of(User::LoginFingerprint).to receive(:resend_email)

      visit operations_pulse_path
      expect_logged_out_with_message
    end
  end

  def expect_logged_out_with_message
    expect(page).to have_content(/New login location detected/i)
    expect(page).to have_content(/Enter your credentials below to login./i)
    expect(page).to have_current_path(property_manager_session_path)
  end

  def expect_logged_out_without_message
    expect(page).to have_no_content(/New login location detected/i)
    expect(page).to have_content(/Enter your credentials below to login./i)
    expect(page).to have_current_path(property_manager_session_path)
  end

  def login
    fill_in 'Email address', with: manager.email
    fill_in 'Password', with: 'TrustNo1!'

    click_button 'Login'
  end
end
