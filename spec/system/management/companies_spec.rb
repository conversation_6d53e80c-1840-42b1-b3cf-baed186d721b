# create a company
# setup a company

# validate setup managed company has at least one ownership?

require 'rails_helper'

RSpec.describe 'managed companies', :js,
               capybara_login: :property_manager do
  let!(:owner) { create(:owner) }
  let!(:portfolio) { create(:portfolio) }

  scenario 'a user adds a managed company' do
    visit manage_companies_path

    click_on 'New Customer Entity'

    select_dropdown 'Portfolio', portfolio.name
    fill_in 'Name', with: 'Fox Properties, LLC'
    fill_in 'Taxpayer Identification Number', with: '*********'
    select_dropdown 'Entity Type', /\ALLC\Z/
    fill_in 'Notes', with: 'Random notes in here'

    fill_in_address \
      line_one: '1420 Washington Blvd.',
      line_two: 'Suite 301',
      city: 'Detroit',
      region: 'Michigan',
      postal_code: '48226'

    select_dropdown 'Owner', owner.name
    fill_in 'Percentage', with: '100'

    fill_in 'Monthly Minimum', with: '$75.00'
    fill_in 'Rent', with: '15'
    fill_in 'Late Fees', with: '100'
    within first('label', text: 'Renewal').find(:xpath, './..') do
      first('button.markup-kind').click
    end
    fill_in 'Renewal', with: '100'
    fill_in 'Maintenance', with: '250'
    fill_in 'Payables', with: '1000'

    click_on 'Create Entity'

    company = Company.last!
    expect(company.taxpayer_identification).to be_ein
    expect(company.tin).to eq('*********')
    expect(company.customer_managed?).to be true
    expect(company.customer_owned?).to be false
    expect(company).to be_setup
    expect(company.notes).to eq 'Random notes in here'

    contract = company.management_contract
    expect(contract.minimum_amount.format).to eq('$75.00')
    expect(contract.rent_markup.value).to eq BigDecimal('0.15')
    expect(contract.rent_markup).to be_percent
    expect(contract.late_markup.value).to eq BigDecimal('1.0')
    expect(contract.late_markup).to be_percent
    expect(contract.renewal_markup.value.format).to eq('$100.00')
    expect(contract.renewal_markup).to be_fixed
    expect(contract.maintenance_limit.format).to eq('$250.00')
    expect(contract.payables_limit.format).to eq('$1,000.00')
  end

  scenario 'a user sets up an owner-created company' do
    company = create(:company,
                     ownerships: [build(:ownership, owner: owner, amount: 1)],
                     setup: false, customer_managed: true)
    tax_ident = create(:taxpayer_identification, taxpayer: company)

    visit manage_companies_path

    expect(page).to have_content(/requires setup/i)
    click_on company.name

    select_dropdown 'Portfolio', portfolio.name

    expect(find_field('Name').value).to eq company.name

    hidden_tin = tax_ident.tin.dup
    hidden_tin[0..4] = '*****'
    expect(find_field('Taxpayer Identification Number').value).to eq hidden_tin

    expect(find_field('Line One').value).to eq company.address.line_one
    expect(find_field('Line Two').value).to eq company.address.line_two
    expect(find_field('City').value).to eq company.address.city
    expect(find_field('State').value).to eq company.address.region
    expect(find_field('Zip Code').value).to eq company.address.postal_code

    expect(page).to have_content(owner.name)
    expect(find_field('Percentage').value).to eq '100.0'

    click_on 'Setup Entity'

    company = Company.last!
    expect(company).to be_setup
    expect(company.customer_managed?).to be true
    expect(company.customer_owned?).to be false
    expect(company.owners).to eq [owner]
  end

  context 'existing company' do
    let!(:company) { create(:company, customer_managed: true) }

    scenario 'a user edits the company name' do
      visit manage_company_path(company)

      click_actions
      click_action_item 'Edit'

      fill_in 'Name', with: (new_name = 'New Name')

      click_on 'Update'

      expect(page).to have_content(/successful/i)

      expect(company.reload.name).to eq(new_name)
    end
  end

  describe 'when configuring electronic deposit account', :vcr do
    let(:company) { create(:company, customer_managed: true) }

    before do
      create(:merchant_account, :zeamster, :ach_credit)
    end

    context 'when there is no existing account' do
      before do
        visit manage_company_path(company)
      end

      it 'does not show any bank account' do
        expect(page).to have_content('None')
        expect(page).to have_css('a.item', text: 'Configure')
      end

      it 'allows user to configure electronic deposit account' do
        expect(page).to have_content('None')
        expect(page).to have_css('a.item', text: 'Configure')

        click_on 'Configure'

        within_modal do
          fill_in 'Account Number', with: '**********'
          fill_in 'Routing Number', with: '*********'

          click_on 'Save'
        end

        expect(page).to have_content('Bank Account Added Successfully')
        expect(page).to have_content(
          "Added by #{manager.name} on #{Time.zone.now.to_fs(:short_date)}"
        )
        expect(page).to have_css('a', text: manager.name)
      end
    end

    context 'when there is an existing account' do
      let!(:bank_account) { create(:bank_account, owner: company, customer_managed: false) }

      context 'when a property manager set up the bank account' do
        it 'displays property manager name with link' do
          bank_account.audits.first.update!(user: manager)

          visit manage_company_path(company)

          expect(page).to have_content(
            "Added by #{manager.name} on #{Time.zone.now.to_fs(:short_date)}"
          )
          expect(page).to have_css('a', text: manager.name)

          click_on manager.name

          expect(page).to have_content(manager.name)
        end
      end

      context 'when an owner set up the bank account' do
        it 'displays owner name with link' do
          owner = Owner.first
          bank_account.audits.first.update!(user: owner)

          visit manage_company_path(company)

          expect(page).to have_content(
            "Added by #{owner.name} on #{Time.zone.now.to_fs(:short_date)}"
          )
          expect(page).to have_css('a', text: owner.name)

          click_on owner.name

          expect(page).to have_content(owner.name)
        end
      end

      context 'when an vendor set up the bank account' do
        it 'displays vendor name with link' do
          vendor = create(:vendor)

          bank_account.audits.first.update!(user: vendor)

          visit manage_company_path(company)

          expect(page).to have_content(
            "Added by #{vendor.name} on #{Time.zone.now.to_fs(:short_date)}"
          )
          expect(page).to have_css('a', text: vendor.name)

          click_on vendor.name

          expect(page).to have_content(vendor.name)
        end
      end

      context 'when an admin set up the bank account' do
        it 'displays admin user name without link' do
          admin = AdminUser.first
          bank_account.audits.first.update!(user: admin)

          visit manage_company_path(company)

          expect(page).to have_content(
            "Added by Revela Support on #{Time.zone.now.to_fs(:short_date)}"
          )
          expect(page).to have_no_css('a', text: 'Revela Support')
        end
      end
    end
  end
end
