require 'rails_helper'

RSpec.describe 'onboarding assignments index', :js, capybara_login: :property_manager do
  let(:onboarding) { create(:member_onboarding_configuration, :information_collection) }

  let!(:completed_assignment1) do
    create(:member_onboarding_assignment, :completed, configuration: onboarding)
  end
  let!(:completed_assignment2) do
    create(:member_onboarding_assignment, :completed, configuration: onboarding)
  end

  let!(:pending) { create(:member_onboarding_assignment, configuration: onboarding) }

  let(:assignments) { onboarding.member_assignments }

  before do
    visit onboarding_assignments_path(onboarding)
  end

  context 'when sorting' do
    it 'sorts by name' do
      tenants = assignments.map(&:tenant).sort_by(&:last_name)

      sort_index 'Name'
      expect(tenants.first.name).to appear_before(tenants.last.name)

      sort_index 'Name', direction: :descending
      expect(tenants.last.name).to appear_before(tenants.first.name)
    end

    xit 'sorts by property' do
      # not implemented yet
    end

    it 'sorts by status' do
      sort_index 'Status'
      expect(completed_assignment1.tenant.name).to appear_before(pending.tenant.name)

      sort_index 'Status', direction: :descending
      expect(pending.tenant.name).to appear_before(completed_assignment1.tenant.name)
    end

    it 'sorts by assigned' do
      completed_assignment2.update(created_at: completed_assignment1.created_at + 1.day)

      sort_index 'Assigned'
      expect(completed_assignment1.tenant.name).to appear_before(completed_assignment2.tenant.name)

      sort_index 'Assigned', direction: :descending
      expect(completed_assignment2.tenant.name).to appear_before(completed_assignment1.tenant.name)
    end

    it 'sorts by completed' do
      completed_assignment2.update(created_at: completed_assignment1.created_at + 1.day)

      sort_index 'Completed'
      expect(completed_assignment1.tenant.name).to appear_before(pending.tenant.name)
      expect(completed_assignment1.tenant.name).to appear_before(completed_assignment2.tenant.name)

      sort_index 'Completed', direction: :descending
      expect(completed_assignment1.tenant.name).to appear_before(pending.tenant.name)
      expect(completed_assignment2.tenant.name).to appear_before(completed_assignment1.tenant.name)
    end
  end

  context 'when filtering' do
    it 'filters by search' do
      expect(page).to have_content(assignments.first.tenant.name)
      expect(page).to have_content(assignments.second.tenant.name)

      fill_in 'Search...', with: assignments.first.tenant.name

      expect(page).to have_no_content(assignments.second.tenant.name)
      expect(page).to have_content(assignments.first.tenant.name)
    end

    it 'filters by assigned_from date' do
      completed_assignment2.update(created_at: completed_assignment1.created_at + 1.day)

      date = completed_assignment1.created_at.to_date - 1.day

      expect(page).to have_content(completed_assignment1.tenant.name)
      expect(page).to have_content(completed_assignment2.tenant.name)

      filter_date 'Assigned From', date: date

      expect(page).to have_content(completed_assignment1.tenant.name)
      expect(page).to have_content(completed_assignment2.tenant.name)

      visit onboarding_assignments_path(onboarding)

      date = completed_assignment1.created_at.to_date + 1.day

      filter_date 'Assigned From', date: date

      expect(page).to have_no_content(completed_assignment1.tenant.name)
      expect(page).to have_content(completed_assignment2.tenant.name)
    end

    it 'filters by assigned_to date' do
      completed_assignment2.update(created_at: completed_assignment1.created_at + 1.day)

      date = completed_assignment1.created_at.to_date - 1.day

      expect(page).to have_content(completed_assignment1.tenant.name)
      expect(page).to have_content(completed_assignment2.tenant.name)

      filter_date 'Assigned Until', date: date

      expect(page).to have_no_content(completed_assignment1.tenant.name)
      expect(page).to have_no_content(completed_assignment2.tenant.name)

      visit onboarding_assignments_path(onboarding)

      date = completed_assignment1.created_at.to_date

      filter_date 'Assigned Until', date: date

      expect(page).to have_content(completed_assignment1.tenant.name)
      expect(page).to have_no_content(completed_assignment2.tenant.name)
    end

    it 'filters by tag' do
      tag = create(:tag, taggable_type: 'Agreements::SimpleAgreement')
      tag2 = create(:tag, taggable_type: 'Agreements::SimpleAgreement')
      create(:simple_agreement, :with_property).memberships.each do |membership|
        membership.tenant = completed_assignment1.tenant
        membership.save!
      end

      Agreements::SimpleAgreement.first.tags << tag
      Agreements::SimpleAgreement.first.save!

      visit onboarding_assignments_path(onboarding)

      expect(page).to have_content(completed_assignment1.tenant.name)

      filter_index 'Membership Tag', with: tag.tag

      expect(page).to have_content(completed_assignment1.tenant.name)
      expect(page).to have_no_content(completed_assignment2.tenant.name)

      visit onboarding_assignments_path(onboarding)

      filter_index 'Membership Tag', with: tag2.tag
      expect(page).to have_no_content(completed_assignment1.tenant.name)
    end

    it 'filters by status' do
      expect(page).to have_content(completed_assignment1.tenant.name)
      expect(page).to have_content(pending.tenant.name)

      filter_index 'Status', with: 'Pending'

      expect(page).to have_no_content(completed_assignment1.tenant.name)
      expect(page).to have_content(pending.tenant.name)

      visit onboarding_assignments_path(onboarding)

      filter_index 'Status', with: 'Completed'

      expect(page).to have_no_content(pending.tenant.name)
      expect(page).to have_content(completed_assignment1.tenant.name)
    end

    it 'filters by property' do
      property = create(:property)
      property2 = create(:property)

      create(:simple_agreement, :with_property, property: property, company: property.company).memberships.each do |membership|
        membership.tenant = completed_assignment1.tenant
        membership.save!
      end
      create(:simple_agreement, :with_property, property: property2, company: property2.company).memberships.each do |membership|
        membership.tenant = completed_assignment2.tenant
        membership.save!
      end

      create(:member_onboarding_property_membership, configuration: onboarding, property: property, enhanced: true)
      create(:member_onboarding_property_membership, configuration: onboarding, property: property2, enhanced: true)

      visit onboarding_assignments_path(onboarding)

      expect(page).to have_content(completed_assignment1.tenant.name)
      expect(page).to have_content(completed_assignment2.tenant.name)

      filter_index 'Property', with: property.name

      expect(page).to have_content(completed_assignment1.tenant.name)
      expect(page).to have_no_content(completed_assignment2.tenant.name)

      visit onboarding_assignments_path(onboarding)

      filter_index 'Property', with: property2.name

      expect(page).to have_no_content(completed_assignment1.tenant.name)
      expect(page).to have_content(completed_assignment2.tenant.name)
    end
  end

  context 'when unassigning' do
    it 'allows unassigning a single member' do
      expect(page).to have_content(pending.tenant.name)
      sleep 5

      click_on 'Unassign', match: :first

      within_modal do
        click_on 'Yes, Unassign'
      end

      expect(page).to have_content('Member successfully unassigned')
      sleep 5
      expect(page).to have_no_content(pending.tenant.name)
    end

    it 'allows bulk unassigning' do
      pending2 = create(:member_onboarding_assignment, configuration: onboarding)

      visit onboarding_assignments_path(onboarding)

      expect(page).to have_content(pending.tenant.name)
      expect(page).to have_content(pending2.tenant.name)

      select_row pending.tenant.name
      select_row pending2.tenant.name

      click_on_batch_action_button 'Unassign'

      within_modal do
        click_on 'Yes, Unassign'
      end

      expect(page).to have_content('2 members successfully unassigned')
      expect(page).to have_no_content(pending.tenant.name)
      expect(page).to have_no_content(pending2.tenant.name)
    end

    it 'does not allow unassigning a completed member' do
      expect(page).to have_content(pending.tenant.name)
      expect(page).to have_content(completed_assignment1.tenant.name)

      select_row pending.tenant.name
      select_row completed_assignment1.tenant.name

      click_on_batch_action_button 'Unassign'

      within_modal do
        click_on 'Yes, Unassign'
      end

      expect(page).to have_content('1 member successfully unassigned')
      expect(page).to have_no_content(pending.tenant.name)
      expect(page).to have_content(completed_assignment1.tenant.name)
    end

    it 'displays an error when unassigning a completed member' do
      expect(page).to have_content(pending.tenant.name)
      pending.update!(completed_at: Time.zone.now)

      click_on 'Unassign', match: :first

      expect(page).to have_content('Cannot unassign a member from a completed assignment')
      expect(page).to have_content(pending.tenant.name)
    end
  end
end
