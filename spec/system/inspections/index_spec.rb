require 'rails_helper'

RSpec.describe 'inspections index', :elasticsearch, :js,
               capybara_login: :property_manager do
  let!(:inspection_one) do
    create(:inspection, :completed, created_at: 3.weeks.ago)
  end

  let!(:inspection_two) do
    create(:inspection)
  end

  let!(:inspections) { [inspection_one, inspection_two] }

  let!(:tenant) { perform_enqueued_jobs { create(:tenant) } }
  let!(:vendor) { perform_enqueued_jobs { create(:vendor) } }

  let!(:unassigned_inspection) { create(:inspection, assigned_to: nil) }
  let!(:assigned_inspection) { create(:inspection, assigned_to: tenant) }

  before { visit operations_inspections_path }

  describe 'sorting' do
    scenario 'by name' do
      sort_index 'Inspection'

      names = inspections.map(&:name).sort

      expect(names.first).to appear_before(names.last)
    end

    scenario 'by created_at' do
      sort_index 'Created'

      names = inspections.sort_by(&:created_at).map(&:name)

      expect(names.first).to appear_before(names.last)
    end

    scenario 'by completed_at' do
      sort_index 'Completed'

      names = inspections.map(&:name)

      expect(names.first).to appear_before(names.last)
    end

    scenario 'by template' do
      sort_index 'Template'

      names = inspections.sort_by { |i| i.template.name }.map(&:name)

      expect(names.first).to appear_before(names.last)
    end
  end

  describe 'filtering' do
    scenario 'by name' do
      fill_in 'Search...', with: inspection_one.name

      expect(page).to have_no_content(inspection_two.name)
      expect(page).to have_content(inspection_one.name)
    end

    scenario 'by status' do
      filter_index 'Status', with: 'Completed'

      expect(page).to have_no_content(inspection_two.name)
      expect(page).to have_content(inspection_one.name)
    end

    scenario 'by template' do
      filter_index 'Template', with: inspection_one.template.name

      expect(page).to have_no_content(inspection_two.name)
      expect(page).to have_content(inspection_one.name)
    end

    scenario 'by property' do
      filter_index 'Property', with: inspection_one.property.name

      expect(page).to have_no_content(inspection_two.name)
      expect(page).to have_content(inspection_one.name)
    end

    scenario 'by more' do
      filter_index 'More', with: 'Unassigned'

      expect(page).to have_no_content(assigned_inspection.name)
      expect(page).to have_content(unassigned_inspection.name)
    end

    describe 'filtering by assignee' do
      let!(:tenant_inspection) { create(:inspection, assigned_to: tenant) }
      let!(:vendor_inspection) { create(:inspection, assigned_to: vendor) }

      scenario 'filters by Tenant' do
        filter_index_search 'Assignee', with: tenant.name

        expect(page).to have_content(tenant_inspection.name)
        expect(page).to have_no_content(vendor_inspection.name)
      end

      scenario 'filters by Vendor' do
        filter_index_search 'Assignee', with: vendor.name

        expect(page).to have_content(vendor_inspection.name)
        expect(page).to have_no_content(tenant_inspection.name)
      end
    end

    context 'when archiving' do
      it 'archives multiple selected inspections' do
        expect(page).to have_content(inspection_one.name)
        expect(page).to have_content(inspection_two.name)

        select_row inspection_one.name
        select_row inspection_two.name

        click_on_batch_action_button('Archive')

        within_modal do
          expect(page).to have_content 'Bulk Archiving'
          expect(page).to have_content '2 Inspections Selected'
          expect(page).to have_content(/archive the selected inspections\?/i)
          click_button 'Submit'
        end

        expect(page).to have_content(/2 Inspections Archived Successfully/i)
        expect(inspection_one.reload).to be_archived
        expect(inspection_two.reload).to be_archived
      end
    end
  end

  describe 'Bulk Archiving' do
    before do
      create(:inspectify_order, inspection_report: inspection_one)
    end

    scenario 'with only non partner inspection' do
      select_row inspection_two.name

      click_on_batch_action_button 'Archive'

      within_modal do
        expect(page).to have_content '1 Inspection Selected'
        expect(page).to have_no_content 'Please note that any vendor assignments that have already started'

        click_button 'Submit'
      end

      expect(page).to have_content(/1 Inspection Archived Successfully/i)

      expect(inspection_two.reload).to be_archived
      expect(inspection_one.reload).not_to be_archived
    end

    scenario 'with only non partner inspection' do
      select_row inspection_one.name
      select_row inspection_two.name

      click_on_batch_action_button 'Archive'

      within_modal do
        expect(page).to have_content '2 Inspections Selected'
        expect(page).to have_content 'Please note that any vendor assignments'

        click_button 'Submit'
      end

      expect(page).to have_content(/2 Inspections Archived Successfully/i)

      expect(inspection_one.reload).to be_archived
      expect(inspection_two.reload).to be_archived
    end
  end
end
