require 'rails_helper'

RSpec.describe 'submitting inspections', :js,
               capybara_login: :property_manager do
  scenario 'a user submits an inspection' do
    inspection = create(:inspection_record).report

    visit operations_inspection_path(inspection)

    click_on 'Next'

    fill_in 'Overall Comments', with: (comments = 'My Comments')

    click_on 'Submit'

    expect(page).to have_content(/successful/i)

    inspection.reload

    expect(inspection).to be_completed
    expect(inspection.comments).to eq(comments)
  end
end
