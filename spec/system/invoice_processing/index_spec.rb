require 'rails_helper'

RSpec.describe 'invoice processing action index', :js,
               capybara_login: :invoice_processor do
  let!(:dte_invoice_processing_email) do
    create(:invoice_processing_email, subject: 'DTE Bill')
  end

  before { visit invoice_processing_emails_path }

  describe 'index' do
    scenario 'a user accesses the webpage' do
      expect(page).to have_content(dte_invoice_processing_email.subject)
    end
  end
end
