require 'rails_helper'

RSpec.describe 'project task assignments', :elasticsearch, :js,
               capybara_login: :property_manager do
  scenario 'a user assigns a task to another user' do
    task = create(:task, :todo_list)
    project = task.project
    todo = create(:todo)
    todo_list = todo.todo_list
    todo_list.update!(parent: task)

    assignee = perform_enqueued_jobs { create(:property_manager) }

    # Need ES index
    _vendor = perform_enqueued_jobs { create(:vendor) }

    visit operations_project_task_path(project, task)

    click_actions
    click_action_item 'Assign'

    perform_enqueued_jobs do
      within_modal do
        search_for assignee.name, in: 'User'

        click_on 'Invite'
      end

      expect(page).to have_content(/successful/i)
    end

    expect(task.reload.assigned_members).to eq([assignee])
    expect(todo.reload.assigned_to).to eq(assignee)

    notification = assignee.notifications.last!
    expect(notification.resource).to eq(task)
  end
end
