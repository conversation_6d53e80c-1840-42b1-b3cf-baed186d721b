require 'rails_helper'
require_relative '../../shared/electronic_signatures_context'

RSpec.describe 'electronic signature history', :js,
               capybara_login: :property_manager do
  include_context 'electronic signatures'

  let!(:document) { create(:lease) }

  let!(:unexecuted_document) do
    create(:document,
           parent: document,
           template_options: {
             template_type: :unexecuted_lease, lease_id: document.id
           })
  end

  let!(:executed_document) { nil }

  let!(:primary_request) do
    create(:electronic_signature, document: document)
  end

  let!(:countersignature_request) do
    create(:electronic_signature, :countersigner, document: document)
  end

  before { visit leasing_lease_path(document) }

  scenario 'a user sees a link to the unexecuted document' do
    expect(history_panel).to have_link(
      'Unexecuted document',
      href: document.unexecuted_lease_document.direct_upload_url
    )
  end

  scenario 'a user sees that a signature is pending for a user' do
    expect(history_panel).to have_content(/pending/i)
  end

  scenario 'a user sees that countersigning is awaiting primary signers' do
    expect(history_panel).to have_content(/awaiting primary signatures/i)
  end

  describe 'a completed signature request' do
    let!(:primary_request) do
      create(:electronic_signature, :signed, document: document)
    end

    scenario 'a user sees the signature request date' do
      expect(history_panel).to have_content(
        primary_request.created_at.to_fs(:human_datetime)
      )
    end

    scenario 'a user sees the signature date' do
      expect(history_panel).to have_content(
        primary_request.signed_at.to_fs(:human_datetime)
      )
    end

    scenario 'a user sees the signature email' do
      expect(history_panel).to have_content(primary_request.email)
    end

    scenario 'a user sees the signature ip address' do
      expect(history_panel).to have_content(primary_request.ip_address)
    end

    scenario 'a user sees the signature name' do
      expect(history_panel).to have_content(primary_request.full_name)
    end

    context 'an executed request' do
      let!(:countersignature_request) do
        create(:electronic_signature,
               :countersigner,
               :signed,
               document: document)
      end

      let!(:executed_document) do
        create(:document,
               parent: document,
               template_options: {
                 template_type: :executed_lease,
                 lease_id: document.id
               })
      end

      scenario 'a user sees a link to the executed document' do
        expect(history_panel).to have_link(
          'Executed document',
          href: document.executed_lease_document.direct_upload_url
        )
      end
    end
  end
end
