require 'rails_helper'

RSpec.describe ContactForm, type: :model do
  describe 'validations' do
    it { is_expected.to validate_presence_of(:first_name) }

    it { is_expected.to validate_presence_of(:last_name) }

    it { is_expected.to validate_presence_of(:email) }

    it { is_expected.to validate_presence_of(:message) }

    it { is_expected.to validate_length_of(:message).is_at_most(1000) }
  end
end
