require 'rails_helper'
require_relative '../../shared/owner_payments_context'

RSpec.describe Owners::PaymentsController, devise_login: :owner do
  include_context 'owner payments'

  let(:params) { { company_id: owned_entity.id } }

  describe 'GET #new' do
    it 'returns http success' do
      get :new, params: params
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    context 'successful' do
      it 'redirects to the invoices page' do
        payment = create(:payment)

        allow_any_instance_of(
          PaymentProcessing::CreateElectronicPayment
        ).to receive(:call) do
          OpenStruct.new(successful?: true, payment: payment)
        end

        post :create, params: params

        expect(response).to redirect_to(owners_invoices_path)
      end
    end

    context 'unsuccessful' do
      it 'renders the new template' do
        allow_any_instance_of(
          PaymentProcessing::CreateElectronicPayment
        ).to receive(:call) do
          OpenStruct.new(successful?: false, payment: Payment.new)
        end

        post :create, params: params

        expect(response).to render_template(:new)
      end
    end
  end
end
