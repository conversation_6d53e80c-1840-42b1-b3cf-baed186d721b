require 'rails_helper'

RSpec.describe Telephony::ProxyCallbacksController do
  let(:payload) do
    {
      outboundResourceStatus: 'sent',
      outboundResourceType: 'message',
      interactionDateUpdated: '2020-06-01T19:23:09.446Z',
      interactionData: '{"body":"My Messge 0"}',
      interactionDateCreated: '2020-06-01T19:23:09Z',
      interactionServiceSid: 'KSdc833993a92c1dbed69fdf00b45d4daa',
      outboundParticipantSid: 'KP862d371f68f51be5bfafe9b8f3e88b24',
      interactionType: 'Message',
      interactionAccountSid: 'ACdc3d5fb248150ff43cb751d67c888105',
      outboundResourceSid: 'SM03f4d3ea909f2942e3134f366820684c',
      outboundResourceUrl: 'https://api.twilio.com/2010-04-01/Accounts/ACdc3d5fb248150ff43cb751d67c888105/Messages/SM03f4d3ea909f2942e3134f366820684c.json',
      interactionSessionSid: 'KC743fbdef8ec453b927ed2846469e8dc8',
      interactionSid: 'KIcb52d342e1f58d3c7f6434f308bf7240'
    }
  end

  let(:user) { create(:property_manager) }
  let(:vendor_assignment) { create(:vendor_assignment, created_by: user) }
  let(:vendor) { vendor_assignment.vendor }
  let(:tenant) do
    ticket = vendor_assignment.maintenance_ticket
    tenant = ticket.opened_by
    ticket.update!(tenant: ticket.opened_by)
    tenant
  end
  let!(:session) do
    create(
      :telephony_twilio_proxy_session,
      resource: vendor_assignment,
      sid: 'KC743fbdef8ec453b927ed2846469e8dc8',
      participant_one: vendor,
      participant_one_sid: '1234',
      participant_two: tenant,
      participant_two_sid: 'KP862d371f68f51be5bfafe9b8f3e88b24'
    )
  end

  describe 'POST #create' do
    it 'processes the callback job' do
      perform_enqueued_jobs do
        post :create, params: payload
      end

      notification = Notification.last!

      expect(notification.user).to eq(user)
    end
  end
end
