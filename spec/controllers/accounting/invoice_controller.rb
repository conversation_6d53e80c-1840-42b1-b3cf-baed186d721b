#
# Generic behaviour for an invoice controller, such as showing and destroying.
# Used to test Accounting::Payables:: and Accounting::Receivables::
# InvoicesControllers.
#

RSpec.shared_examples 'an accounting invoice controller' do
  context 'exisiting invoice' do
    let(:params) { { id: invoice.id } }

    describe 'GET #show' do
      it 'returns http success' do
        get :show, params: params

        expect(response).to have_http_status(:success)
      end
    end

    describe 'DELETE #destroy' do
      it 'redirects to the index' do
        delete :destroy, params: params

        expect(response).to redirect_to(index_path)
      end
    end

    describe 'GET #mark_paid' do
      context 'successful' do
        it 'renders the invocie' do
          get :mark_paid, params: params
          expect(response).to have_http_status(:redirect)
        end
      end

      context 'unsuccessful' do
        before { create(:invoice_payment, invoice: invoice) }

        it 'redirects to the invoice' do
          get :mark_paid, params: params
          expect(response).to have_http_status(:redirect)
        end
      end
    end

    describe 'GET #edit' do
      it 'returns http success' do
        get :edit, params: params
        expect(response).to have_http_status(:success)
      end
    end

    describe 'PUT #update' do
      it 'redirects to the invoice' do
        new_date = Time.zone.tomorrow

        put :update, params: {
          id: invoice.id,
          invoice: { post_date: new_date }
        }

        expect(response).to redirect_to(show_path[invoice])

        expect(invoice.reload.post_date).to eq(new_date)
      end
    end
  end
end
