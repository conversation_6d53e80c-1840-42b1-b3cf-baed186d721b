require 'rails_helper'

RSpec.describe Leasing::PipelinesController,
               devise_login: :property_manager do
  describe 'GET #leads.json' do
    it 'returns http success' do
      get :leads, format: :json
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #applicants.json' do
    it 'returns http success' do
      get :applicants, format: :json
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #expiring.json' do
    it 'returns http success' do
      get :expiring, format: :json
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #waitlist.json' do
    it 'returns http success' do
      get :expiring, format: :json
      expect(response).to have_http_status(:success)
    end
  end
end
