require 'rails_helper'

RSpec.describe Leasing::Guest<PERSON>ardsController,
               devise_login: :property_manager do
  describe 'GET #new' do
    it 'returns http success' do
      get :new
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    before do
      allow(GuestCard::Create).to receive(:call) { result }
      post :create, params: {}
    end

    context 'successful' do
      let(:guest_card) { create(:guest_card) }

      let(:result) { OpenStruct.new(successful?: true, guest_card: guest_card) }

      it 'redirects to the new lead' do
        expect(response).to redirect_to(leasing_lead_path(guest_card.tenant))
      end
    end

    context 'unsuccessful' do
      let(:result) { OpenStruct.new(successful?: false, errors: []) }

      it 'renders the new template' do
        expect(response).to render_template(:new)
      end
    end
  end

  describe 'GET #show' do
    it 'returns http success' do
      guest_card = create(:guest_card)

      get :show, params: { id: guest_card.id }

      expect(response).to have_http_status(:success)
    end
  end
end
