require 'rails_helper'

RSpec.describe Api::V1::SessionsController do
  let(:user) { create(:property_manager) }

  describe 'POST #create' do
    before { post :create, params: params }

    context 'with valid credentials' do
      let(:params) { { email: user.email, password: user.password } }

      it 'retuns http success' do
        expect(response).to have_http_status(:success)
      end

      it 'renders an auth token with user id' do
        token = JSON.parse(response.body)['auth_token']

        payload = JWT.decode(token, Rails.application.secrets.secret_key_base)

        expect(payload.dig(0, 'user_id')).to eq(user.id)
      end
    end

    context 'with invalid credentials' do
      let(:params) { { email: user.email, password: 'incorrect' } }

      it 'returns unauthorized' do
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'GET #show' do
    before do
      request.headers['HTTP_AUTHORIZATION'] = "Token #{token}"
      get :show
    end

    describe 'the response' do
      subject { response }

      context 'without a token' do
        let(:token) { nil }

        it { is_expected.to have_http_status(:unauthorized) }
      end

      context 'with an expired token' do
        let(:token) do
          travel_to(10.weeks.ago) do
            Api::V1::AuthToken.encode(user)
          end
        end

        it { is_expected.to have_http_status(:unauthorized) }
      end

      context 'with a valid token' do
        let(:token) { Api::V1::AuthToken.encode(user) }

        it { is_expected.to have_http_status(:success) }
      end
    end
  end
end
