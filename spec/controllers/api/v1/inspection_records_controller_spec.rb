require 'rails_helper'

RSpec.describe Api::V1::InspectionRecordsController,
               api_login: :property_manager do
  let!(:inspection) { create(:inspection_report) }

  let!(:record) { create(:inspection_record, report: inspection) }

  let!(:question) do
    create(:inspection_question, template: inspection.template)
  end

  let!(:sku_item) { create(:sku_list_item) }

  # Cleared by resubmission
  let!(:old_response) do
    create(:inspection_response, question: question, record: record)
  end

  let(:params) { { id: record.id, inspection_id: inspection.id } }

  describe 'GET #show' do
    it 'returns http success' do
      get :show, params: params
      expect(response).to have_http_status(:success)
    end
  end

  describe 'PUT #update' do
    it 'returns http success' do
      put :update, params: params.merge(
        record: {
          completed_at: Time.zone.now,
          responses_attributes: [
            {
              question_id: question.id,
              body: 'Broken',
              photo_urls: [],
              punch_list_entries_attributes: [
                {
                  sku_list_item_id: sku_item.id,
                  count: 2
                }
              ]
            }
          ]
        }
      )

      expect(response).to have_http_status(:success)

      expect(record.responses.count).to eq(1)
      inspection_response = record.responses.first!
      expect(inspection_response.body).to eq('Broken')
      expect(inspection_response.question).to eq(question)
      punch_list_entry = inspection_response.punch_list_entries.first!
      expect(punch_list_entry.sku_list_item).to eq(sku_item)
      expect(punch_list_entry.count).to eq(2)
    end
  end
end
