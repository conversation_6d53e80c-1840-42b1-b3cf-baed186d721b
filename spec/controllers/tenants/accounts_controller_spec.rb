require 'rails_helper'

RSpec.describe Tenants::AccountsController, devise_login: :tenant do
  let(:lease_membership) { create(:lease_membership) }
  let(:tenant) { lease_membership.tenant }
  let(:lease) { lease_membership.lease }

  describe 'GET #show' do
    it 'returns http success' do
      get :show

      expect(response).to have_http_status(:success)
    end

    context 'when the tenant has an active lease with memberships' do
      before do
        create_list(:insurance_policy, 3, lease: lease)
        get :show
      end

      it 'populates an array of insurance policies' do
        expect(assigns(:insurance_policies)).not_to be_empty
        expect(assigns(:insurance_policies).size).to eq(3)
      end

      it 'returns http success' do
        expect(response).to have_http_status(:success)
      end
    end
  end
end
