<?xml version="1.0" encoding="utf-8"?>
<!-- https://www.zillow.com/static/pdf/feeds/ZillowBrokerFeedsTechnicalSpecV1.0.20.pdf -->
<xs:schema xmlns="http://www.zillow.com" elementFormDefault="qualified" targetNamespace="http://www.zillow.com" id="ZIF" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:simpleType name="yesNoType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Yes" />
      <xs:enumeration value="No" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="zipType">
    <xs:restriction base="xs:string">
      <xs:length value="5" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="stateAbbreviatedType">
    <xs:restriction base="xs:string">
      <xs:pattern value="[A-Z][A-Z]" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="coordinateType">
    <xs:restriction base="xs:decimal">
      <xs:fractionDigits value="6" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="contactForDetailsType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="ContactForDetails" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="Listings">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="Listing" minOccurs="0" maxOccurs="unbounded">
          <xs:complexType>
            <xs:all>
              <!-- Not actually in the ZIF spec -->
              <xs:element name="ID" />
              <xs:element name="Location">
                <xs:complexType>
                  <xs:all>
                    <xs:element name="StreetAddress" type="xs:string" />
                    <xs:element name="UnitNumber" type="xs:string" />
                    <xs:element name="City" type="xs:string" />
                    <xs:element name="State" type="stateAbbreviatedType" />
                    <xs:element name="Zip" type="zipType" />
                    <xs:element name="Lat" type="coordinateType" minOccurs="0" />
                    <xs:element name="Long" type="coordinateType" minOccurs="0" />
                    <xs:element name="DisplayAddress" type="yesNoType" minOccurs="0" />
                  </xs:all>
                </xs:complexType>
              </xs:element>
              <xs:element name="ListingDetails">
                <xs:complexType>
                  <xs:all>
                    <xs:element name="Status">
                      <xs:simpleType>
                        <xs:restriction base="xs:string">
                          <xs:enumeration value="Active" />
                          <xs:enumeration value="Contingent" />
                          <xs:enumeration value="Pending" />
                          <xs:enumeration value="For Rent" />
                          <xs:enumeration value="ComingSoon" />
                        </xs:restriction>
                      </xs:simpleType>
                    </xs:element>
                    <xs:element name="Price" />
                    <xs:element name="ListingUrl" type="xs:anyURI" minOccurs="0" />
                    <xs:element name="MlsId" minOccurs="0" />
                    <xs:element name="MlsName" minOccurs="0" />
                    <xs:element name="ProviderListingId" minOccurs="0" >
                      <xs:simpleType>
                        <xs:restriction base="xs:string">
                          <xs:maxLength value="100" />
                        </xs:restriction>
                      </xs:simpleType>
                    </xs:element>
                    <xs:element name="VirtualTourUrl" type="xs:anyURI" minOccurs="0" />
                    <xs:element name="ListingEmail" type="xs:string" minOccurs="0" />
                    <xs:element name="AlwaysEmailAgent" minOccurs="0">
                      <xs:simpleType>
                        <xs:restriction base="xs:integer">
                          <xs:enumeration value="0" />
                          <xs:enumeration value="1" />
                        </xs:restriction>
                      </xs:simpleType>
                    </xs:element>
                    <xs:element name="ShortSale" type="yesNoType" minOccurs="0" />
                    <xs:element name="REO" type="yesNoType" minOccurs="0" />
                    <xs:element name="ComingSoonOnMarketDate" type="xs:date" minOccurs="0" />
                  </xs:all>
                </xs:complexType>
              </xs:element>
              <xs:element name="RentalDetails" minOccurs="0">
                <xs:complexType>
                  <xs:all>
                    <xs:element name="Availability" minOccurs="0">
                      <xs:simpleType>
                        <xs:union memberTypes="contactForDetailsType xs:date" />
                      </xs:simpleType>
                    </xs:element>
                    <xs:element name="LeaseTerm" minOccurs="0">
                      <xs:simpleType>
                        <xs:restriction base="xs:string">
                          <xs:enumeration value="ContactForDetails" />
                          <xs:enumeration value="Monthly" />
                          <xs:enumeration value="SixMonths" />
                          <xs:enumeration value="OneYear" />
                          <xs:enumeration value="RentToOwn" />
                        </xs:restriction>
                      </xs:simpleType>
                    </xs:element>
                    <xs:element name="DepositFees" type="xs:string" minOccurs="0" />
                    <xs:element name="UtilitiesIncluded" minOccurs="0">
                      <xs:complexType>
                        <xs:all>
                          <xs:element name="Water" type="yesNoType" />
                          <xs:element name="Sewage" type="yesNoType" />
                          <xs:element name="Garbage" type="yesNoType" />
                          <xs:element name="Electricity" type="yesNoType" />
                          <xs:element name="Gas" type="yesNoType" />
                          <xs:element name="Internet" type="yesNoType" />
                          <xs:element name="Cable" type="yesNoType" />
                          <xs:element name="SatTv" type="yesNoType" />
                        </xs:all>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="PetsAllowed" minOccurs="0">
                      <xs:complexType>
                        <xs:all>
                          <xs:element name="NoPets" type="yesNoType" />
                          <xs:element name="Cats" type="yesNoType" />
                          <xs:element name="SmallDogs" type="yesNoType" />
                          <xs:element name="LargeDogs" type="yesNoType" />
                        </xs:all>
                      </xs:complexType>
                    </xs:element>
                  </xs:all>
                </xs:complexType>
              </xs:element>
              <xs:element name="BasicDetails">
                <xs:complexType>
                  <xs:all>
                    <xs:element name="PropertyType">
                      <xs:simpleType>
                        <xs:restriction base="xs:string">
                          <xs:enumeration value="SingleFamily" />
                          <xs:enumeration value="Condo" />
                          <xs:enumeration value="Townhouse" />
                          <xs:enumeration value="Coop" />
                          <xs:enumeration value="MultiFamily" />
                          <xs:enumeration value="Manufactured" />
                          <xs:enumeration value="VacantLand" />
                          <xs:enumeration value="Other" />
                          <xs:enumeration value="Apartment" />
                        </xs:restriction>
                      </xs:simpleType>
                    </xs:element>
                    <xs:element name="Title" type="xs:string" minOccurs="0" />
                    <xs:element name="Description" type="xs:string" minOccurs="0" />
                    <xs:element name="Bedrooms" minOccurs="0" />
                    <xs:element name="Bathrooms" type="xs:decimal" minOccurs="0" />
                    <xs:element name="FullBathrooms" type="xs:integer" minOccurs="0" />
                    <xs:element name="HalfBathrooms" type="xs:integer" minOccurs="0" />
                    <xs:element name="QuarterBathrooms" type="xs:integer" minOccurs="0" />
                    <xs:element name="ThreeQuarterBathrooms" type="xs:integer" minOccurs="0" />
                    <xs:element name="LivingArea" type="xs:integer" minOccurs="0" />
                    <xs:element name="LotSize" type="xs:decimal" minOccurs="0" />
                    <xs:element name="YearBuilt" type="xs:integer" minOccurs="0" />
                  </xs:all>
                </xs:complexType>
              </xs:element>
              <xs:element name="Pictures" minOccurs="0">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="Picture" minOccurs="0" maxOccurs="unbounded">
                      <xs:complexType>
                        <xs:all>
                          <xs:element name="PictureUrl" type="xs:anyURI" />
                          <xs:element name="Caption" type="xs:string" minOccurs="0" />
                        </xs:all>
                      </xs:complexType>
                    </xs:element>
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
              <xs:element name="Agent">
                <xs:complexType>
                  <xs:all>
                    <xs:element name="FirstName" type="xs:string" minOccurs="0" />
                    <xs:element name="LastName" type="xs:string" minOccurs="0" />
                    <xs:element name="EmailAddress" type="xs:string" />
                    <xs:element name="PictureUrl" type="xs:anyURI" minOccurs="0" />
                    <xs:element name="OfficeLineNumber" type="xs:string" minOccurs="0" />
                    <xs:element name="MobilePhoneLineNumber" type="xs:string" minOccurs="0" />
                    <xs:element name="FaxLineNumber" type="xs:string" minOccurs="0" />
                    <xs:element name="LicenseNum" minOccurs="0">
                      <xs:simpleType>
                        <xs:restriction base="xs:string">
                          <xs:maxLength value="25" />
                        </xs:restriction>
                      </xs:simpleType>
                    </xs:element>
                  </xs:all>
                </xs:complexType>
              </xs:element>
              <xs:element name="Office">
                <xs:complexType>
                  <xs:all>
                    <xs:element name="BrokerageName" type="xs:string" minOccurs="0" />
                    <xs:element name="BrokerPhone" type="xs:string" />
                    <xs:element name="BrokerEmail" type="xs:string" minOccurs="0" />
                    <xs:element name="BrokerWebsite" type="xs:anyURI" minOccurs="0" />
                    <xs:element name="StreetAddress" type="xs:string" minOccurs="0" />
                    <xs:element name="UnitNumber" minOccurs="0" />
                    <xs:element name="City" type="xs:string" minOccurs="0" />
                    <xs:element name="State" type="stateAbbreviatedType" minOccurs="0" />
                    <xs:element name="Zip" type="zipType" minOccurs="0" />
                    <xs:element name="OfficeName" type="xs:string" minOccurs="0" />
                    <xs:element name="FranchiseName" type="xs:string" minOccurs="0" />
                  </xs:all>
                </xs:complexType>
              </xs:element>
            </xs:all>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>
