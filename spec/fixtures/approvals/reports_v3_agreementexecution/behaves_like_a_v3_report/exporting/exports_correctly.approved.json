{"version": 2, "title": "Agreement Execution Status", "subheader": "", "header": {"cells": [{"type": "b", "justify": "left", "value": "Created"}, {"type": "b", "justify": "center", "value": "Contact"}, {"type": "b", "justify": "center", "value": "Property"}, {"type": "b", "justify": "center", "value": "Document"}, {"type": "b", "justify": "center", "value": "Signer One"}, {"type": "b", "justify": "center", "value": "Signer Two"}, {"type": "b", "justify": "center", "value": "Countersigner"}, {"type": "b", "justify": "center", "value": "Last Sent At"}, {"type": "b", "justify": "center", "value": "Executed"}]}, "footer": null, "statistics": [{"value": 3, "label": "Agreements"}, {"value": 0, "label": "Executed"}, {"value": 3, "label": "Pending"}], "rows": [{"cells": [{"value": "2021-01-01T00:02:00.000-05:00", "justify": "center"}, {"value": "<PERSON><PERSON> Tenant", "justify": "center"}, {"value": "Sample Property", "justify": "center"}, {"value": "Sample Agreement Type", "justify": "center"}, {}, {}, {}, {"value": null}, {"value": null}]}, {"cells": [{"value": "2021-01-01T00:01:00.000-05:00", "justify": "center"}, {"value": "<PERSON><PERSON> Tenant", "justify": "center"}, {"value": "Sample Property", "justify": "center"}, {"value": "Addendum", "justify": "center"}, {}, {}, {}, {"value": null}, {"value": null}]}, {"cells": [{"value": "2021-01-01T00:00:00.000-05:00", "justify": "center"}, {"value": "<PERSON><PERSON> Tenant", "justify": "center"}, {"value": "Sample Property", "justify": "center"}, {"value": "Lease", "justify": "center"}, {}, {}, {}, {"value": null}, {"value": null}]}]}