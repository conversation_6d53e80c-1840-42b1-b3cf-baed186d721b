{"version": 2, "title": "Aging Receivables", "subheader": "All Results", "header": {"cells": [{"type": "b", "justify": "left", "value": "Name"}, {"type": "b", "justify": "center", "value": "Open"}, {"type": "b", "justify": "center", "value": "Current"}, {"type": "b", "justify": "center", "value": "1 - 30"}, {"type": "b", "justify": "center", "value": "31 - 60"}, {"type": "b", "justify": "center", "value": "61 - 90"}, {"type": "b", "justify": "center", "value": "91 +"}, {"type": "b", "justify": "center", "value": "Prepaid"}, {"type": "b", "justify": "center", "value": "Total"}]}, "footer": {"cells": [{"value": "Total", "type": "b"}, {"value": 3, "type": "b"}, {"value": "$0.00", "type": "b"}, {"value": "$100.00", "type": "b"}, {"value": "$100.00", "type": "b"}, {"value": "$0.00", "type": "b"}, {"value": "$0.00", "type": "b"}, {"value": "$215.00", "type": "b"}, {"value": "-$15.00", "type": "b"}]}, "statistics": [], "rows": [{"cells": [{"value": "<PERSON>"}, {"value": 1}, {"value": "$0.00"}, {"value": "$0.00"}, {"value": "$0.00"}, {"value": "$0.00"}, {"value": "$0.00"}, {"value": "$0.00"}, {"value": "$0.00"}]}, {"cells": [{"value": "<PERSON>"}, {"value": 1}, {"value": "$0.00"}, {"value": "$0.00"}, {"value": "$100.00"}, {"value": "$0.00"}, {"value": "$0.00"}, {"value": "$100.00"}, {"value": "$0.00"}]}, {"cells": [{"value": "<PERSON>"}, {"value": 1}, {"value": "$0.00"}, {"value": "$100.00"}, {"value": "$0.00"}, {"value": "$0.00"}, {"value": "$0.00"}, {"value": "$115.00"}, {"value": "-$15.00"}]}]}