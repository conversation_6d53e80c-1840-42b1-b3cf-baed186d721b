{"version": 2, "title": "Second Nature Monthly Verification", "subheader": "All Results", "header": {"cells": [{"type": "b", "justify": "left", "value": "Charge Date"}, {"type": "b", "justify": "center", "value": "Charge Amount"}, {"type": "b", "justify": "center", "value": "Unit Street Address 1"}, {"type": "b", "justify": "center", "value": "Unit ID"}, {"type": "b", "justify": "center", "value": "Lease ID"}, {"type": "b", "justify": "center", "value": "Tenant"}, {"type": "b", "justify": "center", "value": "Account"}, {"type": "b", "justify": "center", "value": "Invoice"}]}, "footer": null, "statistics": [{"value": 1, "label": "Total Enrolled"}, {"value": 1, "label": "Total Primary Enrolled"}, {"value": 0, "label": "Total Secondary Enrolled"}], "rows": [{"cells": [{"value": "2021-01-01"}, {"value": "$100.00"}, {"value": "1234 Main Street"}, {"value": 1234}, {"value": 5678}, {"value": "<PERSON><PERSON> Tenant"}, {"value": "4500 - Benefit Package"}, {"value": "Benefit Package"}]}]}