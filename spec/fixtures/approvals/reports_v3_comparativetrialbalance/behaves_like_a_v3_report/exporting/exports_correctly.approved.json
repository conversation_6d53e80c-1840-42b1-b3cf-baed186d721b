{"version": 2, "title": "Trial Balance", "subheader": "Sample Entity, LLC", "header": {"cells": [{"type": "b", "justify": "left", "value": ""}, {"type": "b", "justify": "center", "value": "January 2021"}, {"type": "b", "justify": "center", "value": "January 2020"}]}, "footer": null, "statistics": [], "rows": [{"cells": [{"value": "Assets", "type": "h3", "colspan": 3, "depth": 0, "truncate": false}]}, {"cells": [{"value": "Current Asset", "type": "h4", "colspan": 3, "depth": 1, "truncate": false}]}, {"cells": [{"value": "1000 - Accounts Receivable", "depth": 2, "truncate": false}, {"value": "$975.00"}, {"value": "$0.00"}]}, {"cells": [{"value": "Total Current Asset", "depth": 2, "type": "b", "truncate": false}, {"value": "$975.00", "type": "b"}, {"value": "$0.00", "type": "b"}]}, {"cells": [{"value": "Bank", "type": "h4", "colspan": 3, "depth": 1, "truncate": false}]}, {"cells": [{"value": "1500 - Bank Account", "depth": 2, "truncate": false}, {"value": "-$50.00"}, {"value": "$0.00"}]}, {"cells": [{"value": "Total Bank", "depth": 2, "type": "b", "truncate": false}, {"value": "-$50.00", "type": "b"}, {"value": "$0.00", "type": "b"}]}, {"cells": [{"value": "Total Assets", "depth": 1, "type": "b", "truncate": false}, {"value": "$925.00", "type": "b"}, {"value": "$0.00", "type": "b"}]}, {"cells": [{"value": "Liabilities", "type": "h3", "colspan": 3, "depth": 0, "truncate": false}]}, {"cells": [{"value": "Current Liability", "type": "h4", "colspan": 3, "depth": 1, "truncate": false}]}, {"cells": [{"value": "2000 - Accounts Payable", "depth": 2, "truncate": false}, {"value": "-$300.00"}, {"value": "$0.00"}]}, {"cells": [{"value": "Total Current Liability", "depth": 2, "type": "b", "truncate": false}, {"value": "-$300.00", "type": "b"}, {"value": "$0.00", "type": "b"}]}, {"cells": [{"value": "Total Liabilities", "depth": 1, "type": "b", "truncate": false}, {"value": "-$300.00", "type": "b"}, {"value": "$0.00", "type": "b"}]}, {"cells": [{"value": "Equity", "type": "h3", "colspan": 3, "depth": 0, "truncate": false}]}, {"cells": [{"value": "3500 - Owner Draw", "depth": 1, "truncate": false}, {"value": "$50.00"}, {"value": "$0.00"}]}, {"cells": [{"value": "Total Equity", "depth": 1, "type": "b", "truncate": false}, {"value": "$50.00", "type": "b"}, {"value": "$0.00", "type": "b"}]}, {"cells": [{"value": "Revenues", "type": "h3", "colspan": 3, "depth": 0, "truncate": false}]}, {"cells": [{"value": "Rent Income", "type": "h4", "colspan": 3, "depth": 1, "truncate": false}]}, {"cells": [{"value": "4100 - Base Rent", "depth": 2, "truncate": false}, {"value": "-$1,000.00"}, {"value": "$0.00"}]}, {"cells": [{"value": "4200 - Rent Credit", "depth": 2, "truncate": false}, {"value": "$25.00"}, {"value": "$0.00"}]}, {"cells": [{"value": "Total Rent Income", "depth": 2, "type": "b", "truncate": false}, {"value": "-$975.00", "type": "b"}, {"value": "$0.00", "type": "b"}]}, {"cells": [{"value": "Total Revenues", "depth": 1, "type": "b", "truncate": false}, {"value": "-$975.00", "type": "b"}, {"value": "$0.00", "type": "b"}]}, {"cells": [{"value": "Expenses", "type": "h3", "colspan": 3, "depth": 0, "truncate": false}]}, {"cells": [{"value": "5100 - Electrical Expense", "depth": 1, "truncate": false}, {"value": "$300.00"}, {"value": "$0.00"}]}, {"cells": [{"value": "Total Expenses", "depth": 1, "type": "b", "truncate": false}, {"value": "$300.00", "type": "b"}, {"value": "$0.00", "type": "b"}]}], "table_class": "ui very basic selectable single line compact table"}