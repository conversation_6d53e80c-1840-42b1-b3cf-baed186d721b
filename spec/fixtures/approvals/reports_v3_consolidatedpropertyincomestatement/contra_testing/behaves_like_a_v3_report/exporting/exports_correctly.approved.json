{"version": 2, "title": "Consolidated Property Income Statement", "subheader": "Sample Company", "header": {"cells": [{}, {"value": "Sample Property", "justify": "center", "underline": "single"}, {"value": "Totals", "justify": "center", "underline": "single"}]}, "footer": null, "statistics": [], "rows": [{"cells": [{"value": "Revenues", "depth": 0, "type": "b", "colspan": 3}]}, {"cells": [{"value": "Rent Income", "type": "b", "colspan": 3, "depth": 1}]}, {"cells": [{"value": "4100 - Base Rent", "depth": 2, "truncate": false}, {"value": "$1,000.00"}, {"value": "$1,000.00"}]}, {"cells": [{"value": "4200 - Rent Credit", "depth": 2, "truncate": false}, {"value": "-$25.00"}, {"value": "-$25.00"}]}, {"cells": [{"value": "Total Rent Income", "depth": 2}, {"value": "$975.00", "underline": "double", "overline": "single"}, {"value": "$975.00", "underline": "double", "overline": "single"}]}, {"cells": [{"value": "Total Revenues", "depth": 1, "type": "b"}, {"value": "$975.00", "underline": "double", "overline": "single"}, {"value": "$975.00", "underline": "double", "overline": "single"}]}, {"cells": []}, {"cells": [{"value": "Expenses", "depth": 0, "type": "b", "colspan": 3}]}, {"cells": [{"value": "5100 - Electrical Expense", "depth": 1, "truncate": false}, {"value": "$300.00"}, {"value": "$300.00"}]}, {"cells": [{"value": "Total Expenses", "depth": 1, "type": "b"}, {"value": "$300.00", "underline": "double", "overline": "single"}, {"value": "$300.00", "underline": "double", "overline": "single"}]}, {"cells": []}, {"cells": []}, {"cells": [{"value": "Net Income", "type": "b"}, {"value": "$675.00", "underline": "single"}, {"value": "$675.00", "underline": "single"}]}], "table_class": "ui very basic selectable single line compact table"}