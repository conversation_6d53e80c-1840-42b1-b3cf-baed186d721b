{"version": 3, "title": "Move In / Move Out", "subheader": "All Results", "header": {"cells": [{"type": "b", "justify": "left", "value": "Name"}, {"type": "b", "justify": "center", "value": "Property"}, {"type": "b", "justify": "center", "value": "Unit"}, {"type": "b", "justify": "center", "value": "Floorplan"}, {"type": "b", "justify": "center", "value": "Lease Start"}, {"type": "b", "justify": "center", "value": "Lease End"}, {"type": "b", "justify": "center", "value": "Move In"}, {"type": "b", "justify": "center", "value": "Move Out"}, {"type": "b", "justify": "center", "value": "Email"}, {"type": "b", "justify": "center", "value": "Phone"}, {"type": "b", "justify": "center", "value": "Next Scheduled Payment"}]}, "sections": [{"title": "Move In", "rows": [{"cells": [{"value": "<PERSON><PERSON> Tenant", "sort": "Tenant"}, {"value": "Sample Property", "justify": "center"}, {"value": "Sample Unit", "justify": "center"}, {"value": "Sample Floor Plan", "justify": "center"}, {"value": "2020-12-18", "justify": "center"}, {"value": "2021-01-15", "justify": "center"}, {"value": "2020-12-18", "justify": "center"}, {"value": "2021-01-15", "justify": "center"}, {"value": "<EMAIL>"}, {"value": "(*************"}, {"value": null, "justify": "center"}]}, {"cells": [{"value": "<PERSON><PERSON> Tenant", "sort": "Tenant"}, {"value": "Sample Property", "justify": "center"}, {"value": "Sample Unit", "justify": "center"}, {"value": "Sample Floor Plan", "justify": "center"}, {"value": null}, {"value": null}, {"value": "2021-01-08", "justify": "center"}, {"value": null}, {"value": "<EMAIL>"}, {"value": "(*************"}, {"value": null}]}]}, {"title": "Move Out", "rows": [{"cells": [{"value": "<PERSON><PERSON> Tenant", "sort": "Tenant"}, {"value": "Sample Property", "justify": "center"}, {"value": "Sample Unit", "justify": "center"}, {"value": "Sample Floor Plan", "justify": "center"}, {"value": "2020-12-18", "justify": "center"}, {"value": "2021-01-15", "justify": "center"}, {"value": "2020-12-18", "justify": "center"}, {"value": "2021-01-15", "justify": "center"}, {"value": "<EMAIL>"}, {"value": "(*************"}, {"value": null, "justify": "center"}]}]}]}