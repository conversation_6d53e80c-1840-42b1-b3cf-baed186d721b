require 'rails_helper'

RSpec.describe CompaniesQuery, type: :query do
  describe '#by_chart_of_accounts' do
    subject { described_class.new.search.by_chart_of_accounts(chart) }

    # A company that uses a chart of accounts explicitly
    let(:direct_company) { Customer.current.client_entity }

    let(:chart) { direct_company.chart_of_accounts }

    let(:portfolio) do
      configuration = create(:configuration, chart_of_accounts: chart)
      create(:portfolio, configuration: configuration)
    end

    # A company that uses the chart of accounts via its portfolios configuration
    let(:indirect_company) do
      configuration = create(:configuration, chart_of_accounts: chart)
      portfolio = create(:portfolio, configuration: configuration)
      create(:company, portfolio: portfolio)
    end

    # A compnay in the portfolio with an overridden chart of accounts
    let(:override_company) do
      other_chart = create(:chart_of_accounts)
      create(:company, portfolio: portfolio, chart_of_accounts: other_chart)
    end

    # A company in a different potfolio
    let(:other_company) { create(:company) }

    it { is_expected.to include(direct_company) }

    it { is_expected.to include(indirect_company) }

    it { is_expected.not_to include(other_company) }

    it { is_expected.not_to include(override_company) }
  end

  describe '#by_user' do
    context 'with a property manager' do
      let!(:user) { create(:property_manager, top_level: false) }

      let!(:company) { create(:company) }

      it 'includes the company with direct membership' do
        user.property_memberships.create!(target: company)

        results = described_class.new.search.by_user(user)

        expect(results).to include(company)
      end

      it 'includes companies by property access' do
        property = create(:property, company: company)

        user.property_memberships.create!(target: property)

        results = described_class.new.search.by_user(user)

        expect(results).to include(company)
      end

      it 'includes companies by portfolio' do
        portfolio = company.portfolio

        user.property_memberships.create!(target: portfolio)

        results = described_class.new.search.by_user(user)

        expect(results).to include(company)
      end
    end
  end
end
