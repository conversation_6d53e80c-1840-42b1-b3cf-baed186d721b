require 'rails_helper'

RSpec.describe DocumentsQuery do
  describe '#filter_in_portfolio' do
    let(:document) { create(:document, :membership_template) }
    let(:portfolio) { create(:portfolio) }

    context 'when document belongs to portfolio' do
      it 'is included in query result' do
        add_options_to_document({ portfolio_id: portfolio.id })

        result = described_class.new.search.filter_in_portfolio(portfolio.id)
        expect(result).to eq([document])
      end
    end

    context 'when document belongs to property' do
      it 'is included in query result' do
        company = create(:company, portfolio: portfolio)
        property = create(:property, company: company)
        add_options_to_document({ property_id: property.id })

        result = described_class.new.search.filter_in_portfolio(portfolio.id)
        expect(result).to eq([document])
      end
    end

    context 'when document belongs to customer' do
      it 'is included in query result' do
        add_options_to_document({ customer_id: Customer.current.id })

        result = described_class.new.search.filter_in_portfolio(portfolio.id)
        expect(result).to eq([document])
      end
    end

    context 'when document does not belong to portfolio' do
      it 'is not included in query result' do
        result = described_class.new.search.filter_in_portfolio(portfolio.id)
        expect(result).to eq([])
      end
    end

    def add_options_to_document(options)
      document.update!(template_options: document.template_options.merge(options))
    end
  end

  describe '#global' do
    it 'excludes template documents for portfolios and properties' do
      _by_property = create(:document, template_options: { property_id: 1 })
      _by_portfolio = create(:document, template_options: { portfolio_id: 1 })
      by_nothing = create(:document, template_options: { template_type: :lease })

      global_documents = described_class.new.search.global

      expect(global_documents).to contain_exactly(by_nothing)
    end
  end

  describe '#templates' do
    it 'contains documents with some scope key in template_options' do
      _no_template_options = create(:document, template_options: nil)
      _empty_template_options = create(:document, template_options: {})
      customer_template_options = create(:document, template_options: { customer_id: 1 })

      template_documents = described_class.new.search.templates

      expect(template_documents).to contain_exactly(customer_template_options)
    end
  end

  describe '#by_user' do
    let(:document) { create(:document, :membership_template) }
    let(:user) { create(:property_manager) }
    let(:property) { create(:property) }
    let(:access_user) do
      pm = create(:property_manager)
      create(:property_membership,
             user: pm,
             target: property)

      create(:property_membership,
             user: pm,
             target: property.portfolio)

      pm
    end
    let!(:global_doc) { create(:document, template_options: { customer_id: Customer.current.id }) }
    let!(:portfolio_doc) do
      create(:document,
             :membership_template,
             template_options: { customer_id: Customer.current.id,
                                 portfolio_id: property.portfolio.id })
    end
    let!(:property_doc) do
      create(:document,
             :membership_template,
             template_options: { customer_id: Customer.current.id,
                                 property_id: property.id })
    end

    context 'when user has portfolios or properties' do
      it 'returns all templates' do
        result = described_class.new.search.templates.by_user(access_user)

        expect(result).to include(global_doc, portfolio_doc, property_doc)
      end
    end

    context 'when user has no portfolios or properties' do
      it 'only returns global templates' do
        result = described_class.new.search.templates.by_user(user)

        expect(result).to include(global_doc)
        expect(result).not_to include(portfolio_doc, property_doc)
      end
    end
  end
end
