module ActionIndexHelpers
  def filter_index(label, with:)
    within('.action-index') do
      find('.dropdown.item', text: label).click

      within('.visible.menu') do
        find('.item', text: with).click
      rescue Capybara::ElementNotFound
        find('.ui.checkbox', text: with).click
      end
    end
  end

  def filter_index_search(label, with:)
    within('.action-index') do
      find_field(label).set(with)

      within('.search.item > .visible.results') do
        find('.result', text: with).click
      end
    end
  end

  def filter_dropdown_search(label, with:)
    within('.action-index') do
      find('.dropdown.item', text: label).click

      within('.visible.menu') do
        find('.prompt').set(with)

        within('.ui.search > .visible.results') do
          find('.result', text: with).click
        end
      end
    end
  end

  def filter_date(label, date:)
    fill_in label, with: date

    within '.ui.popup.calendar.active' do
      find('td:not(.disabled)', text: /\A#{date.day}\Z/).click
    end
  end

  def sort_index(label, direction: :ascending)
    within('.action-index') do
      find('th', text: label).click
      expect(page).to have_css('th.sorted.ascending')

      if direction == :descending
        find('th', text: label).click
        expect(page).to have_css('th.sorted.descending')
      end
    end
  end

  def select_row(text)
    find('tr', text: text).find('.ui.checkbox').click
  end

  def select_all_rows
    find('.action-index thead .ui.checkbox').click
  end

  def click_on_batch_action_button(text)
    within('.action-index') do
      find('.item', text: text).click
    end
  end
end
