module RBACHelpers
  def mock_permission(manager, return_value, permissions)
    permissions.each do |permission|
      allow(manager.can).to receive(permission).and_return(return_value)
    end

    yield

    permissions.each do |permission|
      allow(manager.can).to receive(permission).and_call_original
    end
  end

  def without_permission(manager, *permissions, &)
    mock_permission(manager, false, permissions, &)
  end

  def with_permission(manager, *permissions, &)
    mock_permission(manager, true, permissions, &)
  end
end
