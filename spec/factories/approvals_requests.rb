FactoryBot.define do
  factory :approvals_request, class: 'Approvals::Request' do
    association :requested_by, factory: :property_manager
    association :approver, factory: :owner
    association :approvable, factory: %i[maintenance_estimate from_maintenance_ticket]
    action { :maintenance_estimate }

    { approved: :approval, rejected: :rejection }.each do |name, attribute|
      trait name do
        after(:build) do |request|
          request.send(
            :"#{attribute}=",
            build(:"approvals_#{attribute}",
                  rule: nil,
                  request: request,
                  approvable: request.approvable)
          )
        end
      end
    end
  end
end
