FactoryBot.define do
  factory :member_onboarding_assignment, class: 'MemberOnboarding::Assignment' do
    association :tenant, factory: :resident

    after(:build) do |assignment|
      if assignment.configuration.nil?
        portfolio = assignment.tenant.current_unit.portfolio
        assignment.configuration = create(:member_onboarding_configuration,
                                          :information_collection,
                                          portfolio: portfolio)
      end
    end

    trait :completed do
      completed_at { Time.zone.now }
    end
  end
end
