FactoryBot.define do
  factory :messaging_email, class: 'Messaging::Email' do
    association :sender, factory: :property_manager
    subject { Faker::Lorem.sentence }
    body { Faker::Lorem.paragraph }
    thread_id { 0 }

    after(:build) do |message|
      next if message.deliveries.any?

      message.deliveries = \
        build_list(:messaging_message_delivery, 1, message: message)
    end
  end
end
