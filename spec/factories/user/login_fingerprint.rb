FactoryBot.define do
  factory :user_login_fingerprint, class: 'User::LoginFingerprint' do
    account { association :property_manager, :without_user_fingerprint }
    customer { Customer.current || Customer.first }
    ip_address { '127.0.0.1' }
    user_agent do
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Apple WebKit/537.36 \
(KHTML, like Gecko) Chrome/********* Safari/537.36'
    end
    location { 'Detroit, Michigan' }
    confirmation_sent_at { 5.minutes.ago }
    confirmed_at { Time.zone.now }
    last_used_at { Time.zone.now }

    trait :public_account do
      customer { nil }
    end

    trait :expired do
      confirmation_sent_at { 15.minutes.ago }
      confirmed_at { nil }
    end

    trait :pristine do
      confirmation_sent_at { nil }
      confirmed_at { nil }
      last_used_at { nil }
    end
  end
end
