FactoryBot.define do
  factory :pay_lease_transaction do
    transient do
      payer { build(:tenant) }
    end

    payment { build(:payment, :tenant_payment, payer: payer) }
    pay_lease_id { '********' }
    status { :approved }
    response_code { 1 }
    response_message { 'All right now' }
    trace_id { SecureRandom.hex(15) }
    merchant_account { build(:merchant_account, :pay_lease, :ach) }
  end
end
