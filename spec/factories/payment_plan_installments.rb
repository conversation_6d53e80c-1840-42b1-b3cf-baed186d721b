FactoryBot.define do
  factory :payment_plan_installment, class: 'PaymentPlan::Installment' do
    payment_plan
    date { Time.zone.today }
    amount { payment_plan.amount / 2 }

    after(:build) do |installment|
      payment_plan = installment.payment_plan
      second = installment.dup
      installment.schedule_payment
      second.schedule_payment
      payment_plan.installments = [installment, second]
    end
  end
end
