FactoryBot.define do
  factory :address do
    # TODO: Simplify
    # Ideally this would just be `address`,
    # or even addressable { build(:vendor, address: instance) },
    # but the former doesn't work and the second doesn't work for attributes_for.
    addressable { build(:vendor, address: instance) }
    line_one { Faker::Address.street_address }
    line_two { Faker::Address.secondary_address }
    city { Faker::Address.city }
    postal_code { Faker::Address.postcode }
    region { Faker::Address.state }
    country { 'United States' }
  end
end
