require 'rails_helper'

RSpec.describe Collections::EvictionVendors<PERSON>ail<PERSON> do
  describe 'assign_eviction_vendor' do
    subject(:mail) do
      described_class.assign_eviction_vendor(
        eviction: eviction, message: message
      )
    end

    let(:eviction) { create(:collections_eviction, :assigned) }

    let(:message) { 'My Message' }

    let(:employee) { eviction.created_by }

    let(:vendor) { eviction.assigned_vendor }

    it 'renders appropriately' do
      eviction.lease.primary_tenant.update!(
        first_name: '<PERSON><PERSON>', last_name: 'Tenant'
      )

      filename = 'eviction-packet-sample-tenant.zip'

      expect(mail.to).to eq([vendor.email])
      expect(mail.from).to eq(['<EMAIL>'])
      expect(mail.reply_to).to eq([employee.email])
      expect(mail.subject).to eq('Eviction Filing for Sample Tenant')
      expect(mail.attachments[filename]).to be_present
      expect(mail.html_part.body).to include(message)
    end
  end
end
