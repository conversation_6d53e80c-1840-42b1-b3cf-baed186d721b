require 'rails_helper'
require_relative '../shared/electronic_signatures_context'

RSpec.describe ElectronicSignaturesMailer do
  include_context 'electronic signatures'

  describe '#request_signature' do
    let(:signature) { create(:electronic_signature) }

    let(:mail) { described_class.request_signature(signature) }

    it 'renders the headers' do
      expect(mail.subject).to eq('Electronic Signature Request')
      expect(mail.to).to eq([signature.email])
      expect(mail.from).to eq(['<EMAIL>'])
      expect(mail.reply_to).to eq([signature.requested_by.email])
    end

    it 'contains the brand' do
      body = mail.html_part.body.to_s
      expect(body).to match(Customer.current.brand.logo_url)
    end

    it 'contains a link to the document' do
      body = Capybara::Node::Simple.new(mail.html_part.body.to_s)

      expect(body).to have_link(
        nil, href: electronic_signature_url(
          signature,
          subdomain: 'alever'
        )
      )
    end
  end
end
