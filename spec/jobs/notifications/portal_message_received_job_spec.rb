require 'rails_helper'

RSpec.describe Notifications::PortalMessageReceivedJob do
  subject(:perform_job) { perform_enqueued_jobs { described_class.perform_later(message) } }

  describe 'when mph' do
    before do
      allow_any_instance_of(described_class).to receive(:marketplace?).and_return(true) # rubocop:disable RSpec/AnyInstance
    end

    # This is the exact PM ids for mph
    let(:client_services) { create(:property_manager, id: 77) }
    let(:owner_services) { create(:property_manager, id: 170) }

    describe 'when sender is an owner' do
      let(:owner) { create(:owner) }
      let(:message) { create(:messaging_email, sender: owner) }

      it 'sends a notification to the owner services' do
        expect { perform_job }.to \
          change { owner_services.notifications.count }
          .by(1)
          .and not_change { client_services.notifications.count }
      end
    end

    describe 'when sender is a tenant' do
      let(:tenant) { create(:resident) }
      let(:message) { create(:messaging_email, sender: tenant) }

      it 'sends a notification to the owner services' do
        expect { perform_job }.to \
          change { client_services.notifications.count }
          .by(1)
          .and not_change { owner_services.notifications.count }
      end
    end

    describe 'when sender is a vendor' do
      let(:vendor) { create(:vendor) }
      let(:message) { create(:messaging_email, sender: vendor, regarding: regarding) }

      describe 'when vendor initiated message' do
        let(:regarding) { nil }

        it 'sends a notification to the client services' do
          expect { perform_job }.to \
            change { client_services.notifications.count }
            .by(1)
            .and not_change { owner_services.notifications.count }
        end
      end

      describe 'when vendor replied to a maintenance ticket' do
        let!(:regarding) do # rubocop:disable RSpec/LetSetup
          create(:maintenance_ticket, opened_by: opened_by, assigned_user: assigned_user)
        end
        let(:assigned_user) { nil }
        let(:opened_by) { create(:property_manager) }

        describe 'when maintenance ticket has an assigned_user' do
          let(:assigned_user) { create(:property_manager) }

          it 'sends a notification to the assigned_user' do
            expect { perform_job }.to \
              change { assigned_user.notifications.count }
              .by(1)
              .and not_change { opened_by.notifications.count }
              .and not_change { client_services.notifications.count }
              .and not_change { owner_services.notifications.count }
          end
        end

        describe 'when maintenance ticket has no assignee and opened by a property manager' do
          it 'sends a notification to the customer representative' do
            expect { perform_job }.to \
              change { opened_by.notifications.count }
              .by(1)
              .and not_change { client_services.notifications.count }
              .and not_change { owner_services.notifications.count }
          end
        end

        describe 'when maintenance ticket has no assignee and opened by a tenant' do
          let(:opened_by) { create(:resident) }

          it 'sends a notification to the customer representative' do
            expect { perform_job }.to \
              change { client_services.notifications.count }
              .by(1)
              .and not_change { owner_services.notifications.count }
          end
        end
      end
    end
  end
end
