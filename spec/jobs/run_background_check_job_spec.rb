require 'rails_helper'

RSpec.describe RunBackgroundCheckJob do
  let(:background_check) { create(:background_check) }

  before do
    allow_any_instance_of(
      BackgroundChecks::RsXmlBuilder
    ).to receive(:to_xml).and_return('the_xml')
  end

  context 'success', vcr: {
    cassette_name: 'send_reference_services'
  } do
    it 'sends a background check' do
      expect(background_check).to be_unsent

      perform_enqueued_jobs do
        described_class.perform_later(background_check)
      end

      expect(background_check.reload).to be_sent
    end

    it 'is idempotent' do
      perform_enqueued_jobs do
        2.times do
          described_class.perform_later(background_check)
        end
      end
    end

    it 'saves the receipt' do
      perform_enqueued_jobs do
        described_class.perform_later(background_check)
      end

      receipt = background_check.reload.receipt
      method = Nokogiri::XML(receipt).xpath('//Method').text
      expect(method).to eq('SEND ORDER')
    end
  end

  context 'failure', vcr: {
    cassette_name: 'send_reference_services_unsuccessfully'
  } do
    it 'marks the background check as failed' do
      perform_enqueued_jobs do
        described_class.perform_later(background_check)
      end

      expect(background_check.reload).to be_failed
    end

    it 'saves the receipt' do
      perform_enqueued_jobs do
        described_class.perform_later(background_check)
      end

      message = 'NO ADDRESS HISTORY TO AUTOPOPULATE FROM.'

      receipt = background_check.reload.receipt
      method = Nokogiri::XML(receipt).xpath('//Message').text
      expect(method).to include(message)

      expect(background_check.message).to include(message)
    end
  end

  context 'with a report link in the receipt', vcr: {
    cassette_name: 'send_reference_services_immediate'
  } do
    subject { background_check.reload }

    before do
      perform_enqueued_jobs do
        described_class.perform_later(background_check)
      end
    end

    it { is_expected.to be_completed }

    its(:external_report_url) do
      is_expected.to eq \
        'https://refserve2.com/webservice/getreport.cfm?ReportID=1234&ReportKey=5678'
    end

    its(:credit_score) { is_expected.to eq(697) }
  end
end
