require 'rails_helper'

RSpec.describe Tunisia::AuthorizedUsers::UpdateEmailCompanyJob do
  let(:company) { create(:company, :tunisia_customer) }
  let(:instance) { described_class.new }
  let(:old_email) { tunisia_authorized_user.email }

  def perform
    instance.perform(new_email:, old_email:, company:)
  end

  describe 'when the user email changes', :vcr do
    before { Tunisia::AuthorizedUsers::BackfillJob.new.perform(company:, dry_run: false) }

    describe 'the replacement request' do
      let!(:primary_tau) { Tunisia::AuthorizedUser.unscoped.find_by(email: '<EMAIL>') }
      let!(:nonprimary_tau) { Tunisia::AuthorizedUser.unscoped.find_by(email: '<EMAIL>') }

      context 'when it is the primary user' do
        let(:tunisia_authorized_user) { primary_tau }
        let(:new_email) { '<EMAIL>' }

        it 'has the new email in the payload', :vcr do
          expect(instance).to receive(:replacement_service).and_wrap_original do |m|
            request_svc = m.call
            request_email = request_svc.request
                                       .dig(:data, :attributes, :contact, :email)
            expect(request_email).to eq new_email
            request_svc
          end
          perform
        end
      end

      context 'when it is an authorized_user user' do
        let(:tunisia_authorized_user) { nonprimary_tau }
        let(:new_email) { '<EMAIL>' }

        it 'has the new email in the payload', :vcr do
          expect(instance).to receive(:replacement_service).and_wrap_original do |m|
            request_svc = m.call
            updated_tau_in_request = request_svc.request
                                                .dig(:data, :attributes, :authorizedUsers)
                                                .find { _1[:email] == new_email}
            expect(updated_tau_in_request).to be_present
            request_svc
          end
          perform
        end
      end
    end

    describe 'outcome based on replacement service response' do
      let(:tunisia_authorized_user) do
        Tunisia::AuthorizedUser.unscoped.find_by(email: '<EMAIL>')
      end
      let(:new_email) { '<EMAIL>' }

      before do
        allow(Tunisia::AuthorizedUsers::ReplaceAll).to \
          receive(:new).and_return(
            instance_double(
              Tunisia::AuthorizedUsers::ReplaceAll,
              call: replacement_response
            )
          )
      end

      context 'replacement response is successful' do
        let(:replacement_response) { OpenStruct.new(successful?: true) }

        it 'updates the tau email' do
          expect { perform }.to \
            change { tunisia_authorized_user.reload.email }.to(new_email)
        end
      end


      context 'and the replace fails' do
        let(:replacement_response) { OpenStruct.new(successful?: false, errors: ['womp']) }

        it 'does not update the tau email' do
          expect { perform }.to \
            raise_error(RuntimeError, 'womp').and not_change { tunisia_authorized_user.reload.email }
        end
      end
    end
  end
end
