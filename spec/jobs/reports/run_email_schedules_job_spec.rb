require 'rails_helper'

RSpec.describe Reports::RunEmailSchedulesJob do
  let!(:email_schedule) do
    schedule = IceCube::Schedule.new(start_time)
    schedule.add_recurrence_rule(IceCube::Rule.daily)
    create(:reports_email_schedule,
           last_ran_at: last_ran_at,
           schedule: schedule)
  end

  let(:start_time) { 5.hours.ago }
  let(:last_ran_at) { nil }

  context 'an email schedule that is not schedule for now' do
    it 'does not enqueue the run job for an email schedule' do
      expect { described_class.perform_now }.to \
        not_enqueue_job(Reports::RunEmailScheduleJob).and \
          not_change { email_schedule.reload.last_ran_at }
    end
  end

  context 'an email schedule that recently ran' do
    let(:start_time) { 1.minute.ago }
    let(:last_ran_at) { 1.minute.ago }

    it 'does not enqueue the run job for an email schedule' do
      expect { described_class.perform_now }.to \
        not_enqueue_job(Reports::RunEmailScheduleJob).and \
          not_change { email_schedule.reload.last_ran_at }
    end
  end

  context 'an email schedule that is scheduled for now' do
    let(:start_time) { 1.minute.ago }

    it 'idempotently enqueues the run job for an email schedule' do
      expect { 3.times { described_class.perform_now } }.to \
        enqueue_job(Reports::RunEmailScheduleJob).with(email_schedule)
    end

    it 'updates the last ran at time of the email schedule' do
      expect { described_class.perform_now }.to \
        change { email_schedule.reload.last_ran_at }
    end
  end
end
