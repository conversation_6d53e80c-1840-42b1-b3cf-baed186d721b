require 'rails_helper'

Rails.describe PayLease::Transaction::Void do
  let(:merchant_account) { create(:merchant_account, :pay_lease, :ach) }
  let(:transaction) do
    create(:pay_lease_transaction, merchant_account: merchant_account)
  end
  let(:payment) { transaction.payment }

  it 'voids a transaction', :vcr do
    res = described_class.call(transaction: transaction)

    expect(res).to be_successful
    expect(res.transaction_id).to eq transaction.pay_lease_id
    expect(res.code).to eq '14'
    expect(res.status).to eq 'Approved'
    expect(res.message).to eq 'This void transaction is complete.'

    expect(transaction.reload.response_code).to eq 14
    expect(transaction).to be_canceled

    expect(payment.reload).to be_canceled
  end
end
