RSpec.shared_examples_for 'tenant payment reports' do
  subject(:report) { described_class.new(user: user, filters: filters) }

  let(:user) { create(:property_manager, top_level: true) }

  let!(:payment) do
    create(:payment, :lease_payment, :unapplied,
           amount: '$100.00',
           description: 'Sample Payment').tap do |payment|
      payment.payer.update!(first_name: '<PERSON><PERSON>', last_name: 'Tenant')
      payment.payee.update!(name: 'Sample Property')
    end
  end

  context 'with an owner filter' do
    before do
      create(:payment, :lease_payment) # Not to show up, filtered out
    end

    let(:owner) do
      create(:owner).tap do |owner|
        create(:ownership, owner: owner, entity: payment.payee.company)
      end
    end

    let(:filters) { OpenStruct.new(owner_id: owner.id) }

    it_behaves_like 'a v3 report'
  end
end
