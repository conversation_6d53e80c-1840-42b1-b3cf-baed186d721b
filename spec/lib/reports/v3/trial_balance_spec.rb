require 'rails_helper'

require_relative 'shared'

require_relative '../../../shared/trust_accounting_context'

RSpec.describe Reports::V3::TrialBalance do
  subject(:report) { described_class.new(user: user, filters: filters) }

  let(:user) { create(:property_manager, top_level: true) }

  let(:filters) { OpenStruct.new(date: Time.zone.today) }

  include_context 'with trust accounting activity'

  it_behaves_like 'a v3 report'
end
