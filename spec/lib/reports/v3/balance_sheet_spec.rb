require 'rails_helper'

require_relative 'shared'

require_relative '../../../shared/trust_accounting_context'

RSpec.describe Reports::V3::BalanceSheet do
  subject(:report) { described_class.new(user: user, filters: filters) }

  let(:user) { create(:property_manager, top_level: true) }

  context 'with trust accounting activity' do
    let(:filters) do
      OpenStruct.new(
        context_id: owner_entity.to_sgid.to_s,
        date: Time.zone.today
      )
    end

    include_context 'with trust accounting activity'

    describe 'the management journal' do
      it_behaves_like 'a v3 report' do
        let(:filters) do
          OpenStruct.new(start_date: export_date,
                         end_date: export_date,
                         basis: 'accrual',
                         context_id: management_company.to_sgid.to_s)
        end
      end
    end

    describe 'the owner journal' do
      it_behaves_like 'a v3 report' do
        let(:filters) do
          OpenStruct.new(start_date: export_date,
                         end_date: export_date,
                         basis: 'accrual',
                         context_id: owner_entity.to_sgid.to_s)
        end
      end
    end

    describe 'when filtering by owner' do
      before do
        allow(ChartOfAccounts).to receive(:where)
          .with(id: anything)
          .and_return(ChartOfAccounts.where(id: management_company.chart_of_accounts.id))
      end

      let(:owner) do
        create(:owner, first_name: 'Sample', last_name: 'Owner').tap do |owner|
          owner.ownerships.create!(entity: owner_entity, ownership_percentage: 100)
        end
      end

      it_behaves_like 'a v3 report' do
        let(:filters) do
          OpenStruct.new(start_date: export_date,
                         end_date: export_date,
                         basis: 'accrual',
                         owner_id: owner.id)
        end
      end
    end
  end
end
