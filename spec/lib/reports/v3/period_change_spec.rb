require 'rails_helper'

require_relative 'shared'

RSpec.describe Reports::V3::PeriodChange do
  subject(:report) { described_class.new(filters: filters, user: user) }

  let(:user) { create(:property_manager, top_level: true) }

  let(:entity) { create(:company, name: 'Sample Entity, LLC') }

  let(:filters) do
    OpenStruct.new(
      context_id: entity.to_sgid.to_s,
      start_date: export_date,
      end_date: export_date
    )
  end

  include_context 'sample activity with contra'

  include_examples 'v3 report balance matrix testing'

  describe 'when filtering by owner' do
    let(:use_account_matrix?) { true }

    let(:owner) do
      create(:owner).tap do |owner|
        owner.ownerships.create!(entity: entity, ownership_percentage: 100)
      end
    end

    it_behaves_like 'a v3 report' do
      let(:filters) do
        OpenStruct.new(start_date: export_date,
                       end_date: export_date,
                       owner_id: owner.id)
      end
    end
  end
end
