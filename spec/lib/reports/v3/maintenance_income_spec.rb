require 'rails_helper'

require_relative 'shared'

RSpec.describe Reports::V3::MaintenanceIncome do
  subject(:report) { described_class.new(user: user) }

  let(:user) { create(:property_manager, top_level: true) }

  before do
    property = create(:property, name: 'Sample Property')
    property.company.update!(name: 'Sample Company')
    property.portfolio.update!(name: 'Sample Portfolio')

    vendor = create(:vendor, name: 'Sample Vendor')

    employee = create(:property_manager,
                      first_name: '<PERSON><PERSON>',
                      last_name: 'Employee')

    work_order = create(:maintenance_ticket,
                        closed_at: Time.zone.today,
                        subject: 'Sample Work Order',
                        property: property,
                        assigned_user: employee)

    invoice = create(:invoice,
                     description: 'Sample Invoice',
                     amount: '$100.00',
                     buyer: Customer.current.client_entity,
                     seller: vendor,
                     linked_maintenance_tickets: [work_order])

    item = invoice.line_items.first
    item.update!(markup_raw: 10, markup_kind: 'percent')
    item.forward_id = property.to_sgid.to_s

    Invoice::Forward.call(invoice)
  end

  it_behaves_like 'a v3 report'
end
