require 'rails_helper'

require_relative 'shared'

RSpec.describe Reports::V3::VendorDirectory do
  subject(:report) { described_class.new(user: user, filters: filters) }

  let(:filters) { OpenStruct.new({}) }
  let(:user) { create(:property_manager, top_level: true) }
  let(:address) do
    build(:address,
          line_one: '657 W 57th St',
          line_two: 'Apt. 674',
          city: 'New New York',
          region: 'NY', postal_code: '10019')
  end

  let(:vendor_contact) do
    build(:vendor_contact,
          first_name: '<PERSON>',
          last_name: 'Farns<PERSON>',
          phone: '+***********',
          email: '<EMAIL>')
  end

  let!(:vendor) do
    create(:vendor,
           name: 'Planet Express',
           address: address,
           business_type: :llc,
           vendor_contacts: [vendor_contact],
           taxpayer_identification: build(:taxpayer_identification,
                                          tin: '*********'))
  end

  it_behaves_like 'a v3 report'
end
