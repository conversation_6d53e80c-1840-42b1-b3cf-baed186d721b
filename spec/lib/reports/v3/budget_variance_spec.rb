require 'rails_helper'

require_relative 'shared'

RSpec.describe Reports::V3::BudgetVariance do
  subject(:report) { described_class.new(user: user, filters: filters) }

  let(:user) { create(:property_manager, top_level: true) }

  let(:filters) {
    OpenStruct.new(
      entity_id: journal.id,
      period: Time.zone.today
    )
  }

  let(:journal) { create(:company, name: 'Sample Journal') }

  before do
    chart = journal.chart_of_accounts

    debit_account = create(:expense_account, tenant: chart, name: 'Sample Expense')
    credit_account = create(:asset_account, tenant: chart)

    create(:journal_entry,
           journal: journal,
           date: Time.zone.today,
           amount: '$100.00',
           debit_account: debit_account,
           credit_account: credit_account)

    # Closing entries should be ignored.
    create(:journal_entry,
           journal: journal,
           date: Time.zone.today,
           amount: '$100.00',
           debit_account: debit_account,
           credit_account: credit_account,
           retained_earnings: true)

    budget = create(:budget, company: journal, property: nil, year: Time.zone.now.year)

    create(:budget_amount, budget: budget, account: debit_account, amount: '$125.00')
  end

  it_behaves_like 'a v3 report'
end
