require 'rails_helper'

require_relative 'shared'

RSpec.describe Reports::V3::InvestorIncomeStatement do
  subject(:report) { described_class.new(user: user, filters: filters) }

  let(:user) { create(:property_manager, top_level: true) }

  let(:entity) { create(:company, name: 'Sample Company') }

  let(:ownership) { create(:ownership, entity: entity) }

  let(:owner) { ownership.owner }

  let(:property) { create(:property, company: entity, name: 'Sample Property') }

  let(:filters) do
    OpenStruct.new(
      owner_id: owner.id,
      start_date: export_date,
      end_date: export_date
    )
  end

  include_context 'sample activity with contra'

  before do
    Plutus::Entry.update_all(property_id: property.id)
  end

  it_behaves_like 'a v3 report'
end
