require 'rails_helper'

def acc(gl, name)
  gl[:accounts].find { |acc| acc[:name] == name }
end

##
# Accounting makes my head hurt. Here's what's happening in here:
#
# Owner contribution - $500
# Rent - $900 - received thru invoice
# Lightbulb - $15 - paid thru invoice
# Transfer - $900 - petty cash --> bank account (tenant paid cash)
# Owner distribution - $310
# Landscaping - $300 - split on labor/material, only paid $158 thru invoice
# Office Supplies - $69.93 - unpaid payable
#
# This is to test the proration and proper cash/accrual calcs for general
# ledger.

RSpec.describe Reports::GeneralLedger do
  around { |example| travel_to(Date.new(2019, 12, 31)) { example.run } }

  let!(:start_date) { Date.new(2019, 1, 1) }
  let!(:end_date) { Date.new(2019, 1, 31) }

  let!(:tenant) { create(:tenant) }
  let!(:owner) { create(:owner) }
  let!(:company) { create(:ownership, owner: owner).entity }
  let!(:property) { create(:property, company: company) }
  let!(:unit) { create(:unit, property: property) }
  let!(:membership) { create(:lease_membership, unit: unit) }
  let!(:tenant) { membership.tenant }
  let!(:vendor) { create(:vendor) }

  let!(:chart) { company.chart_of_accounts }
  let!(:cash_account) { chart.cash_account }
  let!(:bank_account) do
    create(:bank_account, owner: company).tap do |acc|
      chart.accounts << acc.ledger_account
    end
  end
  let!(:accounts_receivable) { chart.accounts_receivable }
  let!(:accounts_payable) { chart.accounts_payable }
  let!(:owner_distribution) { chart.owner_distribution_account }
  let!(:owner_contribution) { chart.owner_contribution_account }
  let!(:rent_income) { chart.rent_income_account }
  let!(:misc_expense) do
    create(:expense_account, tenant: chart, name: 'Miscellaneous')
  end
  let!(:landscaping_labor) do
    create(:expense_account, tenant: chart, name: 'Landscaping Labor')
  end
  let!(:landscaping_material) do
    create(:expense_account, tenant: chart, name: 'Landscaping Material')
  end

  before do
    chart.save!

    Plutus::Entry.create!(
      kind: :manual_entry,
      date: start_date,
      description: 'Beginning Balance',
      debits: [{ account: cash_account, amount: 50_000 }],
      credits: [{ account: owner_contribution, amount: 50_000 }],
      **company.accounting_context.entry_params
    )

    rent_invoice = Invoice.create!(
      invoice_number: 1,
      post_date: start_date + 3.days,
      due_date: start_date + 33.days,
      description: 'Rent',
      buyer: tenant,
      buyer_lease_membership: membership,
      seller: property,
      line_items_attributes: [
        {
          unit_price: Monetize.parse('$900.00'),
          quantity: 1,
          description: 'January Rent',
          receivable_account_id: rent_income.id
        }
      ]
    )

    rent_payment = Payment.create!(
      kind: :check,
      check_number: 1,
      date: start_date + 5.days,
      description: 'January Rent',
      amount: Monetize.parse('$900.00'),
      payer: tenant,
      payer_lease_membership: membership,
      payee: property
    ) do |payment|
      payment.invoice_payments.build(
        invoice: rent_invoice,
        amount: Monetize.parse('$900.00')
      )
    end

    lightbulb_invoice = Invoice.create!(
      invoice_number: 2,
      post_date: start_date + 3.days,
      due_date: start_date + 33.days,
      description: 'Lightbulb',
      buyer: property,
      seller: vendor,
      line_items_attributes: [
        {
          unit_price: Monetize.parse('$15.00'),
          quantity: 1,
          description: 'Lightbulb',
          payable_account_id: misc_expense.id
        }
      ]
    )

    lightbulb_payment = Payment.create!(
      kind: :check,
      check_number: 2,
      date: start_date + 5.days,
      description: 'Lightbulb',
      amount: Monetize.parse('$15.00'),
      debit_bank_account: bank_account,
      payer: property,
      payee: vendor
    ) do |payment|
      payment.invoice_payments.build(
        invoice: lightbulb_invoice,
        amount: Monetize.parse('$15.00')
      )
    end

    Plutus::Entry.create!(
      kind: :manual_entry,
      date: start_date + 9.days,
      description: 'Deposit cash from tenant',
      debits: [{ account: bank_account.ledger_account, amount: 90_000 }],
      credits: [{ account: cash_account, amount: 90_000 }],
      **company.accounting_context.entry_params
    )

    Plutus::Entry.create!(
      kind: :manual_entry,
      date: end_date,
      description: 'Distribution to owner',
      debits: [{ account: owner_distribution, amount: 31_000 }],
      credits: [{ account: cash_account, amount: 31_000 }],
      **company.accounting_context.entry_params
    )

    landscaping_invoice = Invoice.create!(
      invoice_number: 3,
      post_date: start_date + 3.days,
      due_date: start_date + 33.days,
      description: 'Landscaping',
      buyer: property,
      seller: vendor,
      line_items_attributes: [
        {
          unit_price: Monetize.parse('$100.00'),
          quantity: 1,
          description: 'Flowers',
          payable_account_id: landscaping_material.id
        },
        {
          unit_price: Monetize.parse('$200.00'),
          quantity: 1,
          description: 'Labor',
          payable_account_id: landscaping_labor.id
        }
      ]
    )

    landscaping_payment = Payment.create!(
      kind: :check,
      check_number: 3,
      date: start_date + 5.days,
      description: 'Landscaping',
      amount: Monetize.parse('$100.00'),
      debit_bank_account: bank_account,
      payer: property,
      payee: vendor
    ) do |payment|
      payment.invoice_payments.build(
        invoice: landscaping_invoice,
        amount: Monetize.parse('$100.00')
      )
    end
    landscaping_payment = Payment.create!(
      kind: :check,
      check_number: 4,
      date: start_date + 7.days,
      description: 'Landscaping',
      amount: Monetize.parse('$58.00'),
      debit_bank_account: bank_account,
      payer: property,
      payee: vendor
    ) do |payment|
      payment.invoice_payments.build(
        invoice: landscaping_invoice,
        amount: Monetize.parse('$58.00')
      )
    end

    office_supplies_invoice = Invoice.create!(
      invoice_number: 4,
      post_date: start_date + 14.days,
      due_date: start_date + 44.days,
      description: 'Office Supplies',
      buyer: property,
      seller: vendor,
      line_items_attributes: [
        {
          unit_price: Monetize.parse('$9.99'),
          quantity: 7,
          description: 'Post-it Notes',
          payable_account_id: misc_expense.id
        }
      ]
    )
  end

  context 'accrual basis' do
    let(:gl) do
      params = {
        target_id: company.to_sgid,
        start_date: start_date.to_s,
        end_date: end_date.to_s
      }

      described_class.new(params).as_json
    end

    it 'has the AR balance' do
      expect(acc(gl, 'Accounts Receivable')[:ending_balance]).to eq 0
    end

    it 'has the AP balance' do
      expect(acc(gl, 'Accounts Payable')[:ending_balance]).to eq 21_193
    end

    it 'has the cash balance' do
      expect(acc(gl, 'Cash')[:ending_balance]).to eq 19_000
    end

    it 'has the bank account balance' do
      expect(acc(gl, bank_account.ledger_account.name)[:ending_balance]).to \
        eq(90_000 - 15_800 - 1500)
    end

    it 'has landscaping materials balance' do
      expect(acc(gl, 'Landscaping Material')[:ending_balance]).to eq 10_000
    end

    it 'has landscaping labor balance' do
      expect(acc(gl, 'Landscaping Labor')[:ending_balance]).to eq 20_000
    end

    it 'has the misc expense balance' do
      expect(acc(gl, 'Miscellaneous')[:ending_balance]).to eq 8493
    end

    it 'shows a rent payment' do
      expect(acc(gl, 'Rent')[:ending_balance]).to eq 90_000
    end

    it 'shows a deposit' do
      expect(acc(gl, 'Owner Contribution')[:ending_balance]).to eq 50_000
    end

    it 'shows a distribution' do
      expect(acc(gl, 'Owner Distribution')[:ending_balance]).to eq 31_000
    end
  end

  context 'cash basis' do
    let(:gl) do
      Plutus.in_cash_basis(journal_cache_hint: nil) do
        params = {
          target_id: company.to_sgid,
          basis: 'cash',
          start_date: start_date.to_s,
          end_date: end_date.to_s
        }

        described_class.new(params).as_json
      end
    end

    it 'only shows cash accounts' do
      expect(gl[:accounts].pluck(:name).sort).to eq([
        bank_account.ledger_account.name,
        'Cash',
        'Landscaping Labor',
        'Landscaping Material',
        'Miscellaneous',
        'Owner Contribution',
        'Owner Distribution',
        'Rent'
      ].sort)
    end

    it 'has the cash balance' do
      account = acc(gl, 'Cash')
      expect(account[:beginning_balance]).to eq 0
      expect(account[:ending_balance]).to eq 19_000

      transactions = account[:transactions]

      expect(transactions).to contain_exactly(
        {
          link: anything,
          contact: anything,
          date: Date.new(2019, 1, 1),
          description: 'Beginning Balance',
          reference: anything,
          credits: 0,
          debits: 50_000
        }, {
          link: anything,
          contact: anything,
          date: Date.new(2019, 1, 6),
          description: 'Rent',
          reference: anything,
          credits: 0,
          debits: 90_000
        }, {
          link: anything,
          contact: anything,
          date: Date.new(2019, 1, 10),
          description: 'Deposit cash from tenant',
          reference: anything,
          credits: 90_000,
          debits: 0
        }, {
          link: anything,
          contact: anything,
          date: Date.new(2019, 1, 31),
          description: 'Distribution to owner',
          reference: anything,
          credits: 31_000,
          debits: 0
        }
      )
    end

    it 'has the bank account balance' do
      account = acc(gl, bank_account.ledger_account.name)
      expect(account[:beginning_balance]).to eq(0)
      expect(account[:ending_balance]).to eq(90_000 - 15_800 - 1500)

      transactions = account[:transactions]

      expect(transactions).to contain_exactly(
        {
          link: anything,
          contact: anything,
          date: Date.new(2019, 1, 6),
          description: 'Lightbulb',
          reference: anything,
          credits: 1500,
          debits: 0
        }, {
          link: anything,
          contact: anything,
          date: Date.new(2019, 1, 6),
          description: 'Landscaping',
          reference: anything,
          credits: 10_000,
          debits: 0
        }, {
          link: anything,
          contact: anything,
          date: Date.new(2019, 1, 8),
          description: 'Landscaping',
          reference: anything,
          credits: 5800,
          debits: 0
        }, {
          link: anything,
          contact: anything,
          date: Date.new(2019, 1, 10),
          description: 'Deposit cash from tenant',
          reference: anything,
          credits: 0,
          debits: 90_000
        }
      )
    end

    it 'shows a rent payment' do
      expect(acc(gl, 'Rent')[:beginning_balance]).to eq 0
      expect(acc(gl, 'Rent')[:ending_balance]).to eq 90_000
    end

    it 'shows a deposit' do
      expect(acc(gl, 'Owner Contribution')[:beginning_balance]).to eq 0
      expect(acc(gl, 'Owner Contribution')[:ending_balance]).to eq 50_000
    end

    it 'shows a distribution' do
      expect(acc(gl, 'Owner Distribution')[:beginning_balance]).to eq 0
      expect(acc(gl, 'Owner Distribution')[:ending_balance]).to eq 31_000
    end

    it 'shows miscellaneous expenses' do
      expect(acc(gl, 'Miscellaneous')[:beginning_balance]).to eq 0
      expect(acc(gl, 'Miscellaneous')[:ending_balance]).to eq 1500

      expect(acc(gl, 'Miscellaneous')[:transactions]).to match(
        [
          link: anything,
          contact: anything,
          date: Date.new(2019, 1, 6),
          description: 'Lightbulb',
          reference: anything,
          credits: 0,
          debits: 1500
        ]
      )
    end

    it 'does prorating across a partially paid invoice' do
      mat = acc(gl, 'Landscaping Material')[:transactions]
      expect(mat).to match(
        [
          {
            link: anything,
            contact: anything,
            date: Date.new(2019, 1, 6),
            description: 'Landscaping',
            reference: anything,
            credits: 0,
            debits: 3333
          },
          {
            link: anything,
            contact: anything,
            date: Date.new(2019, 1, 8),
            description: 'Landscaping',
            reference: anything,
            credits: 0,
            debits: 1933
          }
        ]
      )

      labor = acc(gl, 'Landscaping Labor')[:transactions]
      expect(labor).to match(
        [
          {
            link: anything,
            contact: anything,
            date: Date.new(2019, 1, 6),
            description: 'Landscaping',
            reference: anything,
            credits: 0,
            debits: 6667
          },
          {
            link: anything,
            contact: anything,
            date: Date.new(2019, 1, 8),
            description: 'Landscaping',
            reference: anything,
            credits: 0,
            debits: 3867
          }
        ]
      )

      expect(acc(gl, 'Landscaping Material')[:ending_balance]).to eq 5266
      expect(acc(gl, 'Landscaping Labor')[:ending_balance]).to eq 10_534
    end
  end
end
