require 'rails_helper'

RSpec.describe ProfitStars::Payment::Refund do
  let(:merchant_account) { create(:merchant_account, :profit_stars) }
  let(:membership) { create(:lease_membership) }
  let(:tenant) { membership.tenant }
  let(:company) { membership.lease.property.company }
  let(:source_account) do
    create(:bank_account, :with_profit_stars, owner: tenant)
  end
  let(:destination_account) do
    create(:bank_account, :with_profit_stars, owner: company)
  end

  let(:payment) do
    create(:payment, :tenant_payment, payer: tenant).tap do |payment|
      payment.update!(
        debit_bank_account: source_account,
        credit_bank_account: destination_account
      )
    end
  end

  context 'a posted transaction' do
    let(:transaction) do
      create(:profit_stars_transaction, :posted, payment: payment)
    end

    describe 'refunding a payment' do
      subject(:result) { described_class.call(merchant_account, transaction) }

      context 'successful', vcr: {
        cassette_name: 'profit_stars/payments/refund_successfully'
      } do
        it { is_expected.to be_successful }

        xit 'updates the transaction status to refunding' do
          expect(result.transaction.status).to eq('Refunding')
        end
      end

      context 'unsuccessful', vcr: {
        cassette_name: 'profit_stars/payments/refund_unsuccessfully'
      } do
        describe 'the result' do
          it { is_expected.not_to be_successful }

          its(:reason) { is_expected.to start_with('You are currently not') }

          it 'does not update the transaction stauts' do
            expect(result.transaction.status).to eq('Approved')
          end
        end
      end
    end
  end
end
