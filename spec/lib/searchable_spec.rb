require 'rails_helper'

RSpec.describe Searchable do
  it 'escapes trash for ElasticSearch' do
    expect(described_class.escape('Property // Name')).to eq 'Property \/\/ Name'
    expect(described_class.escape('Property && Name')).to eq 'Property \&& Name'
    expect(described_class.escape('Property &&& Name')).to eq 'Property \&& Name'
    expect(described_class.escape('Property &&&& Name')).to eq 'Property \&& Name'
  end

  describe 'respects user access scopes', :elasticsearch do
    let(:unscoped_user) { create(:property_manager, top_level: false) }
    let(:top_level_user) { create(:property_manager, top_level: true) }
    let(:scoped_user) do
      scoped_user = create(:property_manager, top_level: false)
      scoped_user.property_memberships << create(:property_membership, target: property,
                                                                       user: scoped_user)
      scoped_user
    end
    let(:company_scoped_user) do
      company_scoped_user = create(:property_manager, top_level: false)
      company_scoped_user.property_memberships << create(
        :property_membership,
        target: property.company,
        user: company_scoped_user
      )
      company_scoped_user
    end
    let!(:property) { create(:property) }

    it 'returns expected scoped results' do
      Searchable::Index.all
      expect(described_class.search_all(property.name).count).to eq 1
      expect(described_class.search_all(property.name).first).to eq property

      # User doesn't have property membership for property
      expect(described_class.search_all(property.name, scope_to: unscoped_user).count).to eq 0

      # User is top level
      expect(described_class.search_all(property.name, scope_to: top_level_user).count).to eq 1
      expect(described_class.search_all(property.name,
                                        scope_to: top_level_user).first).to eq property

      # User has property membership for property
      expect(described_class.search_all(property.name, scope_to: scoped_user).count).to eq 1
      expect(described_class.search_all(property.name, scope_to: scoped_user).first).to eq property
    end
  end
end
