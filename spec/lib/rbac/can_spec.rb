require 'rails_helper'

RSpec.describe RBAC::Can do
  describe '#initialize' do
    describe 'explicitly permitted actor' do
      it 'returns true' do
        pm = create(:property_manager)
        pm.role.permissions.create(namespace: :operations, component: :pulse, action: :show)
        can = described_class.new(pm)

        expect(can.show_operations_pulse?).to be(true)
      end
    end

    describe 'implicitly permitted actor with manage' do
      it 'returns true' do
        pm = create(:property_manager)
        pm.role.permissions.create(namespace: :operations, component: :pulse, action: :manage)
        can = described_class.new(pm)

        expect(can.show_operations_pulse?).to be(true)
      end
    end

    describe 'unpermitted actor' do
      it 'returns false' do
        pm = create(:property_manager)
        can = described_class.new(pm)

        expect(can.show_operations_pulse?).to be(false)
      end
    end
  end
end
