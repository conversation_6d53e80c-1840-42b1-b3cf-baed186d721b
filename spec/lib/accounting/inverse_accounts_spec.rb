require 'rails_helper'

RSpec.describe Accounting::InverseAccounts do
  let(:journals) { create_pair(:company) }

  let(:journal_one) { journals.first }

  let(:journal_two) { journals.second }

  let(:journal_one_chart) { journal_one.chart_of_accounts }

  let(:journal_two_chart) { journal_two.chart_of_accounts }

  it 'determines a default account' do
    invoice = create(:invoice,
                     seller: journal_one,
                     buyer: journal_two,
                     create_default_accounts: false)

    item = invoice.line_items.first!

    expect(item.receivable_account).to \
      eq(journal_one_chart.default_revenue_account)

    expect(item.payable_account).to \
      eq(journal_two_chart.default_expense_account)
  end

  it 'determines a mapped payable account' do
    revenue_account = create(:revenue_account, tenant: journal_one_chart)

    mapped_account = create(:expense_account, tenant: journal_two_chart)

    allow(described_class).to receive(:receivable_to_payable_mapping) do
      {
        journal_one_chart.id => {
          journal_two_chart.id => {
            revenue_account.id => mapped_account.id
          }
        }
      }
    end

    invoice = create(:invoice,
                     seller: journal_one,
                     buyer: journal_two,
                     revenue_account: revenue_account,
                     create_default_accounts: false)

    item = invoice.line_items.first!

    expect(item.receivable_account).to eq(revenue_account)
    expect(item.payable_account).to eq(mapped_account)
  end

  it 'determines a mapped receivable account' do
    expense_account = create(:expense_account, tenant: journal_one_chart)

    mapped_account = create(:revenue_account, tenant: journal_two_chart)

    allow(described_class).to receive(:payable_to_receivable_mapping) do
      {
        journal_one_chart.id => {
          journal_two_chart.id => {
            expense_account.id => mapped_account.id
          }
        }
      }
    end

    invoice = create(:invoice,
                     buyer: journal_one,
                     seller: journal_two,
                     expense_account: expense_account,
                     create_default_accounts: false)

    item = invoice.line_items.first!

    expect(item.payable_account).to eq(expense_account)
    expect(item.receivable_account).to eq(mapped_account)
  end
end
