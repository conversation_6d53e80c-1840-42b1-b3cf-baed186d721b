require 'rails_helper'

RSpec.describe Accounting::Context::Company do
  subject(:accounting_context) { described_class.new(company) }

  context 'a company with no properties' do
    let(:company) { create(:company) }

    its(:entry_params) { is_expected.to eq(journal_id: company.id) }
    its(:child_type) { is_expected.to be_nil }
  end

  context 'a company with one multi family property' do
    let(:property) { create(:property_with_units, unit_count: 2) }
    let(:company) { property.company }

    its(:entry_params) do
      is_expected.to eq(journal_id: company.id, property_id: property.id)
    end

    its(:child_type) { is_expected.to eq('Unit') }

    context 'a company with one singlefamily property' do
      let(:property) { create(:property_with_units, unit_count: 1) }
      let(:unit) { property.units.first }

      its(:entry_params) do
        is_expected.to eq(
          journal_id: company.id, property_id: property.id, unit_id: unit.id
        )
      end

      its(:child_type) { is_expected.to be_nil }
    end
  end

  context 'a company with two properties' do
    let(:company) { create(:company) }
    let!(:properties) { create_pair(:property, company: company) }

    its(:entry_params) { is_expected.to eq(journal_id: company.id) }

    its(:child_type) { is_expected.to eq('Property') }
  end
end
