require 'rails_helper'

require_relative '../reports/v3/shared/management_portfolio_activity_context'

RSpec.describe Accounting::AccountBalanceMatrix do
  describe 'portfolio activity' do
    include_context 'with management portfolio activity'

    def account_balance(portfolio, account)
      matrix = Accounting::AccountBalanceMatrix.new(
        accounting_context: Customer.current.client_entity.accounting_context,
        start_date: Time.zone.today,
        end_date: Time.zone.today
      )

      x = matrix.balance_for_customer_portfolio_filter(
        account:,
        filter: portfolio.id.to_s
      )

      x.abs
    end

    describe 'for portfolio one' do
      it 'knows hvac revenue' do
        expect(account_balance(portfolio_one, hvac_revenue)).to eq(Monetize.parse('$50.00'))
      end

      it 'knows hvac expense' do
        expect(account_balance(portfolio_one, hvac_expense)).to eq(Monetize.parse('$100.00'))
      end

      it 'knows plumbing revenue' do
        expect(account_balance(portfolio_one, plumbing_revenue)).to eq(Monetize.parse('$75.50'))
      end

      it 'knows plumbing expense' do
        expect(account_balance(portfolio_one, plumbing_expense)).to eq(Monetize.parse('$34.15'))
      end
    end

    describe 'for portfolio two' do
      it 'knows plumbing revenue' do
        expect(account_balance(portfolio_two, plumbing_revenue)).to eq(Monetize.parse('$125.33'))
      end

      it 'knows plumbing expense' do
        expect(account_balance(portfolio_two, plumbing_expense)).to eq(Monetize.parse('$56.70'))
      end
    end
  end
end
