require 'rails_helper'

RSpec.describe 'trust accounting security deposits' do
  let!(:management_company) { Customer.current.client_entity }

  let(:chart_of_accounts) { management_company.chart_of_accounts }

  let!(:trust_account) do
    asset_account = create(:asset_account, name: 'Trust Account',
                                           tenant: chart_of_accounts)

    create(:bank_account,
           owner: management_company,
           ledger_account: asset_account)
  end

  let!(:membership) { create(:lease_membership) }

  let(:tenant) { membership.tenant }

  let!(:lease) { membership.lease }

  let(:unit) { lease.unit }

  let!(:property) { unit.property }

  let!(:entity) do
    property.company.tap do |company|
      company.portfolio.configuration.update!(
        chart_of_accounts: chart_of_accounts
      )
    end
  end

  let!(:security_deposit_account) do
    chart_of_accounts.security_deposit_account
  end

  let!(:damages_account) do
    create(:revenue_account, tenant: chart_of_accounts,
                             name: 'Tenant Damages')
  end

  let!(:management_held_security_deposit_account) do
    chart_of_accounts.management_held_security_deposit_account
  end

  let!(:ledger) { membership.ledger }

  before do
    travel_to(Date.new(2000, 1, 1))

    # Consistent reports
    management_company.update!(name: 'Management Company')
    entity.update!(name: 'Sample Entity')
    property.update!(name: 'Sample Property')
    property.address.update!(
      line_one: 'Sample',
      line_two: nil,
      city: 'Detroit',
      region: 'Michigan',
      postal_code: '48226'
    )
    unit.update!(name: '101')
    tenant.update!(first_name: 'Sample', last_name: 'Tenant')
    chart_of_accounts.accounts_receivable.update!(gl_code: '1010')
    chart_of_accounts.accounts_payable.update!(gl_code: '2050')
    chart_of_accounts.security_deposit_account.update!(gl_code: '2000')
    chart_of_accounts.management_held_security_deposit_account.update!(gl_code: '2010')
    chart_of_accounts.due_from_client_entity_account.update!(gl_code: '1020')
    chart_of_accounts.due_to_customer_account.update!(gl_code: '2020')
    chart_of_accounts.prepaid_revenue_account.update!(gl_code: '2030')
    chart_of_accounts.clearing_account.update!(gl_code: '2040')
    trust_account.ledger_account.update!(gl_code: '1000')
    damages_account.update!(gl_code: '4000')
    entity.rent_income_account.update!(gl_code: '4030')

    # Ensure management contract
    contract = create(:management_contract, company: entity)
    create(:management_contract_membership,
           management_contract: contract,
           property: property)

    # Passthrough security deposit
    security_deposit_account.update!(passthrough: true)

    # Invoice $1,000.00 security deposit
    security_deposit_invoice = create(:invoice, :lease_invoice,
                                      membership: membership,
                                      amount: '$1,000',
                                      account: security_deposit_account)

    # Pay $1,000.00 security deposit
    travel_to(Date.new(2000, 1, 2))
    create(:payment, :unapplied,
           description: 'Security Deposit Payment',
           deposit_bank_account: trust_account,
           payer: membership.tenant,
           payer_lease_membership: membership,
           payee: property,
           amount: '$1,000') do |payment|
             payment.invoice_payments.create!(
               invoice: security_deposit_invoice,
               amount: '$1,000'
             )
           end

    expect(security_deposit_invoice).to be_paid
    expect(ledger.deposit_account).to eq(security_deposit_account)
    expect(ledger.held_security_deposit).to eq(Monetize.parse('$1,000.00'))
    expect(membership.security_deposit.held).to eq(Monetize.parse('$1,000.00'))

    # Invoice $500.00 rent
    travel_to(Date.new(2000, 1, 3))
    create(:rent_invoice,
           membership: membership,
           amount: '$500.00')

    # Invoice $250.00 damage
    travel_to(Date.new(2000, 1, 4))
    create(:invoice, :lease_invoice,
           account: damages_account,
           membership: membership,
           description: 'Damages',
           amount: '$250.00')

    # Transfer security deposit
    travel_to(Date.new(2000, 1, 5))
    Accounting::TransferSecurityDeposit.call(
      ledger: ledger,
      direction: 'from_management',
      amount: Monetize.parse('$1,000.00'),
      date: Time.zone.today
    )

    # Apply security deposit
    travel_to(Date.new(2000, 1, 6))
    Ledger::ApplySecurityDeposit.call(
      ledger: ledger,
      date: Time.zone.today
    )

    # Apply credit to invoices
    Accounting::ApplyCredit.call(entity: membership.ledger)

    # Clear credit
    travel_to(Date.new(2000, 1, 7))
    result = Ledger::ClearCredit.call(
      ledger: ledger,
      date: Time.zone.today
    )

    # Create tenant refund
    travel_to(Date.new(2000, 1, 8))
    refund_invoice = Ledger::CreateRefundPayable.call(
      ledger: ledger,
      date: Time.zone.today,
      amount: result.cleared_amount
    ).invoice

    # Refund tenant
    travel_to(Date.new(2000, 1, 9))
    create(:payment, :unapplied,
           withdrawal_bank_account: trust_account,
           description: 'Refund Payment',
           payer: property,
           payer_unit: membership.unit,
           payee: membership.tenant,
           amount: refund_invoice.amount,
           invoices: [refund_invoice])

    # Uniform reference numbers
    Plutus::Entry.update_all(reference_number: nil, reference_text: nil)
  end

  scenario 'there is an appropriate security deposit report' do
    report = Reports::V3::SecurityDeposits.new(
      filters: OpenStruct.new(
        context_id: property.to_sgid.to_s,
        show_zero_rows: true # TODO: This filter should be renamed
      )
    )

    verify { approval_report_json(report) }
  end

  %w[accrual cash].each do |basis|
    scenario "there is an appropriate #{basis} basis management general ledger" do
      report = Reports::V3::GeneralLedger.new(
        filters: OpenStruct.new(
          context_id: management_company.to_sgid.to_s,
          start_date: Date.new(2000, 1, 1),
          end_date: Date.new(2000, 1, 31),
          basis: basis
        )
      )

      verify { approval_report_json(report) }
    end

    scenario "there is an appropriate #{basis} basis entity general ledger" do
      report = Reports::V3::GeneralLedger.new(
        filters: OpenStruct.new(
          context_id: entity.to_sgid.to_s,
          start_date: Date.new(2000, 1, 1),
          end_date: Date.new(2000, 1, 31),
          basis: basis
        )
      )

      verify { approval_report_json(report) }
    end
  end

  scenario 'there is an appropriate owner statement' do
    report = Reports::V3::OwnerStatement.new(
      filters: OpenStruct.new(
        context_id: entity.to_sgid.to_s,
        start_date: Date.new(2000, 1, 1),
        end_date: Date.new(2000, 1, 31)
      )
    )

    verify { approval_report_json(report) }
  end
end
