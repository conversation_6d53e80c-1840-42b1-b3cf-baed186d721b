require 'rails_helper'

RSpec.describe Inspection::Activity do
  describe 'the factory' do
    subject { build(:inspection_activity) }

    it { is_expected.to be_valid }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:inspection_report) }

    it { is_expected.to belong_to(:actor).optional }

    it { is_expected.to belong_to(:assigned_to).optional }

    it { is_expected.to validate_presence_of(:kind) }
  end
end
