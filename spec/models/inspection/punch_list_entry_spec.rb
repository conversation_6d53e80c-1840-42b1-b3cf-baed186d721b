require 'rails_helper'

RSpec.describe Inspection::PunchListEntry do
  describe 'the factory' do
    subject { build(:inspection_punch_list_entry) }

    it { is_expected.to be_valid }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:response) }

    it { is_expected.to validate_presence_of(:sku_list_item) }

    it { is_expected.to validate_presence_of(:count) }

    it { is_expected.to validate_numericality_of(:count) }
  end
end
