require 'rails_helper'

RSpec.describe Taxes::Nelco::Account do
  subject { create(:taxes_nelco_account, password: 'NelcoPassword1') }

  describe 'the factory' do
    it { is_expected.to be_valid }
  end

  describe 'passwords' do
    it 'can expose the decrypted password' do
      expect(subject.password).to eq('NelcoPassword1')
    end

    it 'doesnt store the actual password' do
      expect(subject.encrypted_password).not_to eq('NelcoPassword1')
      expect(subject.encrypted_password_iv).not_to eq('NelcoPassword1')
    end
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:username) }

    it { is_expected.to validate_presence_of(:password) }

    it { is_expected.to validate_presence_of(:email) }

    it { is_expected.to belong_to(:submitted_by).required }
  end
end
