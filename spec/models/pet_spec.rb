require 'rails_helper'

RSpec.describe Pet do
  describe '#age' do
    it 'calculates age in calendar years' do
      travel_to Time.zone.local(2024, 4, 1, 12, 0, 0) do
        pet = build(:pet, date_of_birth: Date.new(2022, 4, 1))
        expect(pet.age).to eq(2)

        pet.date_of_birth = Date.new(2021, 10, 1)
        expect(pet.age).to eq(2) # should floor to completed years

        pet.date_of_birth = Date.new(2021, 4, 2)
        expect(pet.age).to eq(2) # just under 3 years
      end
    end
  end

  describe '#age_in_pet_years' do
    context 'when calculating cat ages' do
      let(:pet) { build(:pet, kind: :cat) }

      before do
        travel_to Time.zone.local(2024, 4, 1, 12, 0, 0)
      end

      it 'calculates age for cats under 1 year' do
        pet.date_of_birth = Date.new(2023, 10, 1) # 6 months old
        expect(pet.age_in_pet_years).to eq(7) # (0.5 * 15).floor
      end

      it 'calculates age for cats at exactly 1 year' do
        pet.date_of_birth = Date.new(2023, 4, 1)
        expect(pet.age_in_pet_years).to eq(15)
      end

      it 'calculates age for cats between 1-2 years' do
        pet.date_of_birth = Date.new(2022, 10, 1) # 1.5 years
        expect(pet.age_in_pet_years).to eq(19) # 15 + (0.5 * 9).floor
      end

      it 'calculates age for cats at exactly 2 years' do
        pet.date_of_birth = Date.new(2022, 4, 1)
        expect(pet.age_in_pet_years).to eq(24) # 15 + 9
      end

      it 'calculates age for cats over 2 years' do
        pet.date_of_birth = Date.new(2021, 4, 1) # 3 years
        expect(pet.age_in_pet_years).to eq(28) # 24 + (1 * 4)

        pet.date_of_birth = Date.new(2019, 4, 1) # 5 years
        expect(pet.age_in_pet_years).to eq(36) # 24 + (3 * 4)
      end
    end

    context 'when calculating dog ages' do
      let(:pet) { build(:pet, kind: :dog) }

      before do
        travel_to Time.zone.local(2024, 4, 1, 12, 0, 0)
      end

      context 'with weight under 20 lbs' do
        before { pet.weight = 15 }

        it 'calculates age for first year' do
          pet.date_of_birth = Date.new(2023, 10, 1) # 6 months
          expect(pet.age_in_pet_years).to eq(7) # (0.5 * 15).floor
        end

        it 'calculates age for second year' do
          pet.date_of_birth = Date.new(2022, 10, 1) # 1.5 years
          expect(pet.age_in_pet_years).to eq(19) # 15 + (0.5 * 9).floor
        end

        it 'calculates age for older dogs' do
          pet.date_of_birth = Date.new(2021, 4, 1) # 3 years
          expect(pet.age_in_pet_years).to eq(28) # 24 + (1 * 4).floor
        end
      end

      context 'with weight between 20-50 lbs' do
        before { pet.weight = 35 }

        it 'calculates age for older dogs' do
          pet.date_of_birth = Date.new(2021, 4, 1) # 3 years
          expect(pet.age_in_pet_years).to eq(29) # 24 + (1 * 5).floor
        end
      end

      context 'with weight over 50 lbs' do
        before { pet.weight = 70 }

        it 'calculates age for older dogs' do
          pet.date_of_birth = Date.new(2021, 4, 1) # 3 years
          expect(pet.age_in_pet_years).to eq(30) # 24 + (1 * 6).floor
        end
      end
    end

    context 'when calculating other pet ages' do
      let(:pet) { build(:pet, kind: :other, kind_detail: 'Hamster') }

      it 'returns regular calendar age' do
        travel_to Time.zone.local(2024, 4, 1, 12, 0, 0) do
          pet.date_of_birth = Date.new(2022, 4, 1) # 2 years
          expect(pet.age_in_pet_years).to eq(2)
        end
      end
    end
  end
end
