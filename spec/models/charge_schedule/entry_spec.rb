require 'rails_helper'

require_relative '../concerns/date_ordering'

RSpec.describe ChargeSchedule::Entry do
  subject { build(:charge_schedule_entry) }

  describe 'the factory' do
    it { is_expected.to be_valid }
  end

  describe 'validations' do
    it { is_expected.to belong_to(:charge_schedule).required }

    it { is_expected.to belong_to(:account).required }

    it { is_expected.to belong_to(:charge_preset).optional }

    it { is_expected.to validate_presence_of(:name) }

    it { is_expected.to validate_presence_of(:amount_cents) }

    it 'enforces exact distribution via charge amounts' do
      membership = build_stubbed(:lease_membership)

      charge = build(:charge_schedule_entry, amount: '$100', allocations: [])
      expect(charge).not_to be_valid

      charge.allocations.build(lease_membership: membership, amount: '$50')
      expect(charge).not_to be_valid

      charge.allocations.build(lease_membership: membership, amount: '$50')
      expect(charge).to be_valid
    end

    include_examples 'date ordering'
  end
end
