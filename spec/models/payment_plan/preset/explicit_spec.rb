require 'rails_helper'

require_relative 'shared'

RSpec.describe PaymentPlan::Preset::Explicit do
  subject { build(:explicit_payment_plan_preset) }

  it_behaves_like 'a payment plan preset'

  describe 'the factory' do
    it { is_expected.to be_valid }

    it { is_expected.to be_a(described_class) }
  end

  describe 'validations' do
    it 'requires unique installment dates' do
      expect(subject).to be_valid
      subject.installments.first.date = Time.zone.tomorrow
      subject.installments.second.date = Time.zone.tomorrow
      expect(subject).not_to be_valid
    end
  end
end
