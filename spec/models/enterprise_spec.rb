require 'rails_helper'

RSpec.describe Enterprise do
  subject { build(:enterprise) }

  describe 'the factory' do
    it { is_expected.to be_valid }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:name) }

    it { is_expected.to validate_uniqueness_of(:name) }

    it { is_expected.to validate_presence_of(:subdomain_prefix) }

    it { is_expected.to validate_uniqueness_of(:subdomain_prefix) }
  end
end
