require 'rails_helper'

RSpec.describe MemberOnboarding::Tenants::Wizard do
  let(:cohort) { MemberOnboarding::Tenants::ConfiguredCohort.new(onboarding, property) }
  let(:property) { create(:property) }
  let(:tenant) { create(:tenant) }
  let(:onboarding) do
    create(:member_onboarding_configuration, :information_collection, :guarantor, :charge,
           membership_agreement: membership_config)
  end
  let(:membership_config) do
    build(:member_onboarding_membership_agreement, require_member_signature: true)
  end
  let(:lease_config) { build(:member_onboarding_lease_agreement, require_member_signature: true) }

  let(:wizard) { described_class.create(cohort, tenant) }

  describe 'initial creation' do
    let(:onboarding) { create(:member_onboarding_configuration, :information_collection) }

    context 'when just collecting information' do
      it 'has a welcome and member profile step' do
        expect(wizard.steps.keys.map(&:to_sym)).to \
          eq(%i[welcome member_profile completion_summary])
      end

      it 'starts on the welcome step' do
        expect(wizard.current_step.id.to_sym).to eq(:welcome)
      end
    end

    context 'when collecting everything' do
      let(:onboarding) do
        create(:member_onboarding_configuration, :information_collection, :guarantor, :charge,
               membership_agreement: membership_config, lease_agreement: lease_config)
      end

      it 'has a welcome and member profile step' do
        expect(wizard.steps.keys.map(&:to_sym)).to \
          eq(%i[welcome member_profile guarantor_profile membership_agreement
                lease_agreement completion_summary])
      end

      it 'starts on the welcome step' do
        expect(wizard.current_step.id.to_sym).to eq(:welcome)
      end
    end
  end

  describe 'persisting and loading' do
    let(:rehydrated_wizard) do
      wizard.persist!
      described_class.hydrate(cohort, tenant)
    end

    it 'has the same steps' do
      expect(rehydrated_wizard.steps.keys).to eq(wizard.steps.keys)

      serialized_wizard_steps = rehydrated_wizard.steps.values.map(&:attributes)
      serialized_rehydrated_steps = rehydrated_wizard.steps.values.map(&:attributes)
      expect(serialized_wizard_steps).to eq(serialized_rehydrated_steps)
    end

    it 'has the same cohort' do
      expect(rehydrated_wizard.property).to \
        be_a(Property).and(have_attributes(id: wizard.property.id))
      expect(rehydrated_wizard.onboarding).to \
        be_a(MemberOnboarding::Configuration).and(have_attributes(id: wizard.onboarding.id))
    end

    it 'is at the same step' do
      expect(rehydrated_wizard.current_step.id).to eq(wizard.current_step.id)
    end

    context 'when replacing a legacy key' do
      before do
        create(:member_onboarding_property_membership,
               property: property, configuration: onboarding)
        tenant.update(lead_property_id: property.id)
        wizard.persist!
        Redis.instance do |redis|
          redis.rename(wizard.redis_key, wizard.legacy_redis_key)
        end
      end

      describe 'preconditions' do
        it 'has wizard data at the legacy redis key' do
          expect(Redis.instance do |redis|
            redis.get(described_class.legacy_redis_key(cohort, tenant))
          end).to be_present

          expect(Redis.instance do |redis|
            redis.get(described_class.redis_key(cohort, tenant))
          end).to be_blank
        end
      end

      it 'updates the key' do
        legacy_wizard = described_class.find_or_create(tenant)
        legacy_wizard_attributes = legacy_wizard.attributes
        expect { legacy_wizard.persist! }.to \
          change { Redis.instance { |r| r.get(described_class.redis_key(cohort, tenant)) } }
          .from(nil).to(legacy_wizard_attributes.to_json).and \
            change {
              Redis.instance { |r| r.get(described_class.legacy_redis_key(cohort, tenant)) }
            }.from(legacy_wizard_attributes.to_json).to(nil)
        expect(described_class.find_or_create(tenant).attributes).to eq(legacy_wizard_attributes)
      end
    end
  end

  describe 'step transitions' do
    let(:onboarding) do
      create(:member_onboarding_configuration, :information_collection, :guarantor,
             lease_agreement: lease_config)
    end

    def visitable_steps_ids
      wizard.steps.values.select(&:visitable?).map(&:id).map(&:to_s)
    end
    describe 'welcome' do
      before { wizard.current_step_id = 'welcome' }

      it 'allows them to visit the profile forms and welcome' do
        expect(visitable_steps_ids).to eq(%w[welcome member_profile guarantor_profile])
      end
    end

    describe 'member profile' do
      before do
        wizard.current_step_id = 'member_profile'
        wizard.current_step.completed = true
      end

      it 'allows them to visit the profile forms and welcome' do
        expect(visitable_steps_ids).to eq(%w[welcome member_profile guarantor_profile])
      end

      it 'does not need to submit after completion' do
        # that would happen after signing the lease
        expect(wizard.current_step).not_to be_submit_after_completed
      end

      context 'when not collecting guarantor' do
        let(:onboarding) do
          create(:member_onboarding_configuration, :information_collection,
                 lease_agreement: lease_config)
        end

        it 'would need to submit before the next step, which is the lease' do
          expect(wizard.current_step).to be_submit_after_completed
          expect(visitable_steps_ids).to eq(%w[welcome member_profile])
        end
      end
    end

    describe 'guarantor profile' do
      before do
        wizard.steps['member_profile'].completed = true
        wizard.steps['guarantor_profile'].completed = true
        wizard.current_step_id = 'guarantor_profile'
      end

      it 'does needs to be submitted after completion' do
        # since the lease is produce by submitting
        expect(wizard.current_step).to be_submit_after_completed
      end

      context 'when it has NOT be submitted successfully' do
        it 'cannot advance to the lease signing since submission creates the lease' do
          expect(visitable_steps_ids).to eq(%w[welcome member_profile guarantor_profile])
        end
      end

      context 'when it HAS been submitted successfully' do
        let(:lease) { create(:lease_membership, tenant: tenant).lease }

        before do
          wizard.submitted = true
          wizard.current_step_id = 'lease_agreement'
          wizard.steps['lease_agreement'].lease_id = lease.id
        end

        it 'allows them to sign the lease' do
          expect(visitable_steps_ids).to eq(%w[welcome lease_agreement])
        end
      end
    end

    describe 'lease agreement' do
      let(:lease) { create(:lease_membership, tenant: tenant).lease }

      before do
        wizard.steps['member_profile'].completed = true
        wizard.steps['guarantor_profile'].completed = true
        wizard.submitted = true
        wizard.current_step_id = 'lease_agreement'
        wizard.steps['lease_agreement'].lease_id = lease.id
      end

      it 'locks the profile forms and allows them to sign the lease' do
        expect(visitable_steps_ids).to eq(%w[welcome lease_agreement])
      end

      context 'when signed' do
        before { create(:electronic_signature, :signed, document: lease) }

        it 'completes the lease agreement step' do
          expect(wizard.steps['lease_agreement']).to be_completed
        end

        it 'prevents signing the lease again forms' do
          expect(visitable_steps_ids).to eq(%w[welcome completion_summary])
        end
      end
    end
  end
end
