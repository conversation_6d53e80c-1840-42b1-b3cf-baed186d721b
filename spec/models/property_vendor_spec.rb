require 'rails_helper'

RSpec.describe PropertyVendor do
  describe 'the factory' do
    subject { build(:property_vendor) }

    it { is_expected.to be_valid }
  end

  describe 'associations' do
    it { is_expected.to belong_to(:property).required }
    it { is_expected.to belong_to(:vendor).required }
  end

  describe 'validations' do
    subject { create(:property_vendor) }

    it { is_expected.to validate_uniqueness_of(:property_id).scoped_to(:vendor_id) }
  end

  describe 'many-to-many relationship' do
    let(:property) { create(:property) }
    let(:vendor1) { create(:vendor) }
    let(:vendor2) { create(:vendor) }

    it 'allows a property to have multiple vendors' do
      property.vendors << vendor1
      property.vendors << vendor2

      expect(property.vendors).to contain_exactly(vendor1, vendor2)
    end

    it 'allows a vendor to be associated with multiple properties' do
      property1 = create(:property)
      property2 = create(:property)

      vendor1.properties << property1
      vendor1.properties << property2

      expect(vendor1.properties).to contain_exactly(property1, property2)
    end

    it 'prevents duplicate associations' do
      property.vendors << vendor1

      expect {
        property.vendors << vendor1
      }.to raise_error(ActiveRecord::RecordInvalid)
    end
  end
end
