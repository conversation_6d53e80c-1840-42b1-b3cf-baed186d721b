require 'rails_helper'

RSpec.describe EntityVendorPreference do
  describe 'the factory' do
    subject { build(:entity_vendor_preference) }

    it { is_expected.to be_valid }
  end

  describe 'associations' do
    it { is_expected.to belong_to(:entity).required }
    it { is_expected.to belong_to(:vendor).required }
  end

  describe 'validations' do
    subject { create(:entity_vendor_preference) }

    it { is_expected.to validate_uniqueness_of(:entity_id).scoped_to([:entity_type, :vendor_id]) }
    it { is_expected.to validate_presence_of(:entity_type) }
  end

  describe 'polymorphic associations with Property' do
    let(:property) { create(:property) }
    let(:vendor1) { create(:vendor) }
    let(:vendor2) { create(:vendor) }

    it 'allows a property to have multiple vendors' do
      property.vendors << vendor1
      property.vendors << vendor2

      expect(property.vendors).to contain_exactly(vendor1, vendor2)
    end

    it 'allows a vendor to be associated with multiple properties' do
      property1 = create(:property)
      property2 = create(:property)

      vendor1.properties << property1
      vendor1.properties << property2

      expect(vendor1.properties).to contain_exactly(property1, property2)
    end

    it 'prevents duplicate property-vendor associations' do
      property.vendors << vendor1

      expect {
        property.vendors << vendor1
      }.to raise_error(ActiveRecord::RecordInvalid)
    end
  end

  describe 'polymorphic associations with Portfolio' do
    let(:portfolio) { create(:portfolio) }
    let(:vendor1) { create(:vendor) }
    let(:vendor2) { create(:vendor) }

    it 'allows a portfolio to have multiple vendors' do
      portfolio.vendors << vendor1
      portfolio.vendors << vendor2

      expect(portfolio.vendors).to contain_exactly(vendor1, vendor2)
    end

    it 'allows a vendor to be associated with multiple portfolios' do
      portfolio1 = create(:portfolio)
      portfolio2 = create(:portfolio)

      vendor1.portfolios << portfolio1
      vendor1.portfolios << portfolio2

      expect(vendor1.portfolios).to contain_exactly(portfolio1, portfolio2)
    end

    it 'prevents duplicate portfolio-vendor associations' do
      portfolio.vendors << vendor1

      expect {
        portfolio.vendors << vendor1
      }.to raise_error(ActiveRecord::RecordInvalid)
    end
  end

  describe 'cross-entity associations' do
    let(:property) { create(:property) }
    let(:portfolio) { create(:portfolio) }
    let(:vendor) { create(:vendor) }

    it 'allows the same vendor to be associated with both properties and portfolios' do
      property.vendors << vendor
      portfolio.vendors << vendor

      expect(vendor.properties).to contain_exactly(property)
      expect(vendor.portfolios).to contain_exactly(portfolio)
    end

    it 'maintains separate associations for properties and portfolios' do
      property1 = create(:property)
      property2 = create(:property)
      portfolio1 = create(:portfolio)
      portfolio2 = create(:portfolio)

      vendor.properties << property1
      vendor.properties << property2
      vendor.portfolios << portfolio1
      vendor.portfolios << portfolio2

      expect(vendor.properties).to contain_exactly(property1, property2)
      expect(vendor.portfolios).to contain_exactly(portfolio1, portfolio2)
      expect(vendor.entity_vendor_preferences.count).to eq(4)
    end
  end
end
