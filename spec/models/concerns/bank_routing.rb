RSpec.shared_examples 'bank routing' do
  # http://sandbox.michaelgrace.org/bank-routing-number-validation/
  it 'requires valid routing numbers' do
    %w[********* ********* ********* *********].each do |valid|
      routable.routing_number = valid
      expect(routable).to be_valid
    end

    %w[********* ********* 828 noteconet *********].each do |invalid|
      routable.routing_number = invalid
      expect(routable).not_to be_valid
    end
  end
end
