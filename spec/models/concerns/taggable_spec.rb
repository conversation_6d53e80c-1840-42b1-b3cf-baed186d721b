require 'rails_helper'

RSpec.describe Taggable do
  let(:taggable) { create(:owner) }

  describe '#tags=' do
    it 'assigns and replaces tags' do
      tag_one, tag_two, tag_three = create_list(:tag, 3, taggable_type: taggable.taggable_type)

      # Assigning a Tag
      taggable.tags = [tag_one]
      expect(taggable.reload.tags).to contain_exactly(tag_one)

      # Replacing with another list of Tags
      taggable.tags = [tag_two, tag_three]
      expect(taggable.reload.tags).to contain_exactly(tag_two, tag_three)

      # Assigning a String Array
      taggable.tags = [tag_one.tag, tag_three.tag]
      expect(taggable.reload.tags).to contain_exactly(tag_one, tag_three)

      # Assigning an empty Array
      taggable.tags = []
      expect(taggable.reload.tags).to be_empty

      # Assigning a String Array for a new Tag
      new_tag_tag = 'New Tag'
      taggable.tags = [new_tag_tag]
      expect(taggable.reload.tags.map(&:tag)).to contain_exactly(new_tag_tag)

      # Assigning a subset of existing Tags retains Taggings
      taggable.tags = [tag_one, tag_two]
      first_tagging = taggable.taggings.find_by(tag: tag_one)
      string_array = [tag_one.tag, tag_three.tag]
      taggable.tags = string_array # Replace tag_two, keep existing tag_one Tagging
      taggable.reload
      new_tagging = taggable.taggings.find_by(tag: tag_three)
      expect(taggable.taggings).to contain_exactly(first_tagging, new_tagging)
    end
  end
end
