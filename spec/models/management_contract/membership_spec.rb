require 'rails_helper'

require_relative '../concerns/date_ordering'

RSpec.describe ManagementContract::Membership do
  subject { build(:management_contract_membership) }

  describe 'the factory' do
    it { is_expected.to be_valid }
  end

  describe 'validations' do
    it { is_expected.to belong_to(:management_contract).required }

    it { is_expected.to belong_to(:property).required }

    include_examples 'date ordering'
  end
end
