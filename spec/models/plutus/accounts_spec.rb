require 'rails_helper'
require_relative '../concerns/searchable'

RSpec.describe Plutus::Account do
  describe 'the factory' do
    subject(:factory) { create(:account) }

    it { is_expected.to be_valid }
  end

  describe 'validations' do
    subject { build(:asset_account) }

    it { is_expected.to validate_presence_of(:gl_code) }

    it { is_expected.to validate_presence_of(:name) }

    it { is_expected.to validate_presence_of(:type) }

    it { is_expected.to validate_presence_of(:category) }

    it 'is unique amongst tenant and header' do
      is_expected.to validate_uniqueness_of(:name).scoped_to(
        :tenant_id, :tenant_type, :type, :category, :header
      )
    end
  end

  describe 'name uniqueness' do
    it 'allows two accounts with the same name for different tenants' do
      create(:account, name: 'Cash')
      account = build(:account, name: 'Cash')
      expect(account).to be_valid
    end

    context 'for the same chart' do
      let(:chart) { create(:chart_of_accounts) }

      it 'does not allow two accounts with the same name' do
        create(:account, name: 'Cash', tenant: chart)
        account = build(:account, name: 'Cash', tenant: chart)
        expect(account).not_to be_valid
      end

      it 'allows two accounts with the same name for different headers' do
        create(:account, tenant: chart, name: 'Travel', header: 'Sales')
        account = build(:account, tenant: chart, name: 'Travel')
        expect(account).to be_valid
      end
    end
  end

  described_class.subclasses.each do |klass|
    describe "searching for #{klass}" do
      it_behaves_like 'searchable' do
        let(:described_class) { klass }

        let(:searchable_fields) { [:name] }
      end
    end
  end
end
