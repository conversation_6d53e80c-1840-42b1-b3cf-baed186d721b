<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified">
   <xs:element name="ServiceLink">
      <xs:complexType>
         <xs:sequence>
            <xs:element name="InspectionOrder" maxOccurs="unbounded" minOccurs="1">
               <xs:complexType>
                  <xs:sequence>
                     <xs:element name="Loan" maxOccurs="unbounded" minOccurs="1">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="LoanNumber" minOccurs="1" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="20" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="InvestorInsurer" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="5" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="LoanType" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element type="xs:integer" name="ClientNumber" minOccurs="1" maxOccurs="1" />
                              <xs:element type="xs:integer" name="LSCI" minOccurs="1" maxOccurs="1" />
                              <xs:element name="Address1" minOccurs="1" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="Address2" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="City" minOccurs="1" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="State" minOccurs="1" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="2" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ZipCode" minOccurs="1" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="5" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ZipPlusFour" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="4" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="CountyName" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ValidatedAddress1" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ValidatedAddress2" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ValidatedCity" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ValidatedState" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="2" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ValidatedZipCode" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="5" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ValidatedZipPlusFour" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="4" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ValidatedCountyName" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ValidatedGeoLongitude" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="15" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ValidatedGeoLatitude" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="15" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="KeyCode" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="40" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="LockBoxCode" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="40" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="DAWGSVPSCode" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element type="xs:dateTime" name="FTVDate" minOccurs="0" maxOccurs="1" />
                              <xs:element type="xs:dateTime" name="InitialSecureDate" minOccurs="0" maxOccurs="1" />
                              <xs:element type="xs:dateTime" name="LastSecureDate" minOccurs="0" maxOccurs="1" />
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="Order" maxOccurs="unbounded" minOccurs="1">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="RequestedProduct" minOccurs="1" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="40" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="OrderedProduct" minOccurs="1" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="40" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="IsFrequent" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element type="xs:integer" name="OrderNumber" minOccurs="1" maxOccurs="1" />
                              <xs:element type="xs:integer" name="WorkOrderNumber" minOccurs="1" maxOccurs="1" />
							  <xs:element type="xs:integer" name="ClientOrderNumber" minOccurs="0" maxOccurs="1" />
                              <xs:element name="OrderSource" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="Department" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element type="xs:dateTime" name="CreatedDate" minOccurs="1" maxOccurs="1" />
                              <xs:element type="xs:dateTime" name="CompletedDate" minOccurs="0" maxOccurs="1" />
                              <xs:element name="IsCancellation" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element type="xs:dateTime" name="CancellationDate" minOccurs="0" maxOccurs="1" />
                              <xs:element name="CancellationReason" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="General" maxOccurs="unbounded" minOccurs="0">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element type="xs:dateTime" name="InspectionDate1" minOccurs="0" maxOccurs="1" />
                              <xs:element type="xs:dateTime" name="InspectionDate2" minOccurs="0" maxOccurs="1" />
                              <xs:element type="xs:dateTime" name="InspectionDate3" minOccurs="0" maxOccurs="1" />
                              <xs:element name="InspectorUniqueId" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="25" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="KeyCode" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="40" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="LockBoxCode" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="40" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="GateCode" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="40" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="DoorCardLeft" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="NoDoorCardLeftReason" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="4000" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="IsCorrectedAddress" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="CorrectedAddress" maxOccurs="unbounded" minOccurs="0">
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="Address1" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="100" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Address2" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="100" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="City" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="100" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="State" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="2" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Zip" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="5" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="HowWasCorrectedAddressFound" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="4000" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                              <xs:element name="GeoLongitude" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="20" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="GeoLatitude" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="20" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="HOA" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="GatedCommunity" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="IsCommunityInfoAvailable" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="CommunityName" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="CommunityNumber" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="CommunityAddress" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="SupplierNotes" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="4000" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="Comments" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="4000" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element type="xs:dateTime" name="TimeOfInspection" minOccurs="0" maxOccurs="1" />
                              <xs:element name="ViolationExists" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="IsWorkPerformed" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="WorkNotPerformed" maxOccurs="unbounded" minOccurs="0">
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="WorkNotPerformedReason" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="BadAddress" maxOccurs="unbounded" minOccurs="0">
                                          <xs:complexType>
                                             <xs:sequence>
                                                <xs:element name="BadAddressReasons" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="8000" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="BadAddressReasonOther" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="1000" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="CheckedWith" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="8000" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                             </xs:sequence>
                                          </xs:complexType>
                                       </xs:element>
                                       <xs:element name="AccessDenied" maxOccurs="unbounded" minOccurs="0">
                                          <xs:complexType>
                                             <xs:sequence>
                                                <xs:element name="AccessDeniedReason" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="500" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="AccessDeniedCommonEntracnceCommunityName" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="100" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="AccessDeniedCommonEntracnceCommunityNumber" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="100" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="AccessDeniedCommonEntracnceCommunityAddress" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="100" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="KeyCodeReason" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="500" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="KeyCodeReasonOther" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="1000" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="WillWeatherCondLastMoreThan7Days" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="WeatherConditionExplain" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="1000" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Guard" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="500" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="IsViewableFromStreet" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="NoTrespassingExplain" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="1000" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="AccessibilityConstraintsExplain" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="1000" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                             </xs:sequence>
                                          </xs:complexType>
                                       </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="Neighborhood" maxOccurs="unbounded" minOccurs="0">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="NeighborhoodIs" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="OverHeadPowerLines" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="School" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="Airport" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="WasteManagementFacility" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="DrugActivity" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="RailroadTracks" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="NonResidentialOrCommercial" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="HighVandalism" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="BoardedHomes" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="FreewayHighway" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="CommercialUse" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="EnvHazard" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="PotentialHazardMaterials" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="IndustrialPlants" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="SuspiciousOdors" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="Dwelling" maxOccurs="unbounded" minOccurs="0">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="DwellingType" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="DwellingTypeOther" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="Unit1IdNumber" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="20" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="Unit2IdNumber" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="20" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="Unit3IdNumber" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="20" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="Unit4IdNumber" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="20" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="MobileHome" maxOccurs="unbounded" minOccurs="0">
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="Make" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="100" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Model" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="100" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Size" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="VinNumber" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="100" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="HudNumber" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="100" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="ReasonForNoVinOrHud" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="200" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="OnFoundation" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="StrappingInPlace" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="PermStructureAttached" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="AxlesRemoved" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="MobileHomeComments" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="4000" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="InMobileHomePark" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="AffixedToRealProperty" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="SkirtingInstalled" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="WheelsRemoved" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="TongueRemoved" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                              <xs:element name="NumStories" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="GarageType" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="GarageTypeOther" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="200" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ExtDwellingCondition" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="DwellingStyle" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="DwellingStyleOther" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ConstructionType" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ConstructTypeOther" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="200" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="AddStructsOnsite" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="TypeOfStructure" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="8000" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ConstructionInProgress" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ConstructInProgUnknownExplain" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="4000" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="Property" maxOccurs="unbounded" minOccurs="0">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="PropertyConforms" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="PropertyForRent" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ForRentBy" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="RentOwnerAgentPhone" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="10" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="RentAgentName" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="FTVPosted" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="WinterStickerPosted" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="HVACCentralUnitPresent" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="HVACPresentUnknownReason" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="HVACPresentUnknownOther" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="4000" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="LawnType" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="GrassHeight" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="LawnTypeOther" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="200" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="RoofTarpPresent" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="isPropertyForSale" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ForSaleBy" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ForSaleOwnerAgentPhone" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="10" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ForSaleAgentName" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="VPRPosted" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
							  <xs:element name="LockBoxPresent" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="7" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="HouseLeftSideView" minOccurs="0" maxOccurs="1">
                               <xs:simpleType>
                                 <xs:restriction base="xs:string">
                                   <xs:minLength value="0" />
                                   <xs:maxLength value="11" />
                                 </xs:restriction>
                               </xs:simpleType>
                             </xs:element>
                             <xs:element name="HouseLeftSideViewPhoto" minOccurs="0" maxOccurs="1">
                               <xs:simpleType>
                                 <xs:restriction base="xs:string">
                                   <xs:minLength value="0" />
                                   <xs:maxLength value="255" />
                                 </xs:restriction>
                               </xs:simpleType>
                             </xs:element>
                             <xs:element name="HouseRightSideView" minOccurs="0" maxOccurs="1">
                               <xs:simpleType>
                                 <xs:restriction base="xs:string">
                                   <xs:minLength value="0" />
                                   <xs:maxLength value="11" />
                                 </xs:restriction>
                               </xs:simpleType>
                             </xs:element>
                             <xs:element name="HouseRightSideViewPhoto" minOccurs="0" maxOccurs="1">
                               <xs:simpleType>
                                 <xs:restriction base="xs:string">
                                   <xs:minLength value="0" />
                                   <xs:maxLength value="255" />
                                 </xs:restriction>
                               </xs:simpleType>
                             </xs:element>
                             <xs:element name="StreetSign" minOccurs="0" maxOccurs="1">
                               <xs:simpleType>
                                 <xs:restriction base="xs:string">
                                   <xs:minLength value="0" />
                                   <xs:maxLength value="11" />
                                 </xs:restriction>
                               </xs:simpleType>
                             </xs:element>
                             <xs:element name="StreetSignPhoto" minOccurs="0" maxOccurs="1">
                               <xs:simpleType>
                                 <xs:restriction base="xs:string">
                                   <xs:minLength value="0" />
                                   <xs:maxLength value="255" />
                                 </xs:restriction>
                               </xs:simpleType>
                             </xs:element>
							 <xs:element name="RequiredSignsPosted" minOccurs="0" maxOccurs="1">
                               <xs:simpleType>
                                 <xs:restriction base="xs:string">
                                   <xs:minLength value="0" />
                                   <xs:maxLength value="3" />
                                 </xs:restriction>
                               </xs:simpleType>
                             </xs:element>
                             <xs:element name="Sign-inSheetPresent" minOccurs="0" maxOccurs="1">
                               <xs:simpleType>
                                 <xs:restriction base="xs:string">
                                   <xs:minLength value="0" />
                                   <xs:maxLength value="3" />
                                 </xs:restriction>
                               </xs:simpleType>
                             </xs:element>
							 <xs:element name="Sign-inSheetShowingInspectionCompleted" minOccurs="0" maxOccurs="1">
                               <xs:simpleType>
                                 <xs:restriction base="xs:string">
                                   <xs:minLength value="0" />
                                   <xs:maxLength value="3" />
                                 </xs:restriction>
                               </xs:simpleType>
                             </xs:element>
                             <xs:element name="CrawlSpaceFlooded" minOccurs="0" maxOccurs="1">
                               <xs:simpleType>
                                 <xs:restriction base="xs:string">
                                   <xs:minLength value="0" />
                                   <xs:maxLength value="3" />
                                 </xs:restriction>
                               </xs:simpleType>
                             </xs:element>
                             <xs:element name="InchesOfWaterInCrawlSpace" minOccurs="0" maxOccurs="1">
                               <xs:simpleType>
                                 <xs:restriction base="xs:long">
                                   <xs:totalDigits value="18" />
                                   <xs:fractionDigits value="0" />
                                 </xs:restriction>
                               </xs:simpleType>
                             </xs:element>
                             <xs:element name="CondemnableStructuralDefects" minOccurs="0" maxOccurs="1">
                               <xs:simpleType>
                                 <xs:restriction base="xs:string">
                                   <xs:minLength value="0" />
                                   <xs:maxLength value="3" />
                                 </xs:restriction>
                               </xs:simpleType>
                             </xs:element>
                          <xs:element name="DefectiveSteps" minOccurs="0" maxOccurs="1">
                            <xs:simpleType>
                              <xs:restriction base="xs:string">
                                <xs:minLength value="0" />
                                <xs:maxLength value="3" />
                              </xs:restriction>
                            </xs:simpleType>
                          </xs:element>
                          <xs:element name="TrippingHazards" minOccurs="0" maxOccurs="1">
                            <xs:simpleType>
                              <xs:restriction base="xs:string">
                                <xs:minLength value="0" />
                                <xs:maxLength value="3" />
                              </xs:restriction>
                            </xs:simpleType>
                          </xs:element>
                          <xs:element name="DefectiveStairRailings" minOccurs="0" maxOccurs="1">
                            <xs:simpleType>
                              <xs:restriction base="xs:string">
                                <xs:minLength value="0" />
                                <xs:maxLength value="3" />
                              </xs:restriction>
                            </xs:simpleType>
                          </xs:element>
                          <xs:element name="MissingDeckRailing" minOccurs="0" maxOccurs="1">
                            <xs:simpleType>
                              <xs:restriction base="xs:string">
                                <xs:minLength value="0" />
                                <xs:maxLength value="3" />
                              </xs:restriction>
                            </xs:simpleType>
                          </xs:element>   
                         </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="PoolSpa" maxOccurs="unbounded" minOccurs="0">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="PoolOnsite" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="PoolDetail" maxOccurs="unbounded" minOccurs="0">
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="PoolType" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="IsPoolSecure" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="IsPoolSecureRecommended" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="PoolSecureRecommendedExplain" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="4000" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="PoolDamage" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="RemoveAboveGroundPool" minOccurs="0" maxOccurs="1">
                                        <xs:simpleType>
                                          <xs:restriction base="xs:string">
                                            <xs:minLength value="0" />
                                            <xs:maxLength value="500" />
                                          </xs:restriction>
                                        </xs:simpleType>
                                      </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                              <xs:element name="SpaHotTubOnsite" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="SpaHotTubDetail" maxOccurs="unbounded" minOccurs="0">
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="Integrated" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Secured" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="Violations" maxOccurs="unbounded" minOccurs="0">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="ViolationDetail" maxOccurs="unbounded" minOccurs="0">
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="AgencyNameAvailable" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="AgencyName" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="50" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="AgencyPhoneAvailable" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="AgencyPhone" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="10" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="ViolationDateAvailable" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element type="xs:dateTime" name="ViolationDate" minOccurs="0" maxOccurs="1" />
                                       <xs:element name="InfoNotAvailComments" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="300" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element type="xs:dateTime" name="CureByDate" minOccurs="0" maxOccurs="1" />
                                       <xs:element name="ViolationComments" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="300" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="ViolationType" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="8000" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="ViolationOther" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="30" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                    </xs:sequence>
                                    <xs:attribute type="xs:integer" name="ViolationId" use="optional" />
                                 </xs:complexType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="ExteriorDamages" maxOccurs="unbounded" minOccurs="0">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element type="xs:decimal" name="TotalPriorEstDamage" minOccurs="0" maxOccurs="1" />
                              <xs:element type="xs:decimal" name="TotalEstDamage" minOccurs="0" maxOccurs="1" />
                              <xs:element name="DamageComments" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="1000" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="PropertyDamageBeyondRepair" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ExteriorDamageExists" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ExteriorDebrisPresent" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ExplainExteriorDebrisPresent" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="4000" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="CausedBy" maxOccurs="unbounded" minOccurs="0">
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="BoilerExplosion" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Earthquake" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Explosion" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Fire" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Freeze" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Hail" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="LandslideMudslide" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Mold" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Plumbing" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Smoke" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="SuddenRupture" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Tornado" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Vehicle" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="WeightOfIceSnowSleet" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="DischargeOrOverflowOfWater" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="FallingObjects" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Flood" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Graffiti" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Hurricane" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Lightning" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="OwnerNeglect" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="RoofLeak" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="StormWind" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Theft" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Vandalism" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Water" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="EnvironmentHazard" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Other" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="ExplainExteriorDamageDuetoOther" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="4000" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                              <xs:element name="RoofDamage" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ChimneyDamage" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="DrivewayWalkwayDamage" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="MissingWallOrDamagedWalls" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="OutbuildingDamage" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="OtherDamage" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="OtherDamageExlained" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="4000" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="DamageIsSafertyConcern" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="HazardousMaterials" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="HazardousMaterialsExplained" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="4000" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="FoundationDamage" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="FasciaSoffitDamage" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="DefectivePaint" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ExposedElectrical" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="SidingDamage" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="RecommendedServices" maxOccurs="unbounded" minOccurs="0">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="MaintResecureRecommend" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="MaintResecureRecommendUnknownReason" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="4000" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="Winterization" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="CutGrass" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="RemoveHazardousMaterial" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="Other" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="OtherDesc" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ChangeLocks" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="SecurePoolTub" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="TrimTreeShrub" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="RemoveDebris" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="RepairGutterOrDownspouts" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="BoardScreenOpenings" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="Occupancy" maxOccurs="unbounded" minOccurs="0">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="OccupancyStatus" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="VerifiedBy" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="VacantVisualReasons" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="8000" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="VacantVisualOtherReason" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="800" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="OccupiedVisualReasons" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="8000" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="FurnitureLocation" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="OccupiedVisualOtherReason" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="800" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="SpokeWith" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="SpokeWithOtherReason" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="800" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="SpokeWithName" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="SpokeWithAddress" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="SpokeWithPhoneNumber" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="10" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="SpokeWithAttitude" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="OtherExplanation" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="800" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="NotSecureReasons" maxOccurs="unbounded" minOccurs="0">
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="FrontDoor" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="SideDoor" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="BasementDoor" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="GarageDoor" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="PetDoor" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="CrawlSpace" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="BackDoor" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Window" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="MultipleWindowsUnsecure" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="LocationUnsecureWindows" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="8000" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Shed" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="OutBuilding" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Other" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="OtherDesc" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="800" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="Utilities" maxOccurs="unbounded" minOccurs="0">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="UtilityInformation" maxOccurs="unbounded" minOccurs="0">
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="ElectricActive" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="ElectricActiveUnknownReason" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="ElectricNoAccessExplain" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="800" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="IsElectricShared" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="ElectricVerifiedBy" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="ElectricVerifiedByOtherExplained" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="800" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="ElectricMeterReading" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="10" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="ElectricSerialNumber" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="30" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="ElectricCompany" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="100" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="ElectricPhone" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="10" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="ElectricRepresentative" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="100" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="GasActive" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="GasActiveUnknownReason" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="GasNoAccessExplain" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="800" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="IsGasShared" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="GasVerifiedBy" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="GasVerifiedByOtherExplained" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="800" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="GasMeterReading" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="10" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="GasSerialNumber" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="30" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="GasCompany" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="100" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="GasPhone" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="10" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="GasRepresentative" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="100" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="WaterActive" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="WaterActiveUnknownReason" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="WaterNoAccessExplain" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="800" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="IsWaterShared" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="WaterVerifiedBy" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="WaterVerifiedByOtherExplained" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="800" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="WaterMeterReading" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="10" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="WaterSerialNumber" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="30" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="WaterCompany" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="100" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="WaterPhone" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="10" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="WaterRepresentative" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="100" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                    </xs:sequence>
                                    <xs:attribute name="UnitNumber" use="optional" />
                                 </xs:complexType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="Interior" maxOccurs="unbounded" minOccurs="0">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="InteriorInfo" maxOccurs="unbounded" minOccurs="0">
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="InteriorInspectionComplete" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="InteriorNotCompletedReason" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="InteriorNotCompletedOtherReasonExplained" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="200" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="InteriorAccessedBy" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="InteriorAccessedByOtherExplained" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="200" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="SecureUponArrival" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="InteriorDwellingCondition" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Attic" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Basement" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="InteriorStandingWater" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="PersonalProperty" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="WinterizationStickerPosted" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="VisualSignsOfWinterization" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="SumpPumpOrBasinPresent" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="SumpPumpOperational" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="SumpPumpNotOperationalReason" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="8000" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="SumpPumpNotOperationalOtherReasonExplained" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="800" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Dishwasher" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="WaterHeater" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Furnace" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="GarbageDisposal" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Oven" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="KitchenVent" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Washer" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Refrigerator" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="BuiltInMicrowave" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="HVACUnit" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="WindowACUnits" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Dryer" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Range" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Stove" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="500" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Other" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="OtherAppliancesPresentExplained" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="800" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                    </xs:sequence>
                                    <xs:attribute name="UnitNumber" use="optional" />
                                 </xs:complexType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="InteriorDamages" maxOccurs="unbounded" minOccurs="0">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="Damage" maxOccurs="unbounded" minOccurs="0">
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="InteriorDamageExists" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="InteriorDamageLocation" maxOccurs="unbounded" minOccurs="0">
                                          <xs:complexType>
                                             <xs:sequence>
                                                <xs:element name="Kitchen" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="LivingRoom" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="BathRoom" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="OtherBedroom" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Attic" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="DiningRoom" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="FamilyRoom" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="MasterBedroom" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Basement" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Garage" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="OtherArea" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="OtherAreaExplained" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="200" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                             </xs:sequence>
                                          </xs:complexType>
                                       </xs:element>
                                       <xs:element name="SafetyConcern" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="HazardousMaterials" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="HazardousMaterialsExplained" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="200" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="Debris" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="DebrisExplained" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="200" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="DefectivePaint" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="ExposedElectrical" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="WallDamage" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="OtherDamage" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="3" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="OtherDamageExplained" minOccurs="0" maxOccurs="1">
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:minLength value="0" />
                                                <xs:maxLength value="200" />
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="InteriorDamageDueTo" maxOccurs="unbounded" minOccurs="0">
                                          <xs:complexType>
                                             <xs:sequence>
                                                <xs:element name="DischargeOrOverflowOfWater" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="EnvironmentHazard" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="FallingObject" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Flood" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Graffiti" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Hurricane" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Lightning" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="OwnerNeglect" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="RoofLeak" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="StormWind" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Theft" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Vandalism" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Water" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="BoilerExplosion" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="EarthQuake" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Explosion" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Fire" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Freeze" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Hail" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="MudSlide" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Landslide" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Mold" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Plumbing" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Smoke" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Rupture" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Tornado" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="Vehicle" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="WeightOfIceSnowSleet" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="OtherCause" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="3" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="OtherCauseExplained" minOccurs="0" maxOccurs="1">
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:string">
                                                         <xs:minLength value="0" />
                                                         <xs:maxLength value="200" />
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                             </xs:sequence>
                                          </xs:complexType>
                                       </xs:element>
                                    </xs:sequence>
                                    <xs:attribute name="UnitNumber" use="optional" />
                                 </xs:complexType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="FEMA" maxOccurs="unbounded" minOccurs="0">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="NeighborhoodDamageLevel" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="NeighborhoodCleanupStatus" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="IsPropertyHabitable" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="MortgagorRemain" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="MortgagorRepair" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="InsuranceFundsReceived" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element type="xs:decimal" name="InsuranceAmountReceived" minOccurs="0" maxOccurs="1" />
                              <xs:element name="AppliedForGovernmentAssitance" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="GovernmentAssistanceReceived" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element type="xs:decimal" name="GovernmentAssistanceAmountReceived" minOccurs="0" maxOccurs="1" />
                              <xs:element name="HazardClaimFiled" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="FloodClaimFiled" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="PropertyDamageLevel" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="CostOfDamageRange" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="PropertyCleanupStatus" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="500" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="InsuranceLoss" maxOccurs="unbounded" minOccurs="0">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element type="xs:integer" name="PercentComplete" minOccurs="0" maxOccurs="1" />
                              <xs:element name="ExteriorComplete" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="8000" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ExteriorCompleteOther" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ExteriorIncomplete" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="8000" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="ExteriorIncompleteOther" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="InteriorComplete" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="8000" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="InteriorCompleteOther" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="InteriorIncomplete" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="8000" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="InteriorIncompleteOther" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="100" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="SignatureObtained" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="3" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="SignatureNotObtainedComments" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:minLength value="0" />
                                       <xs:maxLength value="1000" />
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                  </xs:sequence>
                  <xs:attribute type="xs:integer" name="OrderId" use="optional" />
                  <xs:attribute type="xs:integer" name="WorkOrderId" use="optional" />
               </xs:complexType>
            </xs:element>
         </xs:sequence>
      </xs:complexType>
   </xs:element>
</xs:schema>
