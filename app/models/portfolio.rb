##
# A Portfolio is a collection of {Property}s.
#
# For someone like MPH who has 2k+ top level properties, it helps to
# show them regionally instead of all at once. they use it for that, as well as
# filtering, for example, 'a profit and loss report for the midwest region of
# properties'.
#
# In BU's case, they don't have a ton of properties, but they have different
# kinds for leasing -- student housing, graduate housing, faculty housing, and
# 'visiting scholars' (basically a hotel / airbnb style operation). They use
# groups to filter the bookings table for availability depending on , for
# example, what kind of phone call they get about availability.
#
# For someone like VM, they don't really need them since they only have ~10
# buildings and theyre all in Detroit. Their portfolio homescreen skips the
# group step entirely.
#
# For someone like TN, they only have a handful of properties, but they are
# grouped by the LLC that owns the property.
#
# An additional use case is someone like RM who has ~100 apartment buildings
# across the US that are managed not really by region but actually by
# 'portfolio', or basically separate management teams.
class Portfolio < ApplicationRecord
  include AccountingContext
  include Brandable
  include HasOccupancy
  include Portfolio::Searchable

  belongs_to :configuration

  has_many :companies, dependent: :restrict_with_error
  has_many :properties, through: :companies
  has_many :units, through: :properties
  has_many :floorplans, through: :properties
  has_many :tenants, through: :properties
  has_many :property_memberships, as: :target, inverse_of: :target, dependent: :destroy
  has_many :portfolios_bank_accounts, dependent: :destroy
  has_many :bank_accounts, through: :portfolios_bank_accounts
  has_many :member_onboardings, class_name: 'MemberOnboarding::Configuration', dependent: :nullify
  has_many :entity_vendor_preferences, as: :entity, dependent: :destroy
  has_many :vendors, through: :entity_vendor_preferences

  validates :configuration, presence: true, if: -> { setup? }
  validates :name, presence: true

  def to_s
    name
  end

  def url
    routes.portfolio_path(self)
  end

  def brand
    super || Customer.current.brand
  end
end
