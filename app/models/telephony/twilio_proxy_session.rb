class Telephony::TwilioProxySession < ApplicationRecord
  belongs_to :resource, polymorphic: true
  belongs_to :participant_one, polymorphic: true
  belongs_to :participant_two, polymorphic: true

  validates :sid, presence: true
  validates :resource, presence: true
  validates :participant_one, presence: true
  validates :participant_one_sid, presence: true
  validates :participant_two, presence: true
  validates :participant_two_sid, presence: true

  def send_message_from(participant, body:)
    if participant_one == participant
      send_message_to(participant_two, body: body)
    elsif participant_two == participant
      send_message_to(participant_one, body: body)
    else
      fail 'Invalid Participant.'
    end
  end

  def send_message_to(participant, body:)
    participant_instance(participant)
      .message_interactions
      .create(body: body)
  end

  def last_interaction_date
    session_instance.fetch.date_last_interaction
  end

  def delete_session_instance
    session_instance.delete
  end

  private

  def session_instance
    proxy_service_instance.sessions(sid)
  end

  def participant_instance(participant)
    participant_sid = if participant_one == participant
                        participant_one_sid
                      elsif participant_two == participant
                        participant_two_sid
                      else
                        fail 'Invalid Participant.'
                      end

    session_instance.participants(participant_sid)
  end

  delegate :proxy_service_instance, to: :resource
end
