class Messaging::ContactGroupMembership < ApplicationRecord
  belongs_to :contact_group, counter_cache: :contacts_count
  belongs_to :contact, polymorphic: true

  validates :contact_group_id,
            uniqueness: {
              scope: [:contact_id, :contact_type],
              message: 'already has this member'
            }

  delegate :first_name, :last_name, :name, :email, :phone, :formatted_phone, :to_sgid, :url,
           to: :contact
end
