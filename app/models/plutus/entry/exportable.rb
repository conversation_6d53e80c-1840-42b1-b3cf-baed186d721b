module Plutus::Entry::Exportable
  include ::Exportable::XLSX

  HEADER_VALUES = %w[Account Debits Credits].freeze

  def as_xlsx
    {
      title: description,
      subheader: subheader,
      header: {
        cells: HEADER_VALUES.map { |value| { value: value, type: 'b' } }
      },
      rows: rows
    }
  end

  private

  def subheader
    [
      journal.name,
      'on',
      date.to_fs(:human_date)
    ].join(' ')
  end

  def rows
    total_debits = Money.zero
    total_credits = Money.zero

    amount_rows = sorted_amounts.map do |amount|
      debits, credits = if amount.debit?
                          [Money.new(amount.amount), Money.zero]
                        else
                          [Money.zero, Money.new(amount.amount)]
                        end

      total_debits += debits
      total_credits += credits

      {
        cells: [
          { value: amount.account.display_name },
          { value: (debits if debits.nonzero?) },
          { value: (credits if credits.nonzero?) }
        ]
      }
    end

    blank = { cells: [] }

    total_row = {
      cells: [
        { value: 'Total', type: 'b' },
        { value: total_debits, type: 'b' },
        { value: total_credits, type: 'b' }
      ]
    }

    amount_rows + [blank] + [total_row]
  end
end
