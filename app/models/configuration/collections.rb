module Configuration::Collections
  extend ActiveSupport::Concern

  PERMITTED_PARAMS = %i[
    minimum_demand_letter_balance
    prevent_electronic_payments_while_evicting
    demand_letter_tenant_charge_preset_id
    demand_letter_owner_charge_preset_id
    demand_letter_owner_mark_paid
    eviction_owner_charge_preset_id
    eviction_owner_mark_paid
    eviction_tenant_charge_preset_id
  ].freeze

  included do
    monetize :minimum_demand_letter_balance_cents
  end
end
