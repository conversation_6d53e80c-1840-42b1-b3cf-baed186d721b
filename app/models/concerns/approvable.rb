module Approvable
  extend ActiveSupport::Concern

  included do
    belongs_to :approval_requested_by, class_name: '<PERSON><PERSON><PERSON><PERSON>',
                                       optional: true

    belongs_to :approved_by, class_name: '<PERSON><PERSON>anager',
                             optional: true
  end

  def requires_approval?
    approving_role.present?
  end

  def approvable_by?(user)
    user.role == approving_role
  end

  def approval_requested?
    approval_requested_at.present?
  end

  def approved?
    approved_at.present?
  end

  def unapproved?
    !approved?
  end

  def awaiting_approval?
    requires_approval? && approval_requested? && !approved?
  end

  def request_approval!(user)
    update!(
      approval_requested_by: user,
      approval_requested_at: Time.zone.now
    )
  end

  def approve!(user)
    update!(
      approved_by: user,
      approved_at: Time.zone.now
    )

    after_approve
  end

  def remove_approval!
    update!(
      approved_by: nil,
      approved_at: nil
    )
  end

  def remove_approval_request!
    update!(
      approval_requested_by: nil,
      approval_requested_at: nil
    )
  end

  protected

  def after_approve; end
end
