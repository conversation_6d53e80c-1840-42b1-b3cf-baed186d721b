module LeaseApplication::SubmissionValidations
  extend ActiveSupport::Concern

  included do
    with_options if: :submitting_now? do
      validate :verify_satisfied_document_requirements_on_submission
      validate :verify_exactly_one_primary_applicant
      validate :verify_screening_addresses
    end
  end

  private

  def submitting_now?
    persisted? && submitted_at_changed? && submitted_at_was.nil?
  end

  def verify_satisfied_document_requirements_on_submission
    return unless version >= 7

    applicants.each do |applicant|
      next if applicant.satisfied_document_requirements?

      decorator = LeaseApplication::ApplicantDecorator.new(applicant)

      message = "#{applicant.name}: #{decorator.missing_documents_description}"

      errors.add(:base, message)
    end
  end

  def verify_exactly_one_primary_applicant
    return unless version >= 7

    return if applicants.one?(&:primary_applicant?)

    errors.add(:base, 'Application must have exactly one primary applicant')
  end

  def verify_screening_addresses
    return unless version >= 7

    applicants.select(&:requires_background_check?).each do |applicant|
      address = applicant.address

      next if address.region_code.present? && address.valid?

      errors.add(:base, "#{applicant.name} must have a valid address")
    end
  end
end
