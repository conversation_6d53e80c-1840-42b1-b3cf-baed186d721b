module Lease::HasNextRentDate
  def next_rent_date
    return nil if archived?

    possibly_upcoming_rent_dates.select(&:future?).min&.to_date
  end

  private

  def configured_rent_date_for_period(period)
    Configuration::RentSchedule.new(
      configuration: configuration,
      month: period.beginning_of_month
    ).post_date.in_time_zone.change(hour: 7)
  end

  def possibly_upcoming_rent_dates
    dates_to_check = [
      start_date,
      Time.zone.today,
      Time.zone.today.next_month,
      Time.zone.today.next_month.next_month,
      end_date
    ].select { |date| date.in?(start_date..end_date) }

    dates_to_check.map(&method(:configured_rent_date_for_period))
  end
end
