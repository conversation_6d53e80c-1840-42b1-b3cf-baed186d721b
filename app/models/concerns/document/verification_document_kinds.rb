# rubocop:disable Naming/VariableNumber
module Document::VerificationDocumentKinds
  extend ActiveSupport::Concern

  included do
    enum :verification_document_kind, {
      unspecified: 0,
      other: 1,
      drivers_license: 10,
      passport: 11,
      school_identification: 12,
      military_identification: 13,
      other_photo_id: 2,
      previous_rental_ledger: 3,
      paystub: 4,
      proof_of_income_1099: 5,
      proof_of_income_w2: 6,
      bank_statement: 7,
      pet_photo: 8,
      pet_vaccination_record: 9
    }, prefix: :verification_document
  end

  def self.photo_identification_kinds
    %i[
      drivers_license
      passport
      school_identification
      military_identification
    ]
  end

  def self.income_proof_kinds
    %i[
      bank_statement
      paystub
      proof_of_income_w2
      proof_of_income_1099
    ]
  end

  def self.display_name(kind)
    {
      drivers_license: 'Drivers License or State Issued ID',
      school_identification: 'School ID',
      military_identification: 'Military ID',
      other_photo_id: 'Other Photo ID',
      paystub: 'Pay Stub',
      proof_of_income_1099: 'Proof of Income (1099)',
      proof_of_income_w2: 'Proof of Income (W-2)'
    }[kind.to_sym] || kind.to_s.titleize
  end

  def self.configuration_display_name(kind)
    display_name(kind)
      .gsub('Proof of Income (W-2)', 'Recent W-2')
      .gsub('Proof of Income (1099)', 'Recent 1099')
  end

  def self.dropdown_values
    kinds = Document.verification_document_kinds.keys.without(
      'unspecified', 'other'
    )

    [['Select', :unspecified]] +
      kinds.map { |key| [display_name(key), key] } +
      [['Other', :other]]
  end
end
# rubocop:enable Naming/VariableNumber
