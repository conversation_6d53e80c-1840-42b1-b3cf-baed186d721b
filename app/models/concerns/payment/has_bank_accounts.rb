module Payment::HasBankAccounts
  extend ActiveSupport::Concern

  included do
    # Deposits
    belongs_to :credit_bank_account,
               class_name: 'BankAccount',
               inverse_of: :credits

    alias_attribute :deposit_bank_account, :credit_bank_account

    # Withdrawals
    belongs_to :debit_bank_account,
               class_name: 'BankAccount',
               inverse_of: :debits

    alias_attribute :withdrawal_bank_account, :debit_bank_account
  end
end
