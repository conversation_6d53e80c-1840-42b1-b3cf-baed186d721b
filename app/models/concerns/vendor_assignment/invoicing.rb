module VendorAssignment::Invoicing
  extend ActiveSupport::Concern

  REQUEST_INVOICE_DELAY = 1.hour.freeze

  def invoice_request_sent?
    invoice_request_sent_at.present?
  end

  def request_invoice_later!
    Vendors::Assignments::RequestInvoiceJob
      .set(wait: REQUEST_INVOICE_DELAY)
      .perform_later(vendor_assignment: self)
  end

  def invoices
    maintenance_ticket.linked_invoices.where(seller: vendor)
  end

  def accepting_invoice_submissions?
    return true unless maintenance_ticket # TODO: Handle project assignments

    if maintenance_ticket.customer_resolves?
      maintenance_ticket.resolved?
    else
      !maintenance_ticket.closed? ||
        maintenance_ticket.closed_at&.after?(7.days.ago)
    end
  end
end
