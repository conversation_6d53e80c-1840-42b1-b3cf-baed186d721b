##
# A collection of +Unit+s.
class Property < ApplicationRecord
  include AccountingContext
  include Archivable
  include Brandable
  include FormattedPhones
  include HasAddress
  include HasAttachments
  include HasFairRentalDays
  include HasMetadata
  include HasOccupancy
  include HasRooms
  include Inspection::Target
  include Invoicing
  include Lending::LoanProperty
  include OwnerContribution::Target
  include Property::ApiV2Resource
  include Property::CustomData
  include Property::CustomStatus
  include Property::MemberOnboardable
  include Property::OwnerElectronicDepositDelegation
  include Property::ManagementContractMember
  include Property::PropertyTypes
  include Property::Searchable
  include Property::Transferrable
  include Taggable
  include Utilities::ServiceLocation
  include Zillow::Claimable

  default_scope { order(:name) }

  belongs_to :company, optional: false
  belongs_to :configuration, optional: true

  has_many :parcels, dependent: :destroy
  has_many :buildings, dependent: :destroy
  has_many :parking_lots, dependent: :nullify
  has_many :units, dependent: :destroy
  has_many :leases, through: :units
  has_many :lease_memberships, through: :leases
  has_many :tenants, through: :lease_memberships
  has_many :scheduled_payments
  has_many :floorplans, inverse_of: :property, dependent: :destroy
  has_many :lease_applications, dependent: :destroy
  has_many :maintenance_tickets, dependent: :nullify
  has_many :tours, dependent: :destroy
  has_many :budgets, dependent: :destroy
  has_many :projects, dependent: :nullify
  has_one :hap_contract, dependent: :destroy
  has_many :simple_agreements, class_name: 'Agreements::SimpleAgreement', dependent: :destroy
  has_many :refinancing_invites, class_name: 'Financing::Refinance::Invite', dependent: :destroy
  has_many :inspection_reports, class_name: 'Inspection::Report', dependent: :destroy
  has_many :analyses, dependent: :destroy
  has_many :vendor_contract_memberships,
           class_name: 'Vendor::Contract::PropertyMembership',
           dependent: :destroy
  has_many :vendor_contracts,
           through: :vendor_contract_memberships,
           class_name: 'Vendor::Contract',
           source: :contract
  has_many :property_vendors, dependent: :destroy
  has_many :vendors, through: :property_vendors

  has_many :accounting_debit_card_purchases, class_name: 'Accounting::DebitCardPurchase'

  include HasOccupants

  validates :portfolio, presence: true, if: -> { setup? }
  validates :address, presence: true
  validates :name, presence: true
  validates :year_built, numericality: true, allow_nil: true

  accepts_nested_attributes_for :floorplans
  accepts_nested_attributes_for :parcels, reject_if: proc { |attrs|
    attrs['parcel_id'].blank?
  }, allow_destroy: true

  scope :setup, -> { where(setup: true) }

  strip_attributes only: %i[subdivision], collapse_spaces: true

  set_callback :archive, :before, :remove_scheduled_payments

  archived_by_parent :company
  archives_children :units

  def to_s
    name
  end

  def url
    routes.property_path(self)
  end

  def configuration
    super || portfolio&.configuration
  end

  def brand
    super || company.brand
  end

  def service_area
    self
  end

  def lead_paint_disclosure?
    year_built && year_built < 1978
  end

  delegate :portfolio, to: :company, allow_nil: true

  private

  def remove_scheduled_payments
    scheduled_payments.find_each(&:destroy!)
  end
end
