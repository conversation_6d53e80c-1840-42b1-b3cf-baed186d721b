class Tunisia::AuthorizedUser < ApplicationRecord
  include FormattedPhones
  include Tunisia::PhoneField

  default_scope -> { matching_access }

  scope :matching_access, -> { where(MATCHING_ACCESS_QUERY) }

  MATCHING_ACCESS_QUERY = <<~SQL.squish
    tunisia_authorized_users.id IN (
      SELECT DISTINCT ON (
        taus.company_id,
        taus.normalized_email
      ) taus.id as id
      FROM tunisia_authorized_users taus
      INNER JOIN property_managers ON (
        LOWER(TRIM(property_managers.email)) = taus.normalized_email AND
        property_managers.archived_at IS NULL)
      LEFT JOIN property_memberships company_property_memberships ON (
        company_property_memberships.user_id = property_managers.id AND
        company_property_memberships.target_type = 'Company' AND
        company_property_memberships.target_id = taus.company_id AND
        property_managers.top_level <> true )
      LEFT JOIN property_memberships property_property_memberships ON (
        property_property_memberships.user_id = property_managers.id AND
        property_property_memberships.target_type = 'Property' AND
        property_managers.top_level <> true)
      LEFT JOIN properties ON (
        property_property_memberships.target_id = properties.id)
      LEFT JOIN property_memberships portfolio_property_memberships ON (
        portfolio_property_memberships.user_id = property_managers.id AND
        portfolio_property_memberships.target_type = 'Portfolio' AND
        property_managers.top_level <> true)
      LEFT JOIN companies portfolio_companies ON (
        portfolio_property_memberships.target_id = portfolio_companies.portfolio_id AND
        portfolio_companies.id = taus.company_id)
      WHERE (property_managers.top_level = true OR
        taus.company_id IN (
          company_property_memberships.target_id,
          properties.company_id,
          portfolio_companies.id))
        ORDER BY taus.company_id,
          taus.normalized_email,
          (CASE WHEN property_managers.email = taus.email THEN true ELSE false END) DESC
      )
  SQL
                                .freeze

  belongs_to :company, optional: false, class_name: 'Company'

  belongs_to :user,
             foreign_key: :normalized_email,
             primary_key: :email,
             class_name: 'PropertyManager',
             optional: true,
             inverse_of: :tunisia_authorized_users

  validates :phone, presence: true
  validates :email, presence: true, uniqueness: { scope: :company_id }
  validates :primary, inclusion: { in: [true, false] }

  # TODO: check if we should audit
  audited

  def tunisia_formatted
    {
      fullName: {
        first: first_name,
        last: last_name
      },
      email: email,
      phone: tunisia_formatted_phone(Phony.normalize(phone)),
      jwtSubject: jwt_subject
    }
  end
end
