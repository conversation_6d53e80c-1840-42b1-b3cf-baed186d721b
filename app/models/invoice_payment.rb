class InvoicePayment < ApplicationRecord
  include InvoicePayment::ImpactsCashBasis
  include InvoicePayment::LockedPeriods
  include InvoicePayment::PreventedByEvictions

  belongs_to :invoice, inverse_of: :invoice_payments
  belongs_to :payment, inverse_of: :invoice_payments

  validates :invoice, presence: true
  validates :payment, presence: true
  validates :amount, numericality: { greater_than: 0 }
  validates_associated :invoice
  validates_associated :payment, unless: :avoid_validates_associated_loop_from_payment
  validate :valid_buyer_payer_pair
  validate :valid_seller_payee_pair

  # TODO: Remove this after determining cause of loop
  attr_accessor :avoid_validates_associated_loop_from_payment

  after_initialize :set_date
  before_validation :set_date

  monetize :amount_cents

  audited

  def reversed?
    reversed_at.present?
  end

  def reverse!(date = Time.zone.today)
    update!(reversed_at: date)
  end

  # Returns true if this apply happened after the payment was posted, i.e. it
  # descreases prepayment, rather than being a standard apply.
  def from_prepaid?
    date && payment&.date&.before?(date)
  end

  # TODO: Persisted cash basis matches materialized version,
  # does not use invoice_payment#date
  def from_prepaid_for_persisted_cash_basis?
    payment&.date&.before?(invoice&.post_date)
  end

  private

  def valid_buyer_payer_pair
    if Rails.env.production? && Feature.enabled?(:skip_invoice_payment_validations, Customer.current)
      return
    end
    return unless invoice&.buyer && payment&.payer

    return if invoice.buyer == payment.payer
    return if same_lease?(invoice.buyer, payment.payer)

    errors.add(:base, 'Invoice buyer does not match payment payer')
  end

  def valid_seller_payee_pair
    if Rails.env.production? && Feature.enabled?(:skip_invoice_payment_validations, Customer.current)
      return
    end
    return unless invoice&.seller && payment&.payee

    return if invoice.seller == payment.payee
    return if same_lease?(invoice.seller, payment.payee)

    errors.add(:base, 'Invoice seller does not match payment payee')
  end

  def same_lease?(one, two)
    return unless one.is_a?(Tenant) && two.is_a?(Tenant)

    [one, two].map(&:lease_ids).reduce(&:&).any?
  end

  def set_date
    return if date.present? || payment.nil? || invoice.nil?

    self.date = earliest_apply_date
  end
end
