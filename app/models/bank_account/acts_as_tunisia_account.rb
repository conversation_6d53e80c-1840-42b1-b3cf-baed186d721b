module BankAccount::ActsAsTunisiaAccount
  extend ActiveSupport::Concern

  included do
    has_one :tunisia_deposit_account,
            class_name: 'Tunisia::DepositAccount',
            dependent: :restrict_with_error

    validate :immutable_fields, on: :update, if: :tunisia_account?
  end

  def tunisia_account?
    tunisia_deposit_account.present?
  end

  private

  def immutable_fields
    return unless account_type_changed? || account_number_changed? || routing_number_changed?

    errors.add(:base,
               'Account type, account number, and routing number cannot be changed after creation.')
  end
end
