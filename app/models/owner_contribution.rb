class OwnerContribution < ApplicationRecord
  belongs_to :company, optional: false
  belongs_to :payment_method, polymorphic: true, optional: true
  belongs_to :request, optional: true

  validates :payment_method, presence: true, unless: :idle?

  has_many :allocations, dependent: :destroy

  accepts_nested_attributes_for :allocations, reject_if: :reject_allocation

  enum :status, {
    idle: 0,
    processing: 1,
    completed: 2,
    failed: 3
  }

  validates :amount, numericality: { greater_than: 0 }

  def amount
    Money.sum(allocations.map(&:amount))
  end

  def convenience_fee(merchant_account)
    Money.sum(allocations.map { |a| a.convenience_fee(merchant_account) })
  end

  private

  def reject_allocation(attributes)
    attributes['amount'].blank? || Monetize.parse(attributes['amount']).negative?
  end
end
