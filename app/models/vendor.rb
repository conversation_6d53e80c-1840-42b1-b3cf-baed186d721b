class Vendor < ApplicationRecord
  include Archivable
  include Avatar
  include BankAccountOwner
  include Vendor::Searchable # Contact overrides #as_index_json, so this needs to be first
  include Contact
  include HasAddress
  include HasAttachments
  include HasMetadata
  include HasPaymentMethods
  include Invoicing
  include Insurance::PolicyHolder
  include Inspection::Report::ActivityActor
  include Maintenance::BidSubmitter
  include Maintenance::TicketEventAuthor
  include Maintenance::TicketAssignee
  include Taggable
  include Taxpayer
  include Telephony::ProxySessionParticipant
  include Utilities::ServiceProvider
  include User::HasLoginFingerprints
  include Vendor::Court
  include Vendor::EvictionProcessor
  include Vendor::InspectionService

  default_scope -> { order(name: :asc) }

  has_many :vendor_contacts, dependent: :destroy
  has_one :invite, dependent: :destroy
  has_many :project_bids, dependent: :destroy, class_name: 'Project::Bid',
                          inverse_of: :vendor
  has_many :payments, as: :payer
  has_many :assigned_maintenance_ticket_events,
           class_name: 'MaintenanceTicket::Event',
           foreign_key: 'vendor_assignee_id',
           inverse_of: :vendor_assignee,
           dependent: :destroy
  has_many :sku_list_items, dependent: :destroy, class_name: 'SkuList::Item'
  has_many :contracts, dependent: :destroy
  has_many :entity_vendor_preferences, dependent: :destroy
  has_many :properties, through: :entity_vendor_preferences, source: :entity, source_type: 'Property'
  has_many :portfolios, through: :entity_vendor_preferences, source: :entity, source_type: 'Portfolio'
  has_many :accounting_debit_card_purchases, class_name: 'Accounting::DebitCardPurchase'

  validates :name, presence: true
  validates :kind, presence: true

  enum :kind, {
    contractor: 0,
    supplier: 1,
    financial: 2,
    municipal: 3,
    utility: 4,
    legal: 5
  }

  accepts_nested_attributes_for :vendor_contacts, reject_if: proc { |attributes|
    attributes['first_name'].blank?
  }, allow_destroy: true

  def at_least_one_vendor_contact
    return if vendor_contacts.any?

    errors.add(:vendor_contacts, 'must contain at least one vendor contact')
  end

  def to_s
    name
  end

  def url
    Rails.application.routes.url_helpers.vendor_path(self)
  end

  def sku_list
    SkuList.new(vendor: self)
  end

  def contact_type
    kind.humanize
  end

  def primary_contact
    vendor_contacts.first
  end

  def confirmed_account?
    vendor_contacts.any?(&:user_profile)
  end

  delegate :first_name, :last_name, :email, :phone, :formatted_phone,
           to: :primary_contact, allow_nil: true
end
