# TODO: remove when onboarding_enhancements is completed
class MemberOnboarding::Completion < ApplicationRecord
  belongs_to :configuration, class_name: 'MemberOnboarding::Configuration',
                             inverse_of: :member_completions,
                             optional: false

  belongs_to :tenant, class_name: 'Tenant',
                      inverse_of: :onboarding_completions,
                      optional: false
end
