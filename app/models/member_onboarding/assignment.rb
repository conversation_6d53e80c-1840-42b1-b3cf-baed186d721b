class MemberOnboarding::Assignment < ApplicationRecord
  belongs_to :configuration, class_name: 'MemberOnboarding::Configuration',
                             inverse_of: :member_assignments,
                             optional: false
  belongs_to :tenant, class_name: 'Tenant',
                      inverse_of: :onboarding_assignments,
                      optional: false

  scope :active, -> { where(completed_at: nil) }
  scope :completed, -> { where.not(completed_at: nil) }

  validates :tenant_id, uniqueness: { scope: :configuration_id }
  validates :tenant_id, uniqueness: {
    conditions: -> { active }
  }, if: -> { active? }

  def complete!(datetime = Time.zone.now)
    update!(completed_at: datetime)
  end

  def completed?
    completed_at.present?
  end

  def active?
    !completed?
  end
end
