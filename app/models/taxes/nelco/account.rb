class Taxes::Nelco::Account < ApplicationRecord
  attr_encrypted :password,
                 key: ENV.fetch('BACKGROUND_CHECK_KEY').first(32),
                 optional: false

  belongs_to :submitted_by,
             class_name: 'PropertyManager',
             inverse_of: :nelco_account,
             optional: false

  validates :email, presence: true
  validates :username, presence: true
  validates :password, presence: true
end
