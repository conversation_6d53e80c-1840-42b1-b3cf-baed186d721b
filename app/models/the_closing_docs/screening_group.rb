class TheClosingDocs::ScreeningGroup < ApplicationRecord
  belongs_to :lease_application, inverse_of: :income_verification,
                                 optional: false

  validates :status, presence: true

  enum :status, {
    not_started: 0,
    in_progress: 1,
    completed: 2
  }

  def pending?
    status.in?(%w[not_started in_progress])
  end

  def external_report_url
    @external_report_url ||= begin
      result = TheClosingDocs::ScreeningGroup::Report.call(self)
      result.url if result.successful?
    end
  end

  def screenings
    Array(data&.dig('screenings')).map do |screening_data|
      TheClosingDocs::Screening.new(
        group: self,
        status: screening_data['screening_status'],
        reason_completed: screening_data['reason_completed'],
        applicant: applicant(screening_data),
        monthly_recurring_income: monthly_recurring_income(screening_data['id'])
      )
    end
  end

  private

  def monthly_recurring_income(screening_id)
    # Other available keys are as follows, but three month is preferred based
    # on industry practices.
    #
    #   twelve_month_avg_monthly
    #   nine_month_avg_monthly
    #   six_month_avg_monthly
    #   total

    keys = %w[
      three_month_avg_monthly
      two_month_avg_monthly
    ]

    # Different schema for report data when multiple reports
    # https://docs.payscore.com/reports/getting-the-report-data#combined-report-json-data
    single_report_data =
      if report_data&.dig('reports')
        report_data['reports'].find do |entry|
          entry['screening_id'] == screening_id
        end
      else
        report_data
      end

    values = keys.map do |key|
      single_report_data&.dig('recurring', key)
    end

    Money.new(values.compact_blank.first)
  end

  def applicant(screening_data)
    lease_application.tenants.find do |tenant|
      tenant.email == screening_data['applicant_email']
    end || lease_application.tenants.first
  end
end
