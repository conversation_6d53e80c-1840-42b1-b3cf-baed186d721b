class User::LoginFingerprint < ApplicationRecord
  has_secure_token

  belongs_to :customer, optional: :public_schema?,
                        inverse_of: :user_login_fingerprints

  belongs_to :account, polymorphic: true

  validates :ip_address, presence: true, uniqueness: {
    scope: %i[customer_id account_id account_type]
  }

  validates :user_agent, presence: true

  scope :expired, lambda {
                    where(confirmed_at: nil)
                      .where('confirmation_sent_at < ?', 10.minutes.ago)
                  }

  delegate :email, to: :scoped_account

  def token_expired?
    confirmation_sent_at&.before?(10.minutes.ago)
  end

  def confirmed?
    confirmed_at.present?
  end

  def public_schema?
    account_type.in?(Apartment.excluded_models)
  end

  def parsed_user_agent
    return if user_agent.blank?

    @parsed_user_agent ||= UserAgentParser.parse(user_agent)
  end

  def resend_email
    regenerate_token
    send_email
  end

  def send_email
    Users::LoginFingerprints::ConfirmationNotificationJob.perform_now self
  end

  def scoped_account
    if public_schema?
      account
    else
      customer.activate { account }
    end
  end
end
