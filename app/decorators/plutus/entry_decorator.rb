class Plutus::EntryDecorator < ApplicationDecorator
  delegate_all

  def link(query_args = {}, options = {})
    h.link_to description,
              h.accounting_journal_entry_path(journal_id, object, query_args),
              options
  end

  def reference_link(query_args = {}, options = {})
    return unless payment

    h.link_to reference_text, commercial_document.url, options
  end

  def property_link
    return unless property

    h.link_to property.name, property.url
  end

  def reference_number
    payment&.formatted_reference_number || invoice&.invoice_number
  end

  def short_reference_number
    reference_number&.last(4)
  end

  def kind_text
    if payment
      payment.kind_text
    else
      'Entry'
    end
  end

  def reason_text
    kind.titleize
  end

  # Provides the appropriate path for the associated Invoice or Payment, if
  # any, including the appropriate payable or receivable side of the Invoice.
  def commercial_document_path
    case commercial_document
    when Payment
      payment_commercial_document_path
    when Invoice
      invoice_commercial_document_path
    end
  end

  # If this entry is for a commercial document, provides the appropriate edit
  # path for that commercial document, otherwise, the standard edit path,
  # including the cash basis parameter if necessary.
  def edit_path
    case commercial_document
    when Payment
      edit_payment_commercial_document_path
    when Invoice
      edit_invoice_commercial_document_path
    else
      h.edit_accounting_journal_entry_path(
        object.journal_id, object, basis: basis_parameter
      )
    end
  end

  def destroy_path
    h.accounting_journal_entry_path(
      object.journal_id, object, basis: basis_parameter
    )
  end

  private

  def payment
    commercial_document.decorate if commercial_document.is_a?(Payment)
  end

  def invoice
    commercial_document if commercial_document.is_a?(Invoice)
  end

  def basis_parameter
    :cash if object.cash_basis_only?
  end

  def payment_commercial_document_path
    h.accounting_payment_path(commercial_document)
  end

  def edit_payment_commercial_document_path
    h.edit_accounting_payment_path(commercial_document)
  end

  def invoice_commercial_document_path
    if journal == commercial_document.payable_journal
      h.accounting_payables_invoice_path(commercial_document)
    else
      h.accounting_receivables_invoice_path(commercial_document)
    end
  end

  def edit_invoice_commercial_document_path
    if journal == commercial_document.payable_journal
      h.edit_accounting_payables_invoice_path(commercial_document)
    else
      h.edit_accounting_receivables_invoice_path(commercial_document)
    end
  end
end
