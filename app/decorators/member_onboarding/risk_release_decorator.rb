class MemberOnboarding::RiskReleaseDecorator < ApplicationDecorator
  delegate_all

  def enrollment_start_date
    [coverage_start_date, Time.zone.today].max
  end

  def total_cost
    monthly_cost * num_enrollment_months
  end

  def num_enrollment_months
    num_months = ((coverage_end_date.year - enrollment_start_date.year) * 12) +
                 (coverage_end_date.month - enrollment_start_date.month)

    num_months += 1 if enrollment_start_date.day < coverage_end_date.day

    num_months
  end
end
