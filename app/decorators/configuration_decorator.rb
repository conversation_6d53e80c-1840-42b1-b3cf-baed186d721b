class ConfigurationDecorator < ApplicationDecorator
  delegate_all

  def primary_applicant_charge_description
    return 'No charge' unless primary_applicant_charge

    primary_applicant_charge.amount.format
  end

  def co_applicant_charge_description
    return 'No charge' unless co_applicant_charge

    "#{co_applicant_charge.amount.format} per person"
  end

  def guarantor_charge_description
    return 'No charge' unless guarantor_charge

    "#{guarantor_charge.amount.format} per person"
  end

  def occupant_charge_description
    return 'No charge' unless occupant_charge

    "#{occupant_charge.amount.format} per person"
  end
end
