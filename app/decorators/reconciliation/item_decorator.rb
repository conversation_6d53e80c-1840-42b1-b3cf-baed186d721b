class Reconciliation::ItemDecorator < ApplicationDecorator
  delegate_all

  def date
    super.to_datetime.to_fs(:short_date)
  end

  def contact
    contact_name
  end

  def reference
    path = payment_path || transaction_batch_path

    h.link_to_if path, reference_text, path
  end

  def memo
    path = entry_path || transaction_batch_path

    h.link_to_if path, description, path
  end

  def property
    return nil unless super

    h.link_to super.name, property_path(super)
  end

  def checkbox_class
    if transaction_batch_id
      'deposit-checkbox'
    else
      'amount-checkbox'
    end
  end

  private

  def entry_path
    return nil unless entry_id

    accounting_journal_entry_path(journal_id, entry_id)
  end

  def payment_path
    return nil unless payment_id

    accounting_payment_path(payment_id)
  end

  def transaction_batch_path
    return nil unless transaction_batch_id

    organization_bank_account_deposit_batch_path(
      transaction_batch.bank_account_id, transaction_batch_id
    )
  end
end
