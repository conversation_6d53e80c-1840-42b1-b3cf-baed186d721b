class DocumentTemplateForm
  include ActiveModel::Model

  attr_accessor :upload, :kind, :scope_sgid, :tenant_count, :vehicle_count

  validates :upload, presence: true
  validate :validate_docx_mime_type

  def save
    return false unless valid?

    ActiveRecord::Base.transaction do
      document = Document.create!(document_params)

      document.update!(
        direct_upload_url: document.upload.url,
        upload_file_name: upload.original_filename
      )
    end
  rescue ActiveRecord::RecordInvalid => e
    errors.merge!(e.record.errors) if e.record.errors.any?

    false
  end

  def scope
    return @scope if defined?(@scope)

    @scope = GlobalID::Locator.locate_signed(scope_sgid)
  end

  def self.human_attribute_name(attribute, options = {})
    return 'Document' if attribute.to_s == 'upload'

    super
  end

  private

  def document_params
    { upload:, template_options: }
  end

  def template_options
    options = {
      template_type: kind,
      tenant_count: tenant_count,
      vehicle_count: vehicle_count
    }

    case scope
    when Property
      options[:property_id] = scope.id
    when Portfolio
      options[:portfolio_id] = scope.id
    else
      options[:customer_id] = Customer.current.id
    end

    options.compact_blank
  end

  def validate_docx_mime_type
    return if upload.blank?

    expected_mime_type = Mime::Type.lookup_by_extension(:docx)
    detected_mime_type = Marcel::MimeType.for(upload)

    return if detected_mime_type == expected_mime_type

    errors.add(:upload, "must be a valid .docx file (detected #{detected_mime_type})")
  end
end
