class ChartsOfAccountsQuery
  def initialize(relation = ChartOfAccounts.all.order(name: :asc))
    @relation = relation.extending(Scopes)
  end

  def search
    @relation
  end

  module Scopes
    def by_owner(owner)
      by_companies(owner.companies)
    end

    def by_companies(companies)
      fail 'Pass an ActiveRecord::Relation' unless companies.is_a?(ActiveRecord::Relation)

      configured_charts = ChartOfAccounts.where(
        id: companies.joins(
          portfolio: [configuration: :chart_of_accounts]
        ).merge(ChartOfAccounts.select(:id))
      )

      direct_charts = ChartOfAccounts.where(
        id: companies.select(:chart_of_accounts_id)
      )

      where(id: configured_charts.or(direct_charts).select(:id))
    end
  end
end
