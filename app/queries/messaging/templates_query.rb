class Messaging::Temp<PERSON><PERSON><PERSON><PERSON>
  def initialize(kind, relation = Form::Template.all)
    @relation = relation
                .where(kind:)
                .extending(Scopes)
  end

  def search
    @relation
  end

  module Scopes
    include Queries::MaxPagination

    def filter_with(filters)
      return self if filters.blank?

      filter_text(filters[:search])
    end

    def filter_text(text)
      return self if text.blank?

      where('templates.name ILIKE ?', "%#{text}%")
    end

    def order_with(sort)
      return self if sort.blank?

      direction = sort[:direction] == 'descending' ? 'DESC' : 'ASC'

      case sort[:column]
      when 'name', 'updated_at'
        order(sort[:column] => direction)
      else
        self
      end
    end
  end
end
