class Accounting::CashBasisMigration::RecentlyChangedPaymentsQuery
  def initialize(start_date: 2.weeks.ago)
    @start_date = start_date
  end

  def search
    queries = [
      payments_from_updated_payments,
      payments_from_updated_invoices,
      payments_from_added_invoice_payments,
      payments_from_removed_invoice_payments
    ]

    queries.map { |query| Payment.where(id: query) }.reduce(&:or)
  end

  private

  def payments_from_updated_payments
    Payment.where(updated_at: date_range)
  end

  def payments_from_updated_invoices
    invoices = Invoice.where(updated_at: date_range)

    invoice_payments = InvoicePayment.where(invoice: invoices)

    Payment.where(id: invoice_payments.select(:payment_id))
  end

  def payments_from_removed_invoice_payments
    audits = Audited::CustomAudit.where(
      created_at: date_range,
      auditable_type: 'InvoicePayment',
      action: 'destroy'
    )

    ids = audits.pluck(:audited_changes).pluck('payment_id')

    Payment.where(id: ids)
  end

  def payments_from_added_invoice_payments
    invoice_payments = InvoicePayment.where(created_at: date_range)

    Payment.where(id: invoice_payments.select(:payment_id))
  end

  def date_range
    @start_date..
  end
end
