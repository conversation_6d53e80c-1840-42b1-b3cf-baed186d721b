class Lending::<PERSON><PERSON><PERSON><PERSON><PERSON>
  def initialize(relation = Lending::Loan.all)
    @relation = relation.extending(Scopes)
  end

  def search
    @relation
  end

  module Scopes
    include AgreementStatusFiltering
    include Queries::MaxPagination
    include TagFiltering

    def by_user(user)
      return self if user.top_level?

      where(property: user.properties)
    end

    def filter_with(filters)
      return self if filters.blank?

      filter_search(filters[:search])
        .filter_property_id(filters[:property_id])
        .filter_status(filters[:status])
        .filter_tag(filters[:tag])
    end

    def filter_search(search)
      where('loan_number ILIKE ?', "%#{search}%")
    end

    def filter_property_id(property_id)
      return self if property_id.blank?

      where(property_id: property_id)
    end

    def filter_status(status)
      case status
      when 'current' then unarchived.current
      when 'previous' then unarchived.previous
      when 'upcoming' then unarchived.upcoming
      when 'unarchived' then unarchived
      when 'archived' then archived
      else
        self
      end
    end

    def order_with(sort)
      return self if sort.blank?

      direction = sort[:direction] == 'descending' ? 'DESC' : 'ASC'
      column = sort[:column]

      case column
      when 'loan_number',
           'interest_rate', 'principal_cents',
           'maturity_date', 'first_payment_date'
        reorder(column => direction)
      when 'borrower'
        reorder('companies.name' => direction)
      when 'property'
        reorder('properties.name' => direction)
      else
        self
      end
    end
  end
end
