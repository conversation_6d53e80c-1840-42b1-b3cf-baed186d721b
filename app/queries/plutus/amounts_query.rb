class Plutus::Amou<PERSON><PERSON><PERSON><PERSON>
  def initialize(relation = Plutus.amount_class.all)
    unless relation.table.name == Plutus.amount_table
      fail 'Improper Relation Basis'
    end

    @relation = relation.extending(Scopes)
  end

  def search
    @relation
  end

  module Scopes
    include Queries::MaxPagination

    def debits
      where(type: 'Plutus::DebitAmount')
    end

    def credits
      where(type: 'Plutus::CreditAmount')
    end

    def filter_with(filters)
      return self if filters.blank?

      filter_search(filters[:search])
        .filter_date_range(filters[:start_date], filters[:end_date])
        .filter_subdivision(filters[:subdivision])
    end

    def filter_search(search)
      return self if search.blank?

      term = "%#{search}%"

      where("#{Plutus.entry_table}.description ILIKE ?", term).or(
        where("#{Plutus.entry_table}.reference_number ILIKE ?", term)
      )
    end

    def filter_date_range(start_date, end_date)
      return self if start_date.blank? && end_date.blank?

      entries = Plutus.entry_class

      entries = entries.where(date: start_date..) if start_date.present?

      entries = entries.where(date: ..end_date) if end_date.present?

      merge(entries)
    end

    def filter_subdivision(subdivision)
      return self if subdivision.blank?

      properties = if subdivision == 'no_subdivision'
                     Property.where(subdivision: nil).or(
                       Property.where(subdivision: '')
                     )
                   else
                     Property.where(subdivision: subdivision)
                   end

      merge(Plutus.entry_class.where(property: properties))
    end

    def order_with(sort)
      return self if sort.blank?

      direction = sort[:direction] == 'descending' ? 'DESC' : 'ASC'

      case sort[:column]
      when 'date', 'description', 'contact_name', 'reference_number'
        merge(Plutus.entry_class.reorder(sort[:column] => direction))
      when 'journal'
        merge(Company.reorder(name: direction))
      when 'debits', 'credits'
        if sort[:column] == 'credits'
          direction = direction == 'DESC' ? 'ASC' : 'DESC'
        end

        reorder(
          Arel.sql(
            %{
              (
                CASE WHEN #{Plutus.amount_table}.type = 'Plutus::DebitAmount'
                THEN #{Plutus.amount_table}.amount
                ELSE #{Plutus.amount_table}.amount * -1
                END
              ) #{direction},
              #{Plutus.amount_table}.id #{direction}
            }
          )
        )
      else
        self
      end
    end

    def deposit_batch_filter(search)
      return self if search.blank?

      number = search.gsub(/[^\d]/, '').presence

      where('plutus_entries.description ILIKE ?', "%#{search}%").or(
        where('plutus_entries.contact_name ILIKE ?', "%#{search}%")
      ).or(
        where('plutus_entries.reference_number ILIKE ?', "%#{search}%")
      ).or(
        where("to_char(plutus_entries.date, 'MM/DD/YY') ILIKE ?", "%#{search}%")
      ).or(
        where('properties.name ILIKE ?', "%#{search}%")
      ).or(
        where('plutus_amounts.amount::text ILIKE ?', "%#{number || search}%")
      )
    end

    def deposit_batch_sort(column_value, direction_value)
      if column_value.in?(%w[debit_amount credit_amount])
        dir = if (column_value == 'debit_amount' && direction_value == 'asc') || (column_value == 'credit_amount' && direction_value == 'desc')
                'ASC'
              else
                'DESC'
              end

        return reorder(
          Arel.sql(
            %{
              (
                CASE WHEN plutus_amounts.type = 'Plutus::DebitAmount'
                THEN plutus_amounts.amount
                ELSE plutus_amounts.amount * -1
                END
              ) #{dir}, plutus_amounts.id ASC
            }
          )
        )
      end

      sort = {}
      if (column = sort_column(column_value))
        sort[column] = sort_direction(direction_value)
      end

      if sort.present?
        reorder(sort, 'plutus_amounts.id ASC')
      else
        self
      end
    end

    private

    def sort_column(value)
      case value
      when 'description' then 'plutus_entries.description'
      when 'date' then 'plutus_entries.date'
      when 'reference_number' then 'plutus_entries.reference_number'
      when 'deposited' then 'deposit_batch_id'
      when 'reconciled' then 'reconciliation_id'
      when 'property' then 'properties.name'
      when 'contact_name' then 'plutus_entries.contact_name'
      when 'kind' then 'payments.kind'
      when 'amount' then 'plutus_amounts.amount'
      end
    end

    def sort_direction(value)
      if value.in?(%w[asc desc])
        value
      else
        'asc'
      end
    end
  end
end
