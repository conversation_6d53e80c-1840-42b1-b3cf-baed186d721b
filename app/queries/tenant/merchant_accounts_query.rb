# To unify tenant portal and management tenant profile behavior
class Tenant::MerchantAccountsQuery
  class << self
    def available_merchant_accounts(tenant:, property_id: nil)
      accounts = ::MerchantAccountsQuery.new.search.supports_tenant_accounts

      lease_membership = current_lease_membership(
        tenant: tenant,
        property_id: property_id
      )

      simple_agreement = current_simple_agreement(tenant: tenant)

      accounts.by_lease_membership(lease_membership).or(
        accounts.by_simple_agreement(simple_agreement)
      )
    end

    private

    def current_lease_membership(tenant:, property_id: nil)
      Tenant::LeaseMembershipsQuery.current_lease_membership(
        tenant: tenant,
        property_id: property_id
      )
    end

    def current_simple_agreement(tenant:)
      Tenant::SimpleAgreementsQuery.current_simple_agreement(tenant: tenant)
    end
  end
end
