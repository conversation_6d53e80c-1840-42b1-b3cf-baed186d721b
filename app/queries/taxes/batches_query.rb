class Taxes::<PERSON><PERSON><PERSON><PERSON><PERSON>
  def initialize(relation = Taxes::Nelco::Batch.all)
    @relation = relation.extending(Scopes)
  end

  def search
    @relation
  end

  module Scopes
    def includes(*args)
      super(*args)
    end

    def where(*args)
      super(*args)
    end

    def eligible_for_status_update
      submitted.where.not(nelco_batch_oid: nil)
    end

    def transmittable
      where(status: [:unsubmitted, :error])
    end
  end
end
