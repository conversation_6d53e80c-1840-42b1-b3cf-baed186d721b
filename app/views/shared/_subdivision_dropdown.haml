- if Feature.enabled?(:property_subdivision, Customer.current)
  :ruby
    subdivision_options = \
      Property
      .where.not(subdivision: nil)
      .reorder(subdivision: :asc)
      .distinct(:subdivision)
      .pluck(:subdivision)

  .field
    = f.label :subdivision, 'Subdivision / Neighborhood'
    = f.semantic_dropdown :subdivision,
      [['None', nil]] + subdivision_options,
      {},
      class: 'ui search selection dropdown',
      data: { options: { allowAdditions: true, placeholder: 'None' } }
