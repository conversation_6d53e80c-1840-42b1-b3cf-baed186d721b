.ui.buttons
  %button.ui.button{
    class: class_names(active: transaction_context == 'create'),
    data: {
      tunisia__bank_accounts__card_activities__transaction_target: 'createReceipt',
      action: 'tunisia--bank-accounts--card-activities--transaction#toggleCreateReceipt'
    }
  }
    Create Receipt

  .or
  
  %button.ui.button{
    class: class_names(active: transaction_context == 'forward'),
    data: {
      tunisia__bank_accounts__card_activities__transaction_target: 'forwardReceipt',
      action: 'tunisia--bank-accounts--card-activities--transaction#toggleForwardReceipt'
    }
  }
    Create & Forward
