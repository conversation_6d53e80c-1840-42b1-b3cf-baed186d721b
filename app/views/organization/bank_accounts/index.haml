:ruby
  banking_feature_enabled = Feature.enabled?(:unit_bank_accounts, Customer.current)
  role_permits_application = rbac_enabled_or_if(current_property_manager.role.administrator?)
  deposit_product_configured = Customer.current.tunisia_deposit_product.present?
.action-bar
  .ui.container
    %h1.ui.header
      Bank Accounts
    .action.buttons
      - require_permission :apply_for_organization_bank_accounts? do
        - if banking_feature_enabled && role_permits_application
          - if deposit_product_configured
            = link_to new_organization_tunisia_application_form_path,
              class: 'ui button',
              data: { modal: :open_bank_account_modal } do
              %i.plus.icon
              Open Bank Account
      - require_permission :create_organization_bank_accounts? do
        = link_to new_organization_bank_account_path, class: 'ui button' do
          %i.plus.icon
          Add Bank Account

.action-container{ style: 'padding-top: 1em; overflow-y: scroll;' }
  .ui.container
    = render 'flash_notices'
    = render Tunisia::BankAccounts::RecentComponent.new(user: current_property_manager)
    = ActionIndex::BankAccounts.(self,
      collection: @bank_accounts,
      user: current_property_manager,
      partial: { path: 'organization/bank_accounts/table',
      locals: { bank_accounts: @bank_accounts } })

= render Tunisia::ApplicationForms::SelectOwnerModalComponent.new
