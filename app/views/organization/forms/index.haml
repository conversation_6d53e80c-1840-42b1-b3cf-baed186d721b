.action-bar
  .ui.container
    %h1.ui.header
      Forms

    .action-buttons
      - require_permission :create_organization_forms? do
        = link_to '#', data: { modal: :create_custom_form_modal }, class: 'ui button' do
          %i.plus.icon
          New Form


.action-container
  .ui.container{ style: 'padding-top: 1em' }
    = render 'flash_notices'

    - if @all_forms.none?
      = render EmptyStateComponent.new(icon: 'file alternate outline') do |component|
        - component.with_header do
          Seamlessly manage event registration with our custom forms

        - component.with_paragraph do
          Utilize our event template to create a registration form with optional integrated payment submission. Effortlessly share the form with attendees and quickly export registration data.

        - require_permission :create_organization_forms? do
          = link_to 'Create Form', '#', class: 'ui primary button', data: { modal: :create_custom_form_modal }
    - else
      = ActionIndex::CustomForms.(self,
        collection: @filtered_forms,
        user: current_property_manager,
        partial: { path: 'organization/forms/table',
        locals: { forms: @filtered_forms } })

      = paginate @filtered_forms

    = render CustomForms::CreateCustomFormModalComponent.new

    = render partial: 'shared/confirm_bulk_archiving_modal'
