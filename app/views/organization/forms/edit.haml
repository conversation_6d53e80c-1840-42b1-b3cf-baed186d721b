.action-bar
  .ui.container{ style: 'margin-left: 1rem; margin-right: 1rem; width: calc(100% - 2rem);' }
    .ui.breadcrumb{ style: 'margin: 0.75rem 0;' }
      = link_to 'Forms', organization_forms_path, class: 'ui section'
      %i.right.angle.icon.divider

      = link_to @custom_form.name, organization_form_path(@custom_form), class: 'ui section'
      %i.right.angle.icon.divider

      %h2.ui.header{ style: 'margin: 0;' }
        = @custom_form.name

    .action.buttons
      - unless @custom_form.published?
        = link_to publish_organization_form_path(@custom_form),
          data: { method: :post },   class: 'ui right floated button primary' do
          Publish

      = link_to new_custom_forms_submission_url(token: @custom_form.token, preview: true), target: :_blank, class: 'ui right floated button basic', style: 'margin-left: .5em;' do
        %i.icon.eye.outline
        Preview

.action-container
  #cf-editor.ui.padded.grid.divided.full.height.stackable{ data: { controller: 'custom-form-editor--details custom-form-editor--dnd custom-form-editor--settings custom-form-editor--dimmer',
      'action' => 'refresh->custom-form-editor--details#refresh dim->custom-form-editor--dimmer#dim',
      'custom-form-editor--dnd-form-id-value' => @custom_form.id,
      'custom-form-editor--dimmer-form-id-value' => @custom_form.id } }
    .four.wide.column{style: 'padding: 0;'}
      .ui.grid.padded{style: 'position:sticky; top: 0; padding-top: 2rem;'}
        .row
          #configure-mode.ui.two.item.secondary.menu{style: 'justify-content: center;'}
            = link_to 'Builder', '#', class: 'active item', data: { tab: 'toolkit'}
            = link_to 'Settings', '#', class: 'item', data: { tab: 'settings'}
        .row{style: 'border-top: 1px solid lightgray;'}
          .ui.tab.toolkit-tab{ data: {tab: 'toolkit'} }
            = render partial: 'organization/forms/edit/toolkit', locals: { custom_form: @custom_form }
          .ui.tab.settings-tab{ data: {tab: 'settings', 'custom-form-editor--settings-target' => 'tab' } }
            = render partial: 'organization/forms/edit/settings', locals: { custom_form: @custom_form }

    .twelve.wide.column#canvas-column{ data: { 'custom-form-editor--details-target' => 'canvasPanel' } }
      = render 'flash_notices'
      .ui.segment.placeholder.full.height{style: 'justify-content: start;'}
        #cf-canvas
          = render CustomForms::CanvasContentsComponent.new(form: @custom_form)
    #details-column.four.wide.column{ data: { 'custom-form-editor--details-target' => 'detailsColumn' }}
      .details-panel{ data: { 'custom-form-editor--details-target' => 'detailsPanel'}, style: 'position:sticky; top: 0px' }

    .ui.inverted.dimmer{data: { 'custom-form-editor--dimmer-target' => 'dimmer' } }
      .ui.loader
:javascript
  $('#configure-mode .item').tab();
