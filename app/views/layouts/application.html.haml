!!! 5
%html
  %head
    %meta{ content: 'text/html; charset=UTF-8', 'http-equiv' => 'Content-Type' }
    %title= raw current_page_title
    = favicon_link_tag 'favicon.ico'
    = stylesheet_link_tag 'management',
      media: 'all', 'data-turbolinks-track' => 'reload'
    = javascript_include_tag 'management', 'data-turbolinks-track' => 'reload'
    = tunisia_web_sdk
    %meta{ content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0', name: 'viewport' }
    %meta{ name: 'version', content: REVELA_VERSION }
    %meta{ name: 'direct-upload-url', content: rails_direct_uploads_path }
    = csrf_meta_tags
  %body
    = render 'hot_reload'
    .full.height.pusher
      = render partial: 'application/navigation/sidebar'
      = render partial: 'application/navigation/mobile_menu'
      .main
        = render partial: 'application/navigation/mobile_nav'
        .ui.container
          .ui.two.column.stackable.grid
            .nine.wide.column
              = render partial: 'search/global_search'
            .seven.wide.right.aligned.column.top-content
              %span#action-icons
                %a{ href: '#', onclick: 'return false;' }
                  %i.ui.circular.inverted.black.calendar.alternate.icon#calendar-button
                = link_to notifications_path do
                  %i.ui.circular.inverted.alarm.outline.notification.icon{ class: unread_notifications? ? 'active' : nil }
                = link_to whats_new_path do
                  %i.ui.circular.inverted.gift.notification.icon{ class: new_whats_new_entries? ? 'active' : nil }
                = link_to 'http://kb.revela.co/knowledge-base', target: '_blank', rel: 'noopener' do
                  %i.ui.circular.inverted.black.book.icon
                %a#help-button{ href: '#' }
                  %i.ui.circular.inverted.black.help.icon
              = manager_dropdown
        - unless %w(dashboard notifications whats_new).include? controller_name
          = render partial: 'application/navigation/top_tabs'
        - if @noport
          .ui.container
            = render 'flash_notices' unless @defer_flash_notices
          = content_for?(:content) ? yield(:content) : yield
        - elsif @commentable
          .commentable-port
            .commentable-resource
              .wrapper
                = render 'flash_notices'
                = yield
            = react_component 'CommentsSidebar', { type: @commentable.class.to_s.underscore, id: @commentable.id, messages: serialize(@commentable.messages) }, style: 'display: flex; flex-direction: column;', class: 'comments-sidebar'
        - else
          .port{ data: { turbolinks_preserve_scroll: @port_preserve_scroll } }
            .ui.container
              = render 'flash_notices'
              = content_for?(:content) ? yield(:content) : yield

      = react_component 'CalendarPopup', {},
        id: 'calendar-component',
        'data-turbolinks-permanent' => true
    = render 'help_form'

    .bottom.right.ui.toast-container{ id: 'bottom-right-toasts',
      data: { turbolinks_permanent: true } }
    = render partial: 'shared/mouseflow'
