- content_for(:content) do
  - if @configuration.persisted?
    - require_permission :destroy_organization_configurations? do
      = link_to organization_configuration_path(@configuration),
        method: :delete, data: { confirm: 'Are you sure?' },
        class: 'right floated ui button' do
        %i.trash.icon
        Remove

  .ui.breadcrumb
    = link_to 'Configurations',
      organization_configurations_path,
      class: 'section'
    %i.right.angle.icon.divider
    %h1.ui.header
      = @configuration.name || 'New Configuration'

  .ui.stackable.padded.grid{ style: 'padding-top: 1em;' }
    .four.wide.column
      .ui.vertical.orange.secondary.pointing.menu{ style: 'width: 13em;' }
        :ruby
          sections = [
            { name: 'Overview', path: 'overview', icon: :world },
            { name: 'Late Fe<PERSON>', path: 'late_fees', icon: :calendar },
            { name: 'Charges', path: 'charge_presets', icon: :dollar },
            { name: 'Credits', path: 'credit_presets', icon: :gift },
            (
              if Feature.enabled?(:payment_plans, Customer.current)
                { name: 'Payment Plans', path: 'payment_plan_presets', icon: 'piggy bank' }
              end
            ),
            { name: 'Applications', path: 'applications', icon: 'address card outline' },
            { name: 'Screening', path: 'screening', icon: :legal },
            { name: 'Leases', path: 'leases', icon: :file },
            { name: 'Collections', path: 'collections', icon: 'clipboard check' },
            { name: t('activerecord.models.inspection/report').pluralize, path: 'inspections', icon: :search },
            { name: 'Maintenance', path: 'maintenance', icon: :wrench }
          ].compact

        - sections.each do |section|
          - active_class = request.path.include?(section[:path]) ? 'active orange item' : 'item'
          - if @configuration.new_record?
            .disabled{ class: active_class }
              %i.icon{ class: section[:icon] }
              = section[:name]
          - else
            - path = send("organization_configuration_#{section[:path]}_path", @configuration)
            = link_to path, class: active_class do
              %i.icon{ class: section[:icon] }
              = section[:name]

    .twelve.wide.column
      = yield

= render template: 'layouts/application'
