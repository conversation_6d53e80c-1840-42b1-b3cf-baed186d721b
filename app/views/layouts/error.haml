!!!5
%html{ lang: :en }
  %head{ href: '/' }
    %title
      Revela
    = favicon_link_tag 'favicon.ico'
    = stylesheet_link_tag 'error', media: 'all', 'data-turbolinks-track' => 'reload'
    = javascript_include_tag 'https://cdnjs.cloudflare.com/ajax/libs/particles.js/2.0.0/particles.min.js'
    %meta{ content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0', name: 'viewport' }
    %meta{ content: 'text/html; charset=UTF-8', 'http-equiv' => 'Content-Type' }
  %body{ href: '/', style: 'padding: 0;' }
    #particles-js{ style: 'height: 100vh; width: 100vw' }
      %div{ style: 'display: flex; justify-content: center; align-items: center; height: 100vh; width: 100vw; position: absolute;' }
        .ui.text.container
          %h1{ style: 'font-size: 10em; font-weight: bold' }
            = response.status
          %h2
            #{yield.chomp}.
          %h3
            We have been notified of this problem.
            Please click
            = link_to 'here', 'javascript:history.back()'
            to go back, or contact
            = mail_to '<EMAIL>', '<EMAIL>'
            if the problem persists.

          .ui.form
            / HONEYBADGER FEEDBACK
      = render partial: 'shared/mouseflow'

    :javascript
      particlesJS.load('particles-js', '/assets/particles.json');

      // Customize Honeybader Feedback Form
      var button = document.getElementById('honeybadger_feedback_submit');
      button.className = 'ui right floated submit button';
      button.style.height = '32px';

      var textbox = document.getElementById('honeybadger_feedback_comment');
      textbox.style.height = '80px';

      var link = document.getElementById('honeybadger_feedback_link')
      link.outerHTML = '';
