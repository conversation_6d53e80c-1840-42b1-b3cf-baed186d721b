%h2.header
  Onboarding Complete
  - follow_up_items = onboarding_wizard.steps['completion_summary'].follow_up_items
.ui.info.message
  - if follow_up_items.empty?
    %strong
      Congratulations, you've completed your Onboarding.
  - else
    %strong
      You've completed your Onboarding but may still need
      to take care of some steps:
    %ul.ui.list
      - follow_up_items.each do |item|
        - if item == :ensure_guarantor_account
          %li.item
            Ensure that your guarantor/guardian confirms
            their account here. They should receive an
            invitation email with a link.
        - if item == :ensure_guarantor_signatures
          %li.item
            Your guarantor will need to sign the lease or agreement
        - if item == :check_charges
          %li.item
            Check the portal for charges resulting
            from your membership or lease.
