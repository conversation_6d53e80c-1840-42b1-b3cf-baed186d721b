.ui.container
  = link_to 'Delete',
    [:tenants, :account, @scheduled_payment],
    method: :delete,
    data: { confirm: 'Are you sure?' },
    class: 'right floated ui button'

  = link_to 'Edit',
    edit_tenants_account_scheduled_payment_path(@scheduled_payment),
    class: 'right floated ui button'

  .ui.breadcrumb
    = link_to 'Account',
      %i[tenants account],
      class: 'section'
    %i.right.angle.icon.divider

    %h1.ui.header
      = @scheduled_payment.description
      .sub.header
        Scheduled Payment

  - if @scheduled_payment.failed?
    .ui.error.message
      .header Scheduled Payment Failed
      %p
        This scheduled payment was unable to be processed for the following
        reason:

      %p
        %i= @scheduled_payment.failure_reason

      - if @scheduled_payment.recurring?
        %p
          Your scheduled payment will not be retried automatically, and will
          not run again next month unless it is retried successfully first.
      - else
        %p
          Your scheduled payment will not be retried automatically.

      = link_to 'Retry Payment Now',
       retry_tenants_account_scheduled_payment_path(@scheduled_payment),
        class: 'ui primary button',
        data: { confirm: 'Retry Payment?' }

  .ui.equal.width.divided.stackable.tiny.statistics.grid
    .ui.statistic.column
      .label Amount
      .value{ style: 'text-transform: none;' }
        = @scheduled_payment.balance
    .ui.statistic.column
      .label
        - if @scheduled_payment.recurring?
          Next Payment Date
        - else
          Payment Date
      .value{ style: 'text-transform: none;' }
        - if @scheduled_payment.failed?
          Needs Retry
        - else
          = @scheduled_payment.formatted_next_date
    .ui.statistic.column
      - if @scheduled_payment.payment_plan.present?
        .label Payment Plan
        .value
          = link_to 'View',
            tenants_payment_plan_path(@scheduled_payment.payment_plan)
      - else
        .label Schedule
        .value{ style: 'text-transform: none;' }
          = @scheduled_payment.type
