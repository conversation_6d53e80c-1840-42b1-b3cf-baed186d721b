- page_title @maintenance_ticket.subject

.ui.breadcrumb
  = link_to 'Maintenance', maintenance_tickets_path, class: 'section'
  %i.right.angle.icon.divider
  = link_to "Ticket ##{@maintenance_ticket.number}",
    maintenance_ticket_path(@maintenance_ticket),
    class: 'section'
  %i.right.angle.icon.divider
  .active.section Review and Close

%h2.ui.header Review "#{@maintenance_ticket.subject}" Billing

= form_with url: maintenance_ticket_billings_path(@maintenance_ticket),
  skip_enforcing_utf8: true,
  local: true,
  class: 'ui form' do |f|

  = f.hidden_field :close, value: '1'

  - if @maintenance_ticket.needs_billing?
    #billing-panel-fields{ data: { controller: 'maintenance-ticket--billing-panel' } }
      = render partial: 'billing_panel', locals: { billing_panel_form: f }

  = f.submit 'Close and Bill Ticket', name: nil, class: 'ui primary button'

.clearfix
