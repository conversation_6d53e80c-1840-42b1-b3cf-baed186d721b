.ui.basic.modal#attach-invoice
  :ruby
    invoice = Invoice.new
    invoice.buyer = @maintenance_ticket.accounting_context.entity
    invoice.post_date = Time.zone.today
    invoice.physical_date = Time.zone.today
    net_days = CustomerSpecific::Behavior.payable_invoice_default_net_days || 30
    invoice.due_date = net_days.days.from_now.to_date
    invoice.invoice_number = ''
    invoice.line_items.build(quantity: nil)

  %div
    .ui.paper
      = render partial: 'accounting/shared/invoices/form/updated',
        locals: { invoice: invoice, accounting_side: :payables }
