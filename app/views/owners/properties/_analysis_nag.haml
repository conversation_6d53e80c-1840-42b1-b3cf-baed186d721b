- if analysis.nil?
  .ui.segment{ style: 'display: flex; align-items: center; justify-content: space-between; flex-wrap: wrap; border-top-width: 2px; border-top-style: solid; border-top-color: #20c0da' }
    %p
      %i.home.icon{ style: 'color: #20c0da' }
      %b How is your property performing?
      %br Complete a brief analysis to view your property’s cash-on-cash return.
      You may be eligible for financing to save money on your payments or to invest and grow your portfolio.

    = link_to new_owners_property_analysis_path(property),
      class: 'right floated ui button',
      id: 'analyze-button' do
      %i.magic.icon
      Analyze Property

- else
  - eligible = analysis.mino_eligible?

  .ui.segments
    .ui.clearing.segment{ class: class_names('top attached' => eligible), style: 'border-top-width: 2px; border-top-style: solid; border-top-color: #20c0da;'}
      .ui.three.column.divided.tiny.stackable.statistics.grid
        .statistic.column
          .value= number_to_percentage(analysis.cash_on_cash_return * 100, precision: 1)
          .label Cash-on-Cash Return
        .statistic.column
          .value= analysis.estimated_value.format
          .label Estimated Value
        .statistic.column
          .value= number_to_percentage(analysis.equity * 100, precision: 1)
          .label Equity

      = link_to new_owners_property_analysis_path(property),
        class: 'basic ui icon button',
        title: 'Update',
        style: 'position: absolute; top: 8px; right: 4px; color: #20c0da;' do
        %i.pencil.icon

      %span{ style: 'font-size: .5em; color: gray; position: absolute; right: 8px; bottom: 0;' }
        *These values are estimates based on user provided values and Revela data.

    - if eligible
      .ui.bottom.attached.segment
        %i.olive.exclamation.circle.icon
        This property is eligible for financing!
        = link_to 'Click here',
          'https://www.minolending.com/revela-owner-property-analysis',
          target: '_blank'
        to learn more about financing options.

        .ui.accordion#show-more-accordion
          .title
            %i.dropdown.icon
            Show Details
          .content
            %p.transition.hidden
              %ul
                %li Save money on monthly payments by refinancing.
                %li Grow your portfolio with a cash-out refi to invest in new properties.
                %li Upgrade your properties with access to cash for rehab projects.

:javascript
  $('#show-more-accordion').accordion();

:css
  @media only screen and (min-width: 1200px) {
    #show-more-accordion .title {
      position: absolute; right: 8px; top: 6px;
    }
  }
