.ui.container
  .ui.breadcrumb
    = link_to 'Expenses', owners_invoices_path, class: 'section'
    %i.right.angle.icon.divider

    %h1.ui.header
      Make Payment

  - if @payment_method
    = render partial: 'shared/payment_processing/checkout_page',
      locals: { payment_methods: @company.payment_methods,
      payment_method: @payment_method,
      path: owners_payments_path(company_id: @company.id,
      invoice_id: params[:invoice_id]),
      email: current_owner.email,
      cart: @cart }
  - else
    = render Owners::ElectronicPayments::NoPaymentMethodComponent.new(entity: @company)
