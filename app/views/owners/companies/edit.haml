.ui.container
  .ui.breadcrumb
    = link_to 'Entities', owners_companies_path, class: 'section'
    %i.right.angle.icon.divider

    = link_to @company.name, owners_company_path(@company), class: 'section'
    %i.right.angle.icon.divider

    %h1.ui.header
      Edit Information

  .ui.text.container
    = form_for [:owners, @company], html: { class: 'ui form' } do |f|
      = render partial: 'shared/company/information_fields',
        locals: { f: f, owner_view: true }

      .clearfix
        = f.submit 'Update',
          class: 'ui primary submit button'
