.action-container
  .ui.container{ style: 'margin: 2em;' }
    = render EmptyStateComponent.new(icon: 'phone') do |component|
      - component.with_header do
        Make and Receive Calls and SMS Messages Directly From Revela

      - component.with_paragraph do
        By enabling phone services in Revela, you will be able to provision
        phone numbers that you can use to make and receive calls or text
        messages with your prospects and #{I18n.t('tenant_term').downcase.pluralize}.

      - component.with_paragraph do
        You may incur additional charges based on your usage patterns. Please
        contact support for questions about billing.

      - component.with_paragraph do
        By enabling phone services, you agree to our
        = link_to 'terms and conditions',
            terms_path,
            target: '_blank'
        regarding VoIP and text messaging.

      = link_to 'Add a Phone Number',
        new_telephony_phone_number_path,
        class: 'ui primary button'
