Hello #{@contact.first_name},

%p
  This email is to notify you that the following
  - if @invoices.one?
    charge was
  - else
    charges were
  posted to your account:

%table
  - @invoices.each do |invoice|
    %tr
      %td
        = link_to_if invoice_url(invoice),
          invoice.description,
          invoice_url(invoice)
      %td
        = invoice.amount.format
      %td
        Due
        = invoice.due_date.to_datetime.to_fs(:short_date)

- if invoices_url
  %p
    To view or pay the #{'charge'.pluralize(@invoices.count)},
    you can click the #{'link'.pluralize(@invoices.count)}
    above or view all charges on your account's
    = succeed '.' do
      = link_to 'charges page', invoices_url

- if Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox])
  = render partial: 'sae_mailers/help'
- if Customer.current_subdomain.start_with?('mph-sandbox', 'marketplacehomes')
  %p
    If you have a scheduled payment, no further action is required. If you do
    not have a scheduled payment, please login and make a payment prior to the
    due date.

  Have a nice day!
- elsif !Customer.current_subdomain.start_with?('chio')
  %p
    If you have already paid
    - if @invoices.one?
      this charge
    - else
      these charges
    or wish to pay offline, you can ignore this email and your payment will be
    recorded when it is processed by #{Customer.current.name}.

  Have a nice day!
