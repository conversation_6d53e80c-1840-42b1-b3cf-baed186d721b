.ui.vertical.segment.padded.basic
  %h2.ui.header Payment

.ui.vertical.segment.basic
  %h4.ui.header= "Event Cost: #{payment_settings.charge_amount.format}"
  %p{style:'font-weight:200'}
    -if payment_settings.cc_enabled?
      %span{ id: 'cc-processing-message', style: ('display: none;' if submission.use_ach?) }
        = "* #{payment_settings.processing_fee_message('cc')}"
    -if payment_settings.ach_debit_enabled?
      %span{ id: 'ach-processing-message', style: ('display: none;' if submission.use_credit_card?) }
        = "* #{payment_settings.processing_fee_message('ach')}"

.ui.buttons{ style: ('display: none;' unless (payment_settings.supports_cc_and_ach?)) }
  %button.ui.button#cc-button{ type: :button, class: ('green' if submission.use_credit_card?) }
    Credit / Debit Card
  .or{ style: 'width: 0;' }
  %button.ui.button#ach-button{ type: :button, class: ('green' if submission.use_ach?) }
    E-Check / ACH

.ui.vertical.segment.basic
  = submission_form_builder.hidden_field :payment_method

.equal.width.fields
  %div{class: ['field required', ('error' if submission.errors[:account_holder_name].any?)] }
    = submission_form_builder.label :account_holder_name, (submission.use_credit_card? ? 'Card Holder Name' : 'Account Holder Name')
    = submission_form_builder.text_field :account_holder_name, placeholder: 'Name', required: true

#credit-card-fields{ style: ('display: none;' if submission.use_ach?) }
  .equal.width.fields
    %div{class: ['field required', ('error' if submission.errors[:credit_card_number].any?)] }
      = submission_form_builder.label :credit_card_number, 'Card Number'

      .ui.left.icon.input
        %i.icon.credit.card#card-icon
        = submission_form_builder.text_field :credit_card_number, placeholder: 'Card Number', autocomplete: :off, required: true

  .equal.width.fields
    %div{class: ['field required', ('error' if submission.errors[:credit_card_expiration_date].any?)] }
      = submission_form_builder.label :credit_card_expiration_date, 'Expiry date'
      = submission_form_builder.text_field :credit_card_expiration_date, placeholder: 'MM / YY', required: true

    %div{class: ['field required', ('error' if submission.errors[:credit_card_security_code].any?)] }
      = submission_form_builder.label :credit_card_security_code, 'Security Code'
      = submission_form_builder.text_field :credit_card_security_code, placeholder: 'CVC', required: true, inputmode: 'numeric', maxlength: 4

#ach-fields{ style: ('display: none;' if submission.use_credit_card?) }
  .equal.width.fields
    %div{class: ['field required', ('error' if submission.errors[:ach_account_number].any?)] }
      = submission_form_builder.label :ach_account_number, 'Account Number'
      = submission_form_builder.text_field :ach_account_number, autocomplete: :off

    %div{class: ['field required', ('error' if submission.errors[:ach_routing_number].any?)] }
      = submission_form_builder.label :ach_routing_number, 'Routing Number'
      = submission_form_builder.text_field :ach_routing_number, autocomplete: :off

  .inline.fields{class: ('error' if submission.errors[:ach_account_type].any?) }
    .field.required
      = submission_form_builder.label :ach_account_type, 'Account Type'

    .field
      .ui.radio.checkbox
        = submission_form_builder.radio_button :ach_account_type, :checking
        = submission_form_builder.label :ach_account_type, 'Checking'
    .field
      .ui.radio.checkbox
        = submission_form_builder.radio_button :ach_account_type, :savings
        = submission_form_builder.label :ach_account_type, 'Savings'

#billing-address-fields
  .ui.vertical.segment.basic
    %h4.ui.header Billing Address

  .equal.width.fields
    %div{class: ['field required', ('error' if submission.errors[:billing_address_one].any?)] }
      = submission_form_builder.label :billing_address_one, 'Street Address Line One'
      = submission_form_builder.text_field :billing_address_one, placeholder: 'Address Line One', required: true

    %div{class: ['field', ('error' if submission.errors[:billing_address_two].any?)] }
      = submission_form_builder.label :billing_address_two, 'Street Address Line Two'
      = submission_form_builder.text_field :billing_address_two, placeholder: 'Address Line Two', required: false

  .equal.width.fields
    %div{class: ['field required', ('error' if submission.errors[:billing_city].any?)] }
      = submission_form_builder.label :billing_city, 'City'
      = submission_form_builder.text_field :billing_city, placeholder: 'City', required: true

    %div{class: ['field required', ('error' if submission.errors[:billing_state].any?)] }
      = submission_form_builder.label :billing_state, 'State'
      = submission_form_builder.semantic_dropdown :billing_state, [['State', nil], *Address::US_STATES.to_a], {}, { class: 'ui search clearable selection dropdown', data: { options: { placeholder: 'State' } } }

    %div{class: ['field required', ('error' if submission.errors[:billing_zip_code].any?)] }
      = submission_form_builder.label :billing_zip_code, 'Zip Code'
      = submission_form_builder.text_field :billing_zip_code, placeholder: 'Zip Code', required: true

.ui.vertical.segment.basic
  By submitting this form, you agree to our
  = link_to 'Terms of Use', terms_path, target: :_blank
  and
  = succeed '.' do
    = link_to 'Privacy Policy', privacy_path, target: :_blank

- if payment_settings.cc_enabled?
  :javascript
    var prepareCreditCard = function () {
      $('input#custom_forms_submission_payment_method').val('cc')

      $('#cc-button').addClass('green');
      $('#ach-button').removeClass('green');

      $('#credit-card-fields').show();
      $('#cc-processing-message').show();

      $('#ach-fields').hide();
      $('#ach-processing-message').hide();

      $("label[for='custom_forms_submission_account_holder_name']").text('Card Holder Name');
      
      $('input#custom_forms_submission_ach_account_number').val('')
      $('input#custom_forms_submission_ach_routing_number').val('')
      $('input#custom_forms_submission_ach_account_type_checking').prop('checked', false)
      $('input#custom_forms_submission_ach_account_type_savings').prop('checked', false)

      var total_cost_text = "Pay #{payment_settings.total('cc').format}";
      $('#custom_form_submissions_submit_payment_button').val(total_cost_text);
    };

    $('#cc-button').click(prepareCreditCard);

- if payment_settings.ach_debit_enabled?
  :javascript
    var prepareACH = function () {
      $('input#custom_forms_submission_payment_method').val('ach')

      $('#cc-button').removeClass('green');
      $('#ach-button').addClass('green');

      $('#credit-card-fields').hide();
      $('#cc-processing-message').hide();

      $('#ach-fields').show();
      $('#ach-processing-message').show();

      $("label[for='custom_forms_submission_account_holder_name']").text('Account Holder Name');

      $('input#custom_forms_submission_credit_card_number').val('')
      $('input#custom_forms_submission_credit_card_expiration_date').val('')
      $('input#custom_forms_submission_credit_card_security_code').val('')

      var total_cost_text = "Pay #{payment_settings.total('ach').format}";
      $('#custom_form_submissions_submit_payment_button').val(total_cost_text);
    };

    $('#ach-button').click(prepareACH);

:javascript
  $('input#custom_forms_submission_credit_card_number').on('input', function () {
    var value = $(this).val();

    var klass = 'credit card icon';

    if (value.length) {
      if (value[0] === '3') {
        klass = 'cc amex icon';
      } else if (value[0] === '4') {
        klass = 'cc visa icon';
      } else if (value[0] === '5') {
        klass = 'cc mastercard icon';
      } else if (value[0] === '6') {
        klass = 'cc discover icon';
      }
    }

    $('#card-icon').attr('class', klass);
  });

  $('input#custom_forms_submission_credit_card_expiration_date').dateify();
  $('.ui.radio.checkbox').checkbox()
  $('.ui.dropdown').dropdown();
