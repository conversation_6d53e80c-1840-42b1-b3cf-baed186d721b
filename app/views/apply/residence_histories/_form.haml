:ruby
  residence = LeaseApplication::Residence.new

  residence.build_address

  url = hosted_application_applicant_residence_histories_path(
    @lease_application.uuid, @applicant.id
  )

= form_with model: residence,
  id: :residence_form,
  url: url,
  local: false,
  remote: true,
  scope: :residence,
  class: 'ui form' do |f|

  .three.required.fields
    .field
      = f.label :name, 'Description'
      = f.text_field :name, placeholder: 'e.g. Apartment or House'

    .field
      = f.label :monthly_amount, 'Monthly Payment'
      = f.money_field :monthly_amount

    .field
      = f.label :rental, 'Rental Property'
      = f.semantic_dropdown :rental, [['Yes', true], ['No', false]]

  .three.required.fields
    .field
      = f.label :start_date, 'Move In'
      = f.semantic_date_field :start_date

    .field
      = f.label :end_date, 'Move Out'
      = f.semantic_date_field :end_date

    .field
      = f.label :reason_for_moving, 'Reason for Moving'
      = f.text_field :reason_for_moving

  %h4.ui.dividing.header
    Contact

  .ui.info.message
    %p
      Residence history for rental properties requies a valid email and phone
      so that we may reach out if necessary.

  .three.fields
    .field
      = f.label :contact, 'Name'
      = f.text_field :contact

    .field
      = f.label :email
      = f.email_field :email

    .field
      = f.label :phone, 'Phone Number'
      = f.text_field :phone


  %h4.ui.dividing.header
    Address

  = render partial: 'shared/address_fields', locals: { f: f }

  .ui.error.message
