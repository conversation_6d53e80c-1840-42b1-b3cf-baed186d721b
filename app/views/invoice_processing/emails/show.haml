:ruby
  content_for(:title) { 'Invoice Processing' }

  claimed = @email.assigned_to == current_invoice_processor
  not_rejectable = @email.processed? || @email.invoices.present?

  label_color = case @email.status
                when 'unprocessed' then 'grey'
                when 'completed' then 'green'
                else 'red'
                end

- content_for :label do
  .ui.label{ class: label_color }
    = @email.status.titleize

-# TODO: Credit form
.ui.divided.grid{style: 'padding-top: 0.5rem;' }
  .seven.wide.column
    = render partial: 'info', locals: { email: @email }
    = render partial: 'attachments', locals: { email: @email }
    = render partial: 'invoices', locals: { email: @email }

  -# Invoice processing form
  .nine.wide.column{ style: 'padding-left: 2.5rem; padding-right: 2.5rem' }
    = render 'application/flash_notices'

    = link_to @email.completed? ? 'Completed' : 'Complete',
      complete_invoice_processing_email_path(id: @email,
      customer_subdomain: params[:customer_subdomain]),
      class: class_names('ui right floated green button', disabled: @email.processed?)

    .ui.right.floated.orange.button{ class: class_names(disabled: not_rejectable ),
    id: 'reject-email-button' }
      = @email.rejected? ? 'Rejected' : 'Reject'

    = link_to claimed ? 'Claimed' : 'Claim',
      claim_invoice_processing_email_path(id: @email,
      customer_subdomain: params[:customer_subdomain]),
      class: class_names('ui right floated blue button', disabled: claimed),
      data: { confirm: "Claim email assigned to #{@email.assigned_to.name}?" }

    - if @email.completed?
      .ui.basic.center.aligned.secondary.segment{ style: 'margin-top: 10rem' }
        Completed!
    - elsif @email.rejected?
      .ui.basic.center.aligned.secondary.segment{ style: 'margin-top: 10rem' }
        Rejected!
    - if @email.unprocessed?
      = render partial: 'accounting/shared/invoices/form/updated',
               locals: { email: @email, invoice: @invoice, accounting_side: :payables }

    = render partial: 'reject_modal', locals: { email: @email }

:javascript
  $('#invoice_form').submit(function() {
    var attachments = []

    $('#attachment-list input[type=checkbox]').each(function() {
      if ($(this).prop('checked')) {
        attachments.push($(this).data('id'))
      }
    });

    $('#invoice_selected_attachment_ids').val(JSON.stringify(attachments));
  });
