.ui.visible.inverted.left.vertical.desktop.sidebar.menu
  - unless Customer.current_subdomain == 'ccp'
    = menu_link 'dashboard', :owners_dashboard do
      %i.dashboard.icon
      Dashboard

    = menu_link 'messages', :owners_messages do
      %i.mail.outline.icon
      Messages

      - if try(:unread_message_count)&.positive?
        .ui.small.label= unread_message_count

    = menu_link 'companies', :owners_companies do
      %i.building.icon
      Entities

    = menu_link 'properties', :owners_properties do
      %i.home.icon
      Properties

    - if Feature.enabled?(:owner_portal_maintenance_tickets, Customer.current)
      = menu_link 'maintenance', :owners_maintenance_tickets do
        %i.wrench.icon
        Maintenance

    = menu_link 'invoices', :owners_invoices do
      %i.file.alternate.outline.icon
      Expenses

  - else
    = menu_link 'invoice_approvals', :owners_invoice_approvals do
      %i.thumbs.up.icon
      Invoice Approvals

  = menu_link 'reports', :owners_reports do
    %i.line.chart.icon
    Reports

  - if Owners::DocumentsController.enabled?(current_owner)
    = menu_link 'documents', :owners_documents do
      %i.file.outline.icon
      Documents

  = menu_link 'notifications', :owners_notifications do
    %i.alarm.outline.icon
    Notifications

    - if try(:unseen_notification_count)&.positive?
      .ui.small.label= unseen_notification_count

  = menu_link 'account', :owners_account do
    %i.options.icon
    Account

  = link_to destroy_owner_session_path, method: :delete, class: 'item' do
    %i.sign.out.icon
    Logout
