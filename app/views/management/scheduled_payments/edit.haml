.ui.breadcrumb
  - if @parent.is_a?(Tenant)
    = Breadcrumb::Tenant.(@parent, user: current_property_manager)

    = link_to @parent.name, tenant_path(@parent, anchor: '/electronic-payments')
    %i.right.angle.icon.divider
  - else
    = link_to @parent.class.name.pluralize,
      @parent.class,
      class: 'section'
    %i.right.angle.icon.divider

    = link_to @parent.name,
      @parent,
      class: 'section'
    %i.right.angle.icon.divider

  = link_to @scheduled_payment.description,
    [@parent, @scheduled_payment],
    class: 'section'
  %i.right.angle.icon.divider

  %h1.ui.header
    Edit Scheduled Payment

.ui.text.container
  = render partial: 'shared/scheduled_payments/form',
    locals: { action: [@parent, @scheduled_payment] }
