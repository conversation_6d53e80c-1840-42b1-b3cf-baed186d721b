.ui.breadcrumb
  = link_to 'Owners', owners_path, class: 'section'
  %i.right.angle.icon.divider
  = link_to @owner.name, owner_path(@owner), class: 'section'
  %i.right.angle.icon.divider
  %h1.ui.header
    Archive #{@owner.name}

.ui.divider

= form_with scope: :owner,
  url: archive_owner_path(@owner),
  local: true,
  class: 'ui form' do |f|

  .ui.message
    .header Pick Effective Date

    %p
      After the effective date, #{@owner.name} will no longer show up or
      receive communications in Revela.

      All accounting and other historical information will be kept for review.

  .required.field
    = f.label :archived_at, 'Effective Date'
    = f.semantic_date_field :archived_at

  - if @entities.present?
    .ui.message
      .header
        #{@owner.name}'s Solely Owned Entities and Properties

      %p
        These entities and properties can be archived as of the effective date.

      %ul
        - @entities.each do |entity|
          %li
            = link_to entity.name, manage_company_path(entity)
            %ul
              - entity.properties.each do |property|
                %li= link_to property.name, property_path(property)

    .field
      .ui.checkbox
        = f.check_box :archive_entities
        = f.label :archive_entities, 'Archive entities listed above'

  = f.submit 'Archive', class: 'ui right floated button'
