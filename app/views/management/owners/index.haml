.action-bar
  .ui.container
    %h1.ui.header
      Owners

    .action.buttons
      = link_to '#', id: 'invite-owner-button', class: 'ui button' do
        %i.ui.envelope.outline.icon
        Invite Owner

      = link_to new_owner_path, class: 'ui button' do
        %i.add.user.icon
        Add Owner

.action-container{ style: 'padding-top: 1em; overflow-y: scroll;' }
  .ui.container
    = render 'flash_notices'

    = ActionIndex::Owners.(self,
      collection: @owners,
      user: current_property_manager,
      partial: { path: 'management/owners/table',
      locals: { owners: @owners } })

    %div{ style: 'padding-top: 1rem;' }
      = paginate @owners

= render partial: 'invite_modal'
