.ui.breadcrumb
  = link_to @tenant.name,
    tenant_path(@tenant),
    class: 'section'
  %i.right.angle.icon.divider

  %h1.ui.header
    Transfer Security Deposit

.ui.narrow.container
  = form_with model: nil,
    url: tenant_deposit_transfers_path(@tenant),
    scope: :deposit_transfer,
    local: false,
    remote: true,
    class: 'ui form' do |f|

    .required.field
      = f.label :ledger_context_gid, 'Ledger'
      = f.semantic_dropdown :ledger_context_gid,
        ledger_context_options,
        { selected: @ledger_context.to_gid },
        options: { placeholder: 'Select' }

    .field
      = f.label :direction
      = f.semantic_dropdown :direction,
        [['Transfer From Owner to Management Company', 'from_owner'],
        ['Transfer From Management Company to Owner', 'from_management']]

    .two.fields
      .field
        = f.label :date
        = f.semantic_date_field :date, value: Time.zone.today

      .required.field
        = f.label :amount
        = f.money_field :amount, value: @deposit_balance.format

    .ui.error.message

    = f.submit 'Submit', class: 'ui primary submit button'
