:ruby
  options = [['Select', nil]] +
            f.object.management_contract.company.properties.pluck(:name, :id)

%tr.nested-fields
  %td.four.wide
    = f.semantic_dropdown :property_id,
      options,
      {},
      class: 'ui fluid search selection dropdown',
      data: { options: { placeholder: 'Select' } }

  %td.four.wide
    = f.semantic_date_field :start_date,
      class: 'ui fluid calendar'

  %td.four.wide
    = f.semantic_date_field :end_date

  %td.three.wide
    = f.money_field :reserve_amount

  %td.one.wide
    = link_to_remove_association f, class: 'ui basic icon button', style: 'float: right;' do
      %i.remove.icon
