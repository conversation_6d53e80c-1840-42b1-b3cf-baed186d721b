.ui.basic.padded.vertical.segment
  %h4.ui.dividing.header
    Account Settings

  %table.ui.very.basic.selectable.single.line.compact.fixed.table
    %thead
      %tr
        %th.center.aligned Account
        %th.center.aligned Percentage
    %tbody
      - management_contract.account_settings.includes(:account).each do |setting|
        %tr
          %td.left.aligned
            = link_to setting.account.display_name,
              accounting_journal_account_path(management_contract.company,
              setting.account)
          %td.right.aligned
            #{setting.percentage}%
