= render Modal::BulkActionComponent.new id: :batch_turn_on_conflict_modal,
  title: 'Auto Assignment Conflicts',
  options: { submit: { text: 'Yes, Continue' },
  cancel: {  text: 'No, Cancel' },
  form: :confirm_batch_enable_form,
  controller_action: 'batch_enable' }  do |m|

  .content
    - if confirmed_conflicts.any?
      .ui.padded.embedded.error.message
        %p
          We found new conflicts with auto assignment for the following properties.
          By submitting, any new members added to these properties will now be auto assigned to this onboarding instead.

    - else
      .ui.padded.embedded.info.message
        %p
          One or more of the selected properties currently have auto assignment turned on for another onboarding.
          By submitting, any new members added to these properties will now be auto assigned to this onboarding instead.

    .ui.bulleted.list
      - new_conflicts.each do |conflict|
        .item
          %strong= conflict.name

      - if confirmed_conflicts.any?
        - confirmed_conflicts.each do |conflict|
          .item
            = conflict.name

    Do you wish to continue?

    = form_with url: batch_turn_on_onboarding_auto_assignments_path(@onboarding),
      local: false,
      remote: true,
      id: :confirm_batch_enable_form do |f|
      = f.hidden_field :redirect_path, value: redirect_path

      - m.selected_ids.each do |id|
        = f.hidden_field 'selected_ids[]', value: id

      - (new_conflicts + confirmed_conflicts).each do |property|
        = f.hidden_field 'confirmed_conflicts[]', value: property.id
