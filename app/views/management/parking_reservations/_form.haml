:ruby
  pricing_hash = {}

  space_dropdown_values = @parking_lot.parking_spaces.map do |space|
    if space.price
      pricing_hash[space.id] = space.price.format
      ["#{space.name} (#{space.price.format})", space.id]
    else
      [space.name, space.id]
    end
  end

  is_commercial_parking = @parking_lot.property&.parking?

  commercial_leases = []

  if is_commercial_parking
    @reservation.build_tenant if @reservation.tenant.blank?
    properties = @parking_lot.property.portfolio.properties
    commercial_leases = LeasesQuery.new.search.by_properties(properties)
  end

= form_for [@parking_lot, @reservation],
  html: { class: 'ui parking-reservation form' } do |f|

  - if is_commercial_parking && @reservation.new_record?
    .two.fields
      .field
        = f.label :lease_id, 'Commercial Lease'
        = f.semantic_dropdown :lease_id,
          [['None', '']] + commercial_leases.map { |l| [l.tenants.first.name, l.id] },
          { selected: @reservation.lease_id },
          class: 'ui fluid search selection dropdown',
          data: { options: { placeholder: 'None' } }

      .field
        = f.label :kind, 'Style'
        = f.semantic_dropdown :kind,
          [['Single Vehicle', 'single'],
          ['Reservation Block', 'block']],
          { selected: nil }

    = render partial: 'parker_fields', locals: { f: f }

  .two.fields
    - if @reservation.tenant.blank?
      = react_component 'TenantSearchField',
        { name: 'parking_reservation[tenant_id]' },
        class: 'fluid field'

    - if is_commercial_parking

    - elsif @parking_lot.parking_spaces.any?
      .field
        = f.label :parking_space_id, 'Space'
        = f.semantic_dropdown :parking_space_id,
          [['Any', '']] + space_dropdown_values,
          { selected: @reservation.parking_space_id },
          id: :parking_space_id,
          data: { options: { placeholder: 'Any' } }
    - else
      .field
        = f.label :name, 'Space'
        = f.text_field :name

  = render partial: 'vehicle_fields', locals: { f: f }

  %h4.ui.header
    Details

  .field#space-count{ style: 'display: none;' }
    = f.label :space_count
    = f.number_field :space_count,
      min: 1, step: 1

  .three.fields
    .field
      = f.label :start_date
      = f.semantic_date_field :start_date

    .field
      = f.label :end_date
      = f.semantic_date_field :end_date

    .required.field
      = f.label :monthly_amount
      = f.money_field :monthly_amount,
        value: f.object.monthly_amount.presence&.format || ''

  .two.fields#tag-fields
    .field
      = f.label :tag_number
      = f.text_field :tag_number

    .field
      = f.label :card_number
      = f.text_field :card_number

  .field
    = f.label :note, 'Notes'
    = f.text_area :note, rows: 2

  .clearfix
    = f.submit 'Save', class: 'ui right floated submit button'

:javascript
  var pricing = JSON.parse('#{pricing_hash.to_json.html_safe}');

  var dropdown = $('select#parking_space_id');

  dropdown.dropdown('setting', 'onChange', function(id) {
    var value = pricing[id];
    if (value) {
      $('#parking_reservation_monthly_amount').val(value);
      $('#parking_reservation_monthly_amount').data('userInput', value);
    }
  });

  var setStyle = function (style, x, y, duration = 200) {
  console.log('duration', duration);
    if (style == 'single') {
      $('#vehicle-fields, #tag-fields, #parker-fields').show(duration);
      $('#space-count').hide(duration);
    } else {
      $('#vehicle-fields, #tag-fields, #parker-fields').hide(duration);
      $('#space-count').show(duration);
    }
  };

  $('#parking_reservation_kind').dropdown('setting', 'onChange', setStyle);

  setStyle('#{j @reservation.kind}', null, null, 0);
