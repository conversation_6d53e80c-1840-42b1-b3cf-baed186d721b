    <div data-w-id="00209533-64d0-966e-8d76-52dda09101d6" data-animation="default" data-collapse="medium" data-duration="400" data-easing="ease" data-easing2="ease" role="banner" class="navbar w-nav" style="background-color: black;">
      <div class="container">
        <div class="navbar-wrapper">
          <div class="split-contant navbar-left">
            <a href="/" class="navbar-logo-wrap w-nav-brand"><img src="https://revela-landing-holly.s3.us-east-1.amazonaws.com/images/Revela-Logo-white.png" loading="lazy" width="242" sizes="(max-width: 479px) 54vw, (max-width: 767px) 200px, 242px" alt="" srcset="https://revela-landing-holly.s3.us-east-1.amazonaws.com/images/Revela-Logo-white-p-500.png  500w, images/Revela-Logo-white.png 959w" class="navbar-logo"></a>
          </div>
          <div class="split-contant navbar-right">
            <div data-node-type="commerce-cart-wrapper" data-wf-cart-type="leftSidebar" data-wf-cart-query="" data-wf-page-link-href-prefix="" class="w-commerce-commercecartwrapper cart-wrapper">
              <a href="#" data-node-type="commerce-cart-open-link" aria-haspopup="dialog" aria-label="Open cart" role="button" class="w-commerce-commercecartopenlink cart-button w-inline-block"></a>
              <div data-node-type="commerce-cart-container-wrapper" style="display:none" class="w-commerce-commercecartcontainerwrapper w-commerce-commercecartcontainerwrapper--cartType-leftSidebar cart-contant-wrapper">
                <div role="dialog" data-node-type="commerce-cart-container" class="w-commerce-commercecartcontainer cart-container">
                  <div class="w-commerce-commercecartheader cart-header-wrapper">
                    <h4 class="w-commerce-commercecartheading cart-title">Your Cart</h4>
                    <a href="#" data-node-type="commerce-cart-close-link" role="button" aria-label="Close cart" class="w-commerce-commercecartcloselink cart-close-button w-inline-block"><img src="images/cart-close-icon.svg" loading="lazy" alt="" class="cart-close-icon"></a>
                  </div>
                  <div class="w-commerce-commercecartformwrapper cart-from-wrapper">
                    <form data-node-type="commerce-cart-form" style="display:none" class="w-commerce-commercecartform">
                      <script type="text/x-wf-template" id="wf-template-0d4e67ef-c9e0-ede0-0f94-16bd3311ce5d"></script>
                      <div class="w-commerce-commercecartlist cart-list-wrapper" data-wf-collection="database.commerceOrder.userItems" data-wf-template-id="wf-template-0d4e67ef-c9e0-ede0-0f94-16bd3311ce5d"></div>
                      <div class="w-commerce-commercecartfooter cart-footer">
                        <div aria-atomic="true" aria-live="polite" class="w-commerce-commercecartlineitem cart-line-item">
                          <div class="cart-subtotal">Subtotal</div>
                          <div class="w-commerce-commercecartordervalue cart-total-price"></div>
                        </div>
                        <div>
                          <div data-node-type="commerce-cart-quick-checkout-actions" class="web-payments">
                            <a data-node-type="commerce-cart-apple-pay-button" role="button" tabindex="0" aria-label="Apple Pay" aria-haspopup="dialog" style="background-image:-webkit-named-image(apple-pay-logo-white);background-size:100% 50%;background-position:50% 50%;background-repeat:no-repeat" class="w-commerce-commercecartapplepaybutton">
                              <div></div>
                            </a>
                            <a data-node-type="commerce-cart-quick-checkout-button" role="button" tabindex="0" aria-haspopup="dialog" style="display:none" class="w-commerce-commercecartquickcheckoutbutton"><svg class="w-commerce-commercequickcheckoutgoogleicon" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16" viewbox="0 0 16 16">
                                <defs>
                                  <polygon id="google-mark-a" points="0 .329 3.494 .329 3.494 7.649 0 7.649"></polygon>
                                  <polygon id="google-mark-c" points=".894 0 13.169 0 13.169 6.443 .894 6.443"></polygon>
                                </defs>
                                <g fill="none" fill-rule="evenodd">
                                  <path fill="#4285F4" d="M10.5967,12.0469 L10.5967,14.0649 L13.1167,14.0649 C14.6047,12.6759 15.4577,10.6209 15.4577,8.1779 C15.4577,7.6339 15.4137,7.0889 15.3257,6.5559 L7.8887,6.5559 L7.8887,9.6329 L12.1507,9.6329 C11.9767,10.6119 11.4147,11.4899 10.5967,12.0469"></path>
                                  <path fill="#34A853" d="M7.8887,16 C10.0137,16 11.8107,15.289 13.1147,14.067 C13.1147,14.066 13.1157,14.065 13.1167,14.064 L10.5967,12.047 C10.5877,12.053 10.5807,12.061 10.5727,12.067 C9.8607,12.556 8.9507,12.833 7.8887,12.833 C5.8577,12.833 4.1387,11.457 3.4937,9.605 L0.8747,9.605 L0.8747,11.648 C2.2197,14.319 4.9287,16 7.8887,16"></path>
                                  <g transform="translate(0 4)">
                                    <mask id="google-mark-b" fill="#fff">
                                      <use xlink:href="#google-mark-a"></use>
                                    </mask>
                                    <path fill="#FBBC04" d="M3.4639,5.5337 C3.1369,4.5477 3.1359,3.4727 3.4609,2.4757 L3.4639,2.4777 C3.4679,2.4657 3.4749,2.4547 3.4789,2.4427 L3.4939,0.3287 L0.8939,0.3287 C0.8799,0.3577 0.8599,0.3827 0.8459,0.4117 C-0.2821,2.6667 -0.2821,5.3337 0.8459,7.5887 L0.8459,7.5997 C0.8549,7.6167 0.8659,7.6317 0.8749,7.6487 L3.4939,5.6057 C3.4849,5.5807 3.4729,5.5587 3.4639,5.5337" mask="url(#google-mark-b)"></path>
                                  </g>
                                  <mask id="google-mark-d" fill="#fff">
                                    <use xlink:href="#google-mark-c"></use>
                                  </mask>
                                  <path fill="#EA4335" d="M0.894,4.3291 L3.478,6.4431 C4.113,4.5611 5.843,3.1671 7.889,3.1671 C9.018,3.1451 10.102,3.5781 10.912,4.3671 L13.169,2.0781 C11.733,0.7231 9.85,-0.0219 7.889,0.0001 C4.941,0.0001 2.245,1.6791 0.894,4.3291" mask="url(#google-mark-d)"></path>
                                </g>
                              </svg><svg class="w-commerce-commercequickcheckoutmicrosofticon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewbox="0 0 16 16">
                                <g fill="none" fill-rule="evenodd">
                                  <polygon fill="#F05022" points="7 7 1 7 1 1 7 1"></polygon>
                                  <polygon fill="#7DB902" points="15 7 9 7 9 1 15 1"></polygon>
                                  <polygon fill="#00A4EE" points="7 15 1 15 1 9 7 9"></polygon>
                                  <polygon fill="#FFB700" points="15 15 9 15 9 9 15 9"></polygon>
                                </g>
                              </svg>
                              <div>Pay with browser.</div>
                            </a>
                          </div>
                          <a href="checkout.html" value="Continue To Chackout" data-node-type="cart-checkout-button" class="w-commerce-commercecartcheckoutbutton primary-button cart" data-loading-text="Hang Tight...">Continue To Chackout</a>
                        </div>
                      </div>
                    </form>
                    <div class="w-commerce-commercecartemptystate">
                      <div aria-live="polite" aria-label="This cart is empty">No items found.</div>
                    </div>
                    <div aria-live="assertive" style="display:none" data-node-type="commerce-cart-error" class="w-commerce-commercecarterrorstate">
                      <div class="w-cart-error-msg" data-w-cart-quantity-error="Product is not available in this quantity." data-w-cart-general-error="Something went wrong when adding this item to the cart." data-w-cart-checkout-error="Checkout is disabled on this site." data-w-cart-cart_order_min-error="The order minimum was not met. Add more items to your cart to continue." data-w-cart-subscription_error-error="Before you purchase, please use your email invite to verify your address so we can send order updates.">Product is not available in this quantity.</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="split-contant navbar-middle">
              <nav role="navigation" class="nav-menu-wrap w-nav-menu">
                <div class="nav-menu-links-wrap">
                  <div data-hover="true" data-delay="0" data-w-id="aa0a34cf-bcb0-f7f0-df1c-70a224abb971" class="menu-dropdown-wrapper w-dropdown">
                    <nav class="dropdown-column-wrapper two w-dropdown-list">
                      <div class="dropdown-pd pd-60px homes radius-10">
                        <a href="#" class="single-nav-link natural-color-900 w-inline-block">
                          <div>Home V1</div>
                        </a>
                        <a href="index.html" class="single-nav-link natural-color-900 mg-bottom-0px w-inline-block">
                          <div>Home V2</div>
                        </a>
                      </div>
                    </nav>
                  </div>
                  <a href="/company" class="single-nav-link w-inline-block">
                    <div class="text-block">About Us</div>
                  </a>
                  <div data-hover="true" data-delay="0" data-w-id="cece7e2f-a92c-60e1-e245-64d52ac75a03" class="menu-dropdown-wrapper w-dropdown">
                    <div class="dropdown-toggle two w-dropdown-toggle"></div>
                    <nav class="dropdown-column-wrapper w-dropdown-list">
                      <div class="dropdown-pd pd-60px">
                        <div class="w-layout-grid dropdown-grid">
                          <div id="w-node-cece7e2f-a92c-60e1-e245-64d52ac75a0c-a09101d6" class="dropdown-singel-wrapper">
                            <div class="dropdown-title-wrapper">
                              <h2 class="dropdown-title">Menu</h2>
                            </div>
                            <div class="dropdown-menu-link-grid">
                              <div class="dropdown-menu-wrapper">
                                <a href="#" class="single-nav-link natural-color-900 w-inline-block">
                                  <div>Home V1</div>
                                </a>
                                <a href="index.html" class="single-nav-link natural-color-900 w-inline-block">
                                  <div>Home V2</div>
                                </a>
                                <a href="about-us.html" class="single-nav-link natural-color-900 w-inline-block">
                                  <div>About Us</div>
                                </a>
                                <a href="#" class="single-nav-link natural-color-900 w-inline-block">
                                  <div>Blog</div>
                                </a>
                                <a href="https://zaislam.webflow.io/blog/the-long-barrow-was-built-on-land-previously-inhabited-in-mesolithics" class="single-nav-link natural-color-900 w-inline-block">
                                  <div>Blog Details</div>
                                  <div class="dropdown-menu-cms">- CMS</div>
                                </a>
                                <a href="template-pages/career.html" class="single-nav-link natural-color-900 w-inline-block">
                                  <div>Career</div>
                                </a>
                                <a href="https://zaislam.webflow.io/career/ui-ux-designer" class="single-nav-link natural-color-900 w-inline-block">
                                  <div>Career Single</div>
                                  <div class="dropdown-menu-cms">- CMS</div>
                                </a>
                                <a href="#" class="single-nav-link natural-color-900 w-inline-block">
                                  <div>Integrations</div>
                                </a>
                                <a href="https://zaislam.webflow.io/integration/zapier" class="single-nav-link natural-color-900 mg-bottom-0px w-inline-block">
                                  <div>Integration Single</div>
                                  <div class="dropdown-menu-cms">- CMS</div>
                                </a>
                              </div>
                              <div class="dropdown-menu-wrapper">
                                <a href="template-pages/pricing.html" class="single-nav-link natural-color-900 w-inline-block">
                                  <div>Pricing</div>
                                </a>
                                <a href="https://zaislam.webflow.io/product/personal-plan" class="single-nav-link natural-color-900 w-inline-block">
                                  <div>Pricing Single</div>
                                  <div class="dropdown-menu-cms">- CMS</div>
                                </a>
                                <a href="/contact" aria-current="page" class="single-nav-link natural-color-900 w-inline-block w--current">
                                  <div>Contact</div>
                                </a>
                                <a href="/contact" class="single-nav-link natural-color-900 w-inline-block">
                                  <div>Book A Demo</div>
                                </a>
                                <a href="template-pages/download.html" class="single-nav-link natural-color-900 mg-bottom-0px w-inline-block">
                                  <div>Download</div>
                                </a>
                              </div>
                            </div>
                          </div>
                          <div id="w-node-cece7e2f-a92c-60e1-e245-64d52ac75a45-a09101d6" class="dropdown-singel-wrapper">
                            <div class="dropdown-title-wrapper">
                              <h2 class="dropdown-title">Utility Pages</h2>
                            </div>
                            <div class="dropdown-menu-wrapper">
                              <a href="utility-pages/style-guide.html" class="single-nav-link natural-color-900 w-inline-block">
                                <div>Style Guide</div>
                              </a>
                              <a href="utility-pages/license.html" class="single-nav-link natural-color-900 w-inline-block">
                                <div>License</div>
                              </a>
                              <a href="utility-pages/changelog.html" class="single-nav-link natural-color-900 w-inline-block">
                                <div>Changelog</div>
                              </a>
                              <a href="401.html" class="single-nav-link natural-color-900 w-inline-block">
                                <div>Password Protected</div>
                              </a>
                              <a href="404.html" class="single-nav-link natural-color-900 w-inline-block">
                                <div>404 Not Found</div>
                              </a>
                              <a href="authentication-pages/sign-in.html" class="single-nav-link natural-color-900 w-inline-block">
                                <div>Sign In</div>
                              </a>
                              <a href="authentication-pages/sign-up.html" class="single-nav-link natural-color-900 w-inline-block">
                                <div>Sign Up</div>
                              </a>
                              <a href="authentication-pages/forgot.html" class="single-nav-link natural-color-900 w-inline-block">
                                <div>Forgot Password</div>
                              </a>
                            </div>
                            <div class="more-temple-wrapper">
                              <a href="https://webflow.com/templates/designers/flowzai" target="_blank" class="more-temple-text-link">More Templates Form Us</a>
                              <div class="more-temple-text-link-boder"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </nav>
                  </div>
                  <a href="/contact" class="primary-button mobile-button w-inline-block">
                    <div>Book a Demo</div>
                    <div class="primary-button-bg"></div>
                  </a>
                </div>
              </nav>
            </div>
            <a href="/contact" aria-current="page" class="primary-button none w-inline-block w--current">
              <div class="primary-button-bg"></div>
              <div>Book a Demo</div>
            </a>
            <div class="hamburger-menu-wrapper w-nav-button">
              <div class="hamburger-menu-lines">
                <div class="hamburger-menu-bar top"></div>
                <div class="hamburger-menu-bar bottom"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <section class="section contact" style="margin-top: 4rem;">
      <div class="container">
        <div class="contact-us-wrapper">
          <div data-w-id="9f22cb0e-d3d5-7a81-0229-7b68ccea58fc" class="contact-us-card" style="background-color: rgb(183, 183, 183);">
            <div class="contact-us-card-contant">
              <h3 class="contact-us-title" style="color: #0a0a0a;">Book a Demo</h3>
              <div class="contact-us-from-wrap">
                <div id="talk-to-sales-form" class="contact-us-from-block w-form">
                  <form id="wf-form-talk-to-sales" name="wf-form-talk-to-sales" data-name="talk-to-sales" method="get" class="contact-us-from" data-wf-page-id="6557d7082bfc629d80d83d83" data-wf-element-id="9f22cb0e-d3d5-7a81-0229-7b68ccea5902">
                    <div id="w-node-_9f22cb0e-d3d5-7a81-0229-7b68ccea5903-80d83d83" class="input-wrapper"><label for="firstname-2" class="input-name" style="color: #0a0a0a;">First Name</label><input class="input contact w-input" maxlength="256" name="firstname" data-name="firstname" placeholder="" type="text" id="firstname-2" required=""></div>
                    <div id="w-node-f8742949-3c1e-f6cc-d1f3-979a36c2f544-80d83d83" class="input-wrapper"><label for="lastname-2" class="input-name" style="color: #0a0a0a;">Last Name</label><input class="input contact w-input" maxlength="256" name="lastname" data-name="lastname" placeholder="" type="text" id="lastname-2" required=""></div>
                    <div id="w-node-_9f22cb0e-d3d5-7a81-0229-7b68ccea5907-80d83d83" class="input-wrapper"><label for="email" class="input-name" style="color: #0a0a0a;">Your Email</label><input class="input contact w-input" maxlength="256" name="email" data-name="Email" placeholder="" type="email" id="email" required=""></div>
                    <div id="w-node-a2debf58-5689-8f04-12a5-c1400534e54c-80d83d83" class="input-wrapper"><label for="company-2" class="input-name" style="color: #0a0a0a;">Company Name</label><input class="input contact w-input" maxlength="256" name="company" data-name="company" placeholder="" type="text" id="company-2" required=""></div>
                    <div id="w-node-_9f22cb0e-d3d5-7a81-0229-7b68ccea590f-80d83d83" class="input-wrapper"><label for="whattypeofrealestateassetsdoyoumanage" class="input-name" style="color: #0a0a0a;">What type of real estate do you manage?</label><input class="input contact w-input" maxlength="256" name="whattypeofrealestateassetsdoyoumanage" data-name="whattypeofrealestateassetsdoyoumanage" placeholder="" type="text" id="whattypeofrealestateassetsdoyoumanage" required=""></div>
                    <div id="w-node-_6d14992e-e114-1bd1-a013-984e5320edd7-80d83d83" class="input-wrapper"><label for="howmanyunitsdoyoumanage" class="input-name" style="color: #0a0a0a;">How many units do you manage?</label><input class="input contact w-input" maxlength="256" name="howmanyunitsdoyoumanage" data-name="howmanyunitsdoyoumanage" placeholder="" type="text" id="howmanyunitsdoyoumanage" required=""></div>
                    <div id="w-node-_9f22cb0e-d3d5-7a81-0229-7b68ccea5917-80d83d83" class="submit-button-wrap">
                      <div class="code-embed w-embed"><input type="hidden" id="utm_source" name="utm_source" value="">
                        <input type="hidden" id="utm_medium" name="utm_medium" value="">
                        <input type="hidden" id="utm_campaign" name="utm_campaign" value="">
                        <input type="hidden" id="utm_term" name="utm_term" value="">
                        <input type="hidden" id="utm_content" name="utm_content" value="">
                      </div><input type="submit" data-wait="Please wait..." id="submitButton" class="submit-button contact-us w-button" value="Contact Now">
                    </div>
                  </form>
                  <div id="form-done" class="w-form-done">
                    <div class="text-block-4" style="color: black;">Thank you! Your submission has been received!</div>
                  </div>
                  <div class="w-form-fail" style="color: black;">
                    <div>Oops! Something went wrong while submitting the form.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
      <div class="ui inverted vertical footer segment">
<div class="ui container">
<div class="ui four column stackable grid">
<div class="column">
<h4 class="ui header">
Contact Us
</h4>
<div class="ui inverted link list">
<div class="item">1420 Washington Blvd.</div>
<div class="item">Suite 301</div>
<div class="item">Detroit, MI</div>
</div>
</div>
<div class="column">
<h4 class="ui header">
Resources
</h4>
<div class="ui inverted link list">
<a class="item" href="/training">
Training
</a>
<a class="item" href="/contact">
Contact Us
</a>
</div>
</div>
<div class="column">
<h4 class="ui header">
About
</h4>
<div class="ui inverted link list">
<a class="item" href="/company">
Company
</a>
<a class="item" href="/careers">
Careers
</a>
<a class="item" href="https://www.revela.co/blog" target="_blank">
Blog
</a>
</div>
</div>
<div class="column">
Revela's mission is to build and deliver technology that improves the
efficiency, sustainability, and quality of life for the communities we
serve by providing them with solutions to analyze, understand, and
manage the universe’s assets.
</div>
</div>
<div class="copyright">
<div class="right floated ui horizontal inverted link list">
<a class="item" target="_blank" href="/privacy">Privacy</a>
<a class="item" target="_blank" href="/terms">Terms</a>
<a class="item" href="/legal">Legal</a>
<a class="item" href="/security">Security</a>
<a class="item" target="_blank" href="/status">Status</a>
</div>
<img class="ui small image" src="//revela-public.s3.amazonaws.com/landing/images/logo_text_white.svg">
© 2023 Revela. All rights reserved.
&nbsp;
&nbsp;
</div>
</div>
</div>
  <script src="https://js.na.chilipiper.com/marketing.js" type="text/javascript" async=""></script>
  <script src="https://d3e54v103j8qbb.cloudfront.net/js/jquery-3.5.1.min.dc5e7f18c8.js?site=6557d7082bfc629d80d83d3b" type="text/javascript" integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous"></script>
  <script src="https://revela-landing-holly.s3.us-east-1.amazonaws.com/js/webflow.js" type="text/javascript"></script>
  <script>
function getUTMParameters() {
    const urlParams = new URLSearchParams(window.location.search);
    const utmParams = JSON.parse(getCookie('utmParams')) || {};
    // List of UTM parameters
    const utmKeys = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'];
    // Loop through the keys and extract them from the URL if present
    utmKeys.forEach(key => {
        const value = urlParams.get(key);
        if (value) {
            utmParams[key] = value;
        }
    });
    // Store UTM parameters in a cookie for 30 days
    setCookie('utmParams', JSON.stringify(utmParams), 30);
}
function setCookie(name, value, days) {
    const date = new Date();
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
    const expires = "expires=" + date.toUTCString();
    document.cookie = name + "=" + value + ";" + expires + ";path=/";
}
// Call the function when the page loads to capture UTM parameters
window.onload = function() {
    getUTMParameters();
};
</script>
  <script>
function applyUTMParametersToForm() {
    // Get the UTM parameters cookie
    const utmParams = getCookie('utmParams');
    // Parse the cookie value if it exists
    if (utmParams) {
        const utmData = JSON.parse(utmParams);
        // Get the form element
        const form = document.getElementById('wf-form-talk-to-sales');
        // Check if the form exists
        if (form) {
            // Apply the UTM parameters to the form fields
            for (const key in utmData) {
                const input = form.querySelector(`input[name="${key}"]`);
                if (input) {
                    input.value = utmData[key];
                }
            }
        }
    }
}
function getCookie(name) {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) === ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
}
// Call the function to apply UTM parameters when the page loads
window.onload = function() {
console.log("running utm capture")
    applyUTMParametersToForm();
};
if (document.getElementById("submitButton") != null) {
    document.getElementById("submitButton").addEventListener("click", function (event) {
        if (event.target.closest('form').checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
            let inputs = $(this).closest("form").find("input");
            let select = $(this).closest("form").find("select");
            let data = {};
            for (var i = 0; i < inputs.length; i++) {
                data[inputs[i].name] = inputs[i].value;
            }
            for (var i = 0; i < select.length; i++) {
                data[select[i].name] = select[i].options[select[i].selectedIndex].value;
            }
            console.log(data);
            // Submit the form using ChiliPiper
            ChiliPiper.submit("revela", "talk-to-sales", {
                lead: data,
                map: true
            });
            // Immediately hide the form and show the success message
            let formElement = document.getElementById("wf-form-talk-to-sales");
            if (formElement) {
                formElement.style.display = "none";
            }
            let successElement = document.getElementById("form-done");
            if (successElement) {
                successElement.style.display = "block";
            }
        }
    });
}
</script>
