= render Dashboards::CardComponent.new slug: :available_units,
  title: 'Available Units' do |c|

  - c.with_subtitle do
    = pluralize count, 'Vacant Unit'

  %table.ui.small.single.line.unstackable.compact.very.basic.selectable.fixed.table
    %thead
      %tr
        %th.center.aligned.seven.wide Property
        %th.center.aligned.five.wide Available Units
        %th.center.aligned.four.wide Vacancy
    %tbody
      - rows.map do |row|
        %tr
          %td
            = link_to row.property.name, property_path(row.property)
          %td.right.aligned
            = row.unit_count
          %td.right.aligned
            = number_to_percentage row.vacancy, precision: 0
