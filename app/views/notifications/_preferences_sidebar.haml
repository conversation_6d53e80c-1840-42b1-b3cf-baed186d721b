.ui.right.vertical.wide.sidebar.menu#notification-preferences-sidebar{ style: 'padding: 1em; transition-duration: .2s;' }
  %h2.ui.header
    Notification Preferences

  = form_with id: :notification_preferences,
    model: notification_preferences,
    url: { action: :update_preferences },
    local: false,
    method: :patch,
    class: 'ui form' do |f|

    .ui.very.basic.vertical.segment
      = render partial: 'notifications/preferences_form',
        locals: { f: f }

    .ui.very.basic.vertical.segment
      %button.ui.primary.button{ type: 'submit' }
        %i.save.icon
        Save

      %button.ui.basic.button#cancel{ type: 'button' }
        Cancel

:javascript
  var sidebar = $('#notification-preferences-sidebar');

  $('button#notification-preferences').click(function () {
    sidebar.sidebar({ transition: 'overlay' });

    sidebar.sidebar('toggle');
  });

  $('#notification-preferences-sidebar button#cancel').click(function () {
    sidebar.sidebar('hide');
  });

  $('form#notification_preferences')
    .on('ajax:before', function () {
      $('#notification_preferences .primary.button')
        .addClass('disabled loading');
    })
    .on('ajax:complete', function () {
      $('#notification_preferences .primary.button')
        .removeClass('disabled loading');
    })
    .on('ajax:success', function () {
      sidebar.sidebar('hide');
    });
