- if tenant.resident?
  = link_to tenant, class: 'item' do
    .content
      .left.floated.ui.mini.rounded.image
        %img{ src: tenant.avatar_url }
      .header
        = tenant.name
      .meta
        - if tenant.current_lease
          Active #{I18n.t('tenant_term')}
        - else
          Inactive #{I18n.t('tenant_term')}
- else
  = link_to leasing_lead_path(tenant), class: 'item' do
    .content
      .left.floated.ui.mini.rounded.image
        %img{ src: tenant.avatar_url }
      .header
        = tenant.name
      .meta
        = tenant.kind.capitalize
