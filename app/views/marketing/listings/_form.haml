:ruby
  property_zillow_claim =
    Zillow::Claim.new(
      address: Address.new(
        city: @listing.property.address.city,
        region: Address::REGION_INDEX.fetch(
          @listing.property.address.region_code,
          @listing.property.address.region
        ),
        postal_code: @listing.property.address.postal_code,
        country: @listing.property.address.country
      )
    )

  claim_options = []
  if @listing.persisted?
    claim_options = Zillow::Claims::DropdownOptions.new(listing_id: @listing.id).call
    current_claim_id = @listing.property.zillow_claim&.id&.to_s || ' '
    claim_options.find { _1[:value].to_s == current_claim_id }[:selected] = true
  end

  form_data = {
    controller: 'listing-form',
    listing_form_listing_id_value: @listing.id.to_s,
    listing_form_initial_claim_options_value: claim_options.to_json,
    action: 'claimCreated->listing-form#addClaim'
  }
= form_for [:marketing, @listing], html: { class: 'ui form listing',
  data: form_data } do |f|
  = hidden_field_tag :floorplan_id, @floorplan.id

  .field
    = f.label :name, 'Listing Name'
    = f.text_field :name, placeholder: @listing.default_name

  .field
    = f.label :agent_id, 'Listing Agent'
    - agents = @listing.available_agents(current_property_manager).map { |e| [e.name, e.id] }
    = f.semantic_dropdown :agent_id,
      [['None', nil]] + agents,
      {},
      class: 'ui search selection dropdown',
      data: { options: { placeholder: 'None' } }

  .field
    = f.label :price
    = f.money_field :price, value: @floorplan.price.to_f

  .three.fields
    .required.field
      = f.label :bathrooms
      = f.text_field :bathrooms

    .required.field
      = f.label :bedrooms
      = f.text_field :bedrooms

    .required.field
      = f.label :square_feet
      = f.text_field :square_feet

  .required.field
    = f.label :description
    = f.text_area :description

  - if Feature.enabled?(:custom_schedule_tour_link, Customer.current)
    .field
      = f.label :schedule_tour_url, 'Schedule Tour Link'
      = f.text_field :schedule_tour_url,
        placeholder: 'An optional URL to schedule a tour for this listing'

  .field
    = f.label :virtual_tour_url, 'Virtual Tour Link'
    = f.text_field :virtual_tour_url,
      placeholder: 'An optional URL to a virtual tour for this listing'

  .field
    %label Amenities
    = react_component 'AmenitiesInput',
      name: 'listing[amenities][]',
      amenities: @listing.amenities

  .field
    %label Photos
    = react_component 'PhotoAttachmentsArea', photos: serialize(@listing.photos)

  .field
    = f.label :notes, 'Notes or Showing Instructions'
    = f.text_area :notes, rows: 3,
      placeholder: 'Notes are internal and will not be displayed on the listing'
  - if Feature.enabled?(:direct_zillow_syndication, Customer.current)
    - if !f.object.persisted?
      .ui.info.message
        Create a listing before syndicating it.
      .field
        .ui.checkbox#zillow_feed_checkbox
          = f.check_box :zillow_feed, disabled: true
          = f.label :zillow_feed
    - else
      .field
        .ui.checkbox#zillow_feed_checkbox
          = f.check_box :zillow_feed
          = f.label :zillow_feed, 'Add to Zillow Feed'

      .ui.segment#zillow_fields{style: "display:#{f.object.zillow_feed? ? 'block' : 'none'}; padding: 2em;", }
        = render partial: 'syndication_details', locals: { listing: @listing }
        = render partial: 'zillow_fields', locals: { f: f }

  .clearfix
    = f.submit class: 'ui primary submit button'

= render partial: 'zillow_claim_modal',
  locals: { new_zillow_claim: property_zillow_claim, listing: @listing }
