.ui.breadcrumb
  = link_to 'Leasing', leasing_leads_path,
    class: 'section'
  %i.right.angle.icon.divider
  %h1.ui.header
    %span{ title: @lead.name }
      = @lead.name.truncate(50)
    .sub.header
      = @lead.kind.capitalize
    - @lead.tags.each do |tag|
      .ui.tiny.tag.label= tag

= ActionsMenu::Lead.(@lead, current_property_manager)
= render partial: 'temperature', locals: { lead: @lead }
= render partial: 'leasing_agent', locals: { lead: @lead }

= render partial: 'shared/tenant', locals: { tenant: @lead }
