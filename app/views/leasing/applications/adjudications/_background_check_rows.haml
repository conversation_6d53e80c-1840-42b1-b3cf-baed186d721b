- if application.background_checks.any?
  %tr
    %td{ colspan: 5 }
      %b Background Checks

  :ruby
    requirement_names = %i[
      credit_score
      age_restriction
      misdemeanor
      felony
      eviction
    ]

  - application.background_checks.each do |background_check|
    - tenant = background_check.tenant

    %tr
      %td{ colspan: 5 }
        &emsp;
        = link_to tenant.name, tenant.url
        &emsp;
        = link_to 'View Report',
          background_check.external_report_url,
          target: '_blank',
          rel: 'noopener'

    - requirement_names.each do |name|
      :ruby
        klass_name = \
          "LeaseApplication::ScreeningRequirement::#{name.to_s.classify}"

        requirement = klass_name.constantize.new(
          application, background_check
        )

      %tr{ id: name.to_s.dasherize, class: requirement.result_row_class }
        %td
        %td= requirement.name
        %td= requirement.configured || 'None Set'
        %td= requirement.result || 'Unknown'
        %td= requirement.result_description
