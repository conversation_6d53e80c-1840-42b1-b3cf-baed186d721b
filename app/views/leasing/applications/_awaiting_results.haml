- if application.awaiting_results?
  :ruby
    saferent_screening_pending = application.saferent_screening&.pending?
    background_check_count = application.background_checks.count(&:pending?)
    income_verification_count = application.income_verification&.screenings&.count(&:pending?) || 0
    landlord_verification_count = application.landlord_verifications.count(&:pending?)

    items = []

    if saferent_screening_pending
      items << 'SafeRent screening'
    end

    if background_check_count.positive?
      items << pluralize(background_check_count, 'background check')
    end

    if income_verification_count.positive?
      items << pluralize(income_verification_count, 'income verification')
    end

    if landlord_verification_count.positive?
      items << pluralize(landlord_verification_count, 'landlord verification')
    end

  = link_to leasing_application_adjudication_path(application),
    class: 'ui yellow icon message' do

    %i.wait.icon
    .content
      .header Awaiting Screening Results
      %p
        Currently waiting for results from #{items.to_sentence}.
        Click here to see more information.
