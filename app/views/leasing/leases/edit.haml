.ui.breadcrumb
  = link_to 'Leasing',
    leasing_leads_path,
    class: 'section'
  %i.right.angle.icon.divider

  = link_to "Lease #{@lease.id}",
    leasing_lease_path(@lease),
    class: 'section'

  %i.right.angle.icon.divider

  %h1.ui.header
    Edit Lease

= react_component 'NewLeaseForm',
  lease: serialize(@lease),
  show_grace_period_days: Feature.enabled?(:lease_grace_period_days, Customer.current),
  show_eligible_for_placement_fee: Customer.current.fee_management? || Customer.current.single_family_management? || Customer.current.asset_management?
