= form_for [:operations, @project, @task], html: { class: 'ui form' } do |f|
  - if @project.phases.any?
    .field
      = f.label :phase
      = react_component 'Dropdown',
        name: 'task[phase_id]',
        items: @project.phases.pluck_h(:id, :name),
        defaultSelection: @task.phase_id || params[:phase_id],
        fluid: true

  .required.field
    = f.label :name
    = f.text_field :name

  .required.field
    = f.label :description
    = f.text_area :description, rows: 2

  .two.fields
    .ui.calendar.field#due-date
      = f.label :due_date
      .ui.left.icon.input
        %i.calendar.icon
        = f.text_field :due_date

    .field
      = f.label :approving_role_id
      = react_component 'Dropdown',
        name: 'task[approving_role_id]',
        fluid: true,
        items: Role.pluck_h(:id, :name) + [{ id: '', name: 'None' }],
        defaultSelection: @task.approving_role_id,
        options: { placeholder: 'None' }

  .field
    = f.label :assigned_members
    = react_component 'Dropdown',
      name: 'task[assigned_member_ids][]',
      url: "/operations/projects/#{@project.id}/members.json",
      defaultSelection: @task.assigned_members.map(&:id).map(&:to_s),
      multiple: true,
      className: 'ui small fluid multiple search selection dropdown'

  .field
    = f.label :prerequisites
    = react_component 'Dropdown',
      name: 'task[prerequisite_ids][]',
      url: dependable_operations_project_tasks_path(@task.project_id, task_id: @task.id, format: :json),
      defaultSelection: @task.prerequisites.map(&:to_global_id).map(&:to_s),
      multiple: true,
      className: 'ui small fluid multiple search selection dropdown'

  .field
    = f.label :kind
    %input#task-kind{ type: 'hidden', value: @task.kind, name: 'task[kind]' }
    .ui.basic.kind.picker.buttons
      - Task.kinds.keys.each do |kind|
        %button.ui.button{ type: :button,
        'data-kind' => kind,
        class: (:active if @task.send("#{kind}?")) }
          = kind.titleize

  #todo-list-form{ style: @task.todo_list? ? nil : 'display: none;' }
    .field
      %label
        Todos
      = render partial: 'shared/todo_list/form',
        locals: { todo_list: @task.todo_list, f: f }

  #bid-collection-form{ style: @task.bid_collection? ? nil : 'display: none;' }
    .field
      = f.label :required_bid_count
      = f.number_field :required_bid_count, value: 3, min: 1

  %br
  = react_component 'AttachmentsInput', photos: serialize(@task.photos)

  = f.submit buttonText, class: 'ui primary submit button'

:javascript
  $('#due-date').calendar();
  $('.kind.picker.buttons .button').click(function() {
    var kind = $(this).data('kind');
    $('input#task-kind').val(kind);
    $('.kind.picker.buttons .button').removeClass('active');
    $(this).addClass('active');

    if (kind === 'todo_list') {
      $('#todo-list-form').show();
    } else {
      $('#todo-list-form').hide();
    }

    if (kind === 'bid_collection') {
      $('#bid-collection-form').show();
    } else {
      $('#bid-collection-form').hide();
    }
  });
