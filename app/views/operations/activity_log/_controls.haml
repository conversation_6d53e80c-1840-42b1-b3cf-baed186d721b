#activity-log-controls{ 'data-turbolinks-permantent' => 'true', 'data-controller' => 'reports--filters' }
  = form_with model: nil, class: 'ui form', url: nil do |f|
    -#.field
      .ui.calendar#activity-date-filter

    = render partial: 'reports/v3/filters/activity_type',
      locals: { f: f, report: @report }

    = render partial: 'reports/v3/filters/property',
      locals: { f: f, report: @report }

    = render partial: 'reports/v3/filters/employee',
      locals: { f: f, report: @report }

    = link_to operations_activity_log_path,
      class: "ui basic #{params.dig(:filters) ? nil : 'disabled'} fluid button" do
      Clear Filters

    :javascript
      $('#activity-date-filter')
        .calendar({
          type: 'date',
          inline: true,
          startCalendar: '#activity-date-filter',
        }).calendar(
          'set startDate', '2019-05-22'
        ).calendar(
          'set endDate', '2019-05-28'
        ).calendar(
          'set focusDate', '2019-05-28'
        );
