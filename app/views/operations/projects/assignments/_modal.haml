:ruby
  assignment = Project::AssignmentForm.new(
    project: @project,
  )

.ui.small.modal#assign-modal
  %i.ui.close.icon
  .header
    Assign
  .scrolling.content
    = render partial: 'operations/projects/assignments/form',
      locals: { assignment: assignment }

  .actions
    .ui.basic.cancel.button
      Cancel
    %button.ui.disabled.blue.button{ form: 'project_assignment',
    data: { disable: true } }
      Assign

:javascript
  var updateAssignmentForm = function (userSgid, assignableSgid) {
    $.get('#{new_operations_project_assignment_path(@project, format: :js)}?assignment[user]=' + userSgid + '&assignment[assignable_sgid]=' + assignableSgid);
  }

  window.hookupAssignmentSearch = function (assignableSgid) {
    $('#assign-modal .ui.search').search('setting', 'onSelect', function (selected) {
      var userSgid = selected && selected.sgid;

      $('#assign-modal .ui.blue.button').addClass('disabled').text('Assign');

      updateAssignmentForm(userSgid, assignableSgid);
    });
  }

  $('[data-action="assign-user"]').on('click', function (event) {
    event.preventDefault();

    var target = event.target;

    var assignableName = target.dataset.assignableName;
    var assignableSgid = target.dataset.assignableSgid;

    console.log('clicked', target);

    $('.ui.modal#assign-modal')
      .modal(modalDefaults)
      .modal('setting', 'autofocus', false)
      .modal('show');

    $('.ui.modal .header').text('Assign to ' + assignableName);

    hookupAssignmentSearch(assignableSgid);

    updateAssignmentForm('', assignableSgid);
  });
