= link_to new_operations_inspection_template_path,
  class: 'right floated ui button' do
  %i.plus.icon
  New Template

.ui.breadcrumb
  = link_to t('activerecord.models.inspection/report').pluralize,
    operations_inspections_path, class: 'section'
  %i.right.angle.icon.divider

  %h1.ui.header
    = t('activerecord.models.inspection/report')
    Templates

.ui.selection.list
  - @inspection_templates.each do |template|
    = link_to edit_operations_inspection_template_path(template), class: 'item' do
      .header=template.name
      Created
      = template.created_at.to_fs(:human_date)
