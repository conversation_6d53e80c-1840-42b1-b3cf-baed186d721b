:ruby
  total = section[:subsections].flat_map { |s| s[:accounts] }
                               .sum { |a| a[:balance_cents] }

%div{ style: 'flex: 1 1 auto; display: flex; flex-direction: column; margin: 0.75em;' }
  %h2.ui.dividing.header
    = section[:name]

  - section[:subsections].each do |subsection|
    = render partial: 'reports/v3/custom/balance_sheet/subsection',
      locals: { subsection: subsection }

  - unless section[:display_total] == false
    %table.ui.very.basic.compact.table{ style: 'margin-top: auto;' }
      %tr
        %td
          %i Total #{section[:name]}
        %td.right.aligned
          = Money.new(total).accounting_format
