:ruby
  bank_account_options = BankAccountsQuery
                         .new.search
                         .customer_managed
                         .pluck(:name, :id)

.field
  = f.label :bank_account_id, 'Bank Account'
  = f.semantic_dropdown :bank_account_id,
    [['All Accounts', '']] + bank_account_options,
    { selected: params.dig(:filters, :bank_account_id) },
    class: 'ui search selection dropdown',
    data: { options: { placeholder: 'All Accounts' } }
