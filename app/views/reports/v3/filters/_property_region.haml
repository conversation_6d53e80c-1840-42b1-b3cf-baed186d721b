:ruby
  region_names_from = lambda do |regions|    
    regions.filter_map do |region|
      next if region.blank?

      Address::REGION_INDEX[region.squish.upcase] || region.squish.titleize
    end
  end

  region_values = region_names_from[Property.includes(:address).pluck('addresses.region')].uniq.sort

.field
  = f.label :region
  = f.semantic_dropdown :property_region,
    [['Select', nil]] + region_values,
    { selected: params.dig(:filters, :property_region) },
    class: 'ui search selection dropdown',
    data: { options: { placeholder: 'Select' } }
