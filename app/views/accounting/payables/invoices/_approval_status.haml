- approved = invoice.invoice_payment_approved?

- if approved
  .ui.tiny.green.label#approval-popup
    Approved
- else
  .ui.tiny.orange.label#approval-popup
    Unapproved

.ui.flowing.popup.bottom
  .ui.list
    - rules = invoice.approval_rules(:invoice_payment)

    -# TODO: Clean up
    - if invoice.paid?
      - invoice.approvals.each do |approval|
        .item
          %i.green.check.mark.icon
          .content
            .header= approval.rule.name
            .description
              = approval.approved_by.name
              on
              = approval.created_at.to_fs(:short_datetime)

    - elsif rules.none?
      No approval rules to show.

    - rules.each do |rule|
      - approval = rule.approvals.find_by(approvable: invoice)
      - next if approved && approval.nil?

      .item
        - if approval
          %i.green.check.mark.icon
        - else
          %i.yellow.hourglass.half.icon
        .content
          .header= rule.name
          .description
            - if approval
              = approval.approved_by.name
              on
              = approval.created_at.to_fs(:short_datetime)
            - else
              Pending

:javascript
  $('#approval-popup').popup({
    inline: true,
    position: 'bottom center',
    hoverable: true,
  });
