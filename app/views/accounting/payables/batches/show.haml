= ActionsMenu::PaymentBatch.(@payment_batch, user: current_property_manager)
- require_permission :print_accounting_payables_batches? do
  = print_button
- require_permission :export_accounting_payables_batches? do
  = export_button(class_name: 'right floated ui button')

.ui.breadcrumb
  = link_to 'Payables', accounting_payables_path, class: 'section'
  %i.right.angle.icon.divider

  = link_to 'Batches', accounting_payables_batches_path, class: 'section'
  %i.right.angle.icon.divider

  %h1.ui.header
    = @payment_batch.name
    .sub.header
      - if @payment_batch.open?
        Open
      - else
        Closed
      Payment Batch

= render partial: 'batch', locals: { batch: @payment_batch }
= render partial: 'overdraft_warning', locals: { batch: @payment_batch }
