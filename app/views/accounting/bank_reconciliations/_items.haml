:ruby
  sort_class = ->(key, rest) do
    if params.dig(:sort, :column) == key
      if params.dig(:sort, :direction) == 'desc'
        "return #{rest} sorted descending"
      else
        "#{rest} sorted ascending"
      end
    else
      rest
    end
  end

%table.ui.sortable.selectable.single.line.compact.striped.fixed.very.basic.unstackable.table
  %thead
    %tr
      %th{ 'data-column' => 'date', class: sort_class['date', 'two wide'] } Date
      %th{ 'data-column' => 'reference_number', class: sort_class['reference_number', 'two wide center aligned'] } Reference
      %th{ 'data-column' => 'contact_name', class: sort_class['contact_name', 'three wide center aligned'] } Contact
      %th{ 'data-column' => 'property', class: sort_class['property', 'three wide center aligned'] } Property
      %th{ 'data-column' => 'description', class: sort_class['description', 'four wide center aligned'] } Memo
      %th{ 'data-column' => 'amount', class: sort_class['amount', 'two wide right aligned'] } Amount
      %th{ 'data-column' => 'reconciled', class: sort_class['reconciled', 'two wide right aligned'] } Cleared

  %tbody
    = f.collection_check_boxes :reconciled_amount_ids,
      items,
      :id,
      :id do |b|
      - item = b.object.decorate
      %tr
        %td= item.date
        %td= item.reference
        %td= item.contact
        %td= item.property
        %td= item.memo
        %td.right.aligned
          = Money.new(item.amount).abs.format
        %td.right.aligned
          .ui.checkbox{ class: item.checkbox_class }
            %label
            = b.check_box checked: item.reconciled?

- if items.none?
  .ui.info.message
    %p
      No unreconciled transactions posted up to and including
      = succeed '.' do
        = reconciliation.statement_date.to_datetime.to_fs(:short_date)

= paginate items, param_name: tab_page, params: { action: tab_name }
