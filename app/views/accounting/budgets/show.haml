.action-container
  %div{ style: 'padding-top: 1rem;' }
    .ui.container
      = link_to 'Edit',
        edit_accounting_budget_path(@budget),
        class: 'right floated ui button'

      .ui.breadcrumb
        = link_to 'Budgets', accounting_budgets_path, class: 'segment'
        %i.right.angle.icon.divider
        %h1.ui.header
          = @budget.name

  .ui.wide.container{ style: 'padding: 2rem;' }
    :ruby
      months = @budget.company.fiscal_year(@budget.year).months
      accounts = @budget.company.accounts.where(type: ['Plutus::Expense', 'Plutus::Revenue']).order(gl_code: :asc, name: :asc, id: :asc)

    %table.ui.small.compact.celled.selectable.very.basic.single.line.fixed.table
      %thead
        %tr
          %th.three.wide Account
          - months.map.with_index do |month, index|
            %th
              - if index.zero? || month.month == 1
                = month.strftime("%b '%y")
              - else
                = month.strftime('%B')
          %th
            Total

      %tbody
        - accounts.each do |account|
          - total = 0
          %tr
            %td
              = link_to account.display_name,
                @budget.account_link(account)
            - months.map do |month|
              %td.right.aligned
                - amount = @budget.budget_amounts.find_by(account: account, month: month)&.amount || Money.zero
                - total += amount
                = amount.accounting_format
            %td.right.aligned
              %b= total.accounting_format
