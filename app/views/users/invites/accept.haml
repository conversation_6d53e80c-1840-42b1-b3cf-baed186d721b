.login-container
  .login.company.logo{ style: "background-image:url(#{brand.logo_url}" }

  %h1.ui.header
    Create #{@invite.display_type} Profile

  = form_with model: @account,
    url: accept_users_invite_path(@invite),
    id: :account_form,
    local: false,
    remote: true,
    scope: :account,
    class: 'ui form' do |f|

    .two.required.fields
      .field
        = f.label :first_name
        = f.text_field :first_name

      .field
        = f.label :last_name
        = f.text_field :last_name

    .two.required.fields
      .disabled.field
        = f.label :email
        = f.email_field :email

      .field
        = f.label :phone
        = f.text_field :phone

    .two.required.fields
      .field
        = f.label :password
        = f.password_field :password

      .field
        = f.label :password_confirmation, 'Confirm Password'
        = f.password_field :password_confirmation

    .ui.error.message

    = f.submit 'Create Profile',
      class: 'ui blue submit button'
