.login-container
  %h2.ui.header Change Your Password

  = form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :put, class: 'ui form' }) do |f|
    = render partial: 'users/shared/error_messages', resource: resource

    = f.hidden_field :reset_password_token
    .field
      = f.label :password, 'New password'
      - if @minimum_password_length
        %em
          (#{@minimum_password_length} characters minimum)
        %br/
      = f.password_field :password, autofocus: true, autocomplete: 'new-password'

    .field
      = f.label :password_confirmation, 'Confirm new password'
      = f.password_field :password_confirmation, autocomplete: 'new-password'

    .ui.very.basic.clearing.segment
      = f.submit 'Change My Password',
        class: 'right floated ui button'
