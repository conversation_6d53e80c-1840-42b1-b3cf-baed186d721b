%table.ui.striped.table
  %tr
    %td IP Address
    %td
      = fingerprint.ip_address
  %tr
    %td Approximate Location
    %td
      = fingerprint.location

  - if user_agent = fingerprint.parsed_user_agent
    %tr
      %td Browser
      %td
        = user_agent.to_s
    %tr
      %td Operating System
      %td
        = user_agent.os
    %tr
      %td Device
      %td
        = user_agent.device
    %tr
      %td Time
      %td
        = fingerprint.created_at.to_fs(:human_time)
        = fingerprint.created_at.to_fs(:human_date)
