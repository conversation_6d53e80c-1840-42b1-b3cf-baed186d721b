class BuildingListExporter
  include XLSXExporter

  attr_reader :property

  def initialize(property:)
    @property = property
  end

  def filename
    "#{property.name.parameterize.underscore}_buildings.xlsx"
  end

  private

  def row_data
    property.buildings.includes(:address).map do |building|
      address = building.address

      [
        building.name,
        address&.line_one,
        address&.line_two,
        address&.city,
        address&.region,
        address&.postal_code,
        building.square_feet
      ]
    end
  end

  def columns
    {
      'Name' => true,
      'Line One' => false,
      'Line Two' => false,
      'City' => false,
      'State' => false,
      'Zip Code' => false,
      'Square Feet' => false
    }
  end
end
