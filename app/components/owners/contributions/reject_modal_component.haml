= render ModalComponent.new id: :reject_owner_contribution_request_modal,
  title: 'Reject Owner Contribution Request',
  options: { size: :small,
  form: :reject_owner_contribution_form } do

  .ui.top.embedded.info.message
    %p
      = requested_by.name
      will receive notice of your rejection with your reason given.

  = form_with model: nil,
    url: url,
    id: :reject_owner_contribution_form,
    local: false,
    remote: true,
    scope: :owner_contribution_request,
    class: 'ui form' do |f|

    .required.field
      = f.label :rejection_reason, 'Reason'
      = f.text_area :rejection_reason,
        rows: 3,
        placeholder: 'e.g. Defer repairs until next year'

    .ui.bottom.embedded.error.message
