class Tunisia::BankAccounts::StatementItemComponent < ApplicationComponent
  attr_reader :statement_hash, :bank_account

  def initialize(statement_hash:, bank_account:)
    super()
    @statement_hash = statement_hash
    @bank_account = bank_account
  end

  def period
    DateTime.strptime(statement_hash[:attributes][:period], '%Y-%m')
            .strftime('%B %Y')
  end

  def view_link
    link_to 'View',
            view_path,
            class: 'view',
            data: { modal: :statement_modal }
  end

  def pdf_download_link
    link_to 'Download PDF',
            pdf_download_path,
            target: '_blank',
            rel: 'noopener'
  end

  private

  def view_path
    statement_organization_tunisia_bank_account_path(
      bank_account,
      statement_id: statement_hash[:id], format: :html
    )
  end

  def pdf_download_path
    statement_organization_tunisia_bank_account_path(
      bank_account,
      statement_id: statement_hash[:id], format: :pdf
    )
  end
end
