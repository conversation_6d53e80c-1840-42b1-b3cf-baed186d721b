class Accounting::Payments::InvoicesTable::ApplyComponent < ApplicationComponent
  def initialize(apply:)
    super()
    @apply = apply
  end

  private

  delegate :date, :invoice, to: :@apply
  delegate :amount, :balance, to: :invoice, prefix: true

  def apply_amount
    @apply.amount
  end

  def reversed?
    @apply.reversed?
  end

  def reversal_description
    @apply.decorate.reversal_description
  end

  def invoice_description
    description = if invoice.seller.is_a?(Vendor)
                    [invoice.invoice_number, invoice.description].join(' - ')
                  else
                    invoice.description
                  end

    helpers.truncate(description)
  end

  def invoice_path
    if params[:controller].starts_with?('tenants')
      tenants_rent_invoice_path(invoice)
    elsif params[:controller].starts_with?('vendors')
      vendors_invoice_path(invoice)
    else
      invoice.url
    end
  end
end
