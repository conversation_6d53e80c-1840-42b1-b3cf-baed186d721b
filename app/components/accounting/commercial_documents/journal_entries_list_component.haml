.ui.very.basic.vertical.segment.nonprintable{ data: { controller: 'accounting--commercial-document--journal-entries-list' },
  style: 'display: table-row; vertical-align: bottom;' }

  .ui.text.menu{ style: 'margin-bottom: 0;' }
    .header.item{ style: 'font-size: 1em; text-transform: none;' }
      Journal Entries

    - if multi_basis?
      .right.menu{ style: 'font-size: 0.9em;' }
        = link_to 'Accrual',
          '#',
          class: 'active item',
          data: { action: 'click->accounting--commercial-document--journal-entries-list#showAccrualBasis',
          accounting__commercial_document__journal_entries_list_target: 'accrualButton' }

        = link_to 'Cash',
          '#',
          class: 'item',
          data: { action: 'click->accounting--commercial-document--journal-entries-list#showCashBasis',
          accounting__commercial_document__journal_entries_list_target: 'cashButton' }

  .ui.divider{ style: 'margin-top: 0;' }

  - if multi_basis?
    .ui.list{ style: 'margin-bottom: 0;',
      data: { accounting__commercial_document__journal_entries_list_target: 'cashEntries' } }
      = render EntryComponent.with_collection cash_journal_entries

  .ui.list{ style: 'margin-bottom: 0;',
    data: { accounting__commercial_document__journal_entries_list_target: 'accrualEntries' } }
    = render EntryComponent.with_collection accrual_journal_entries
