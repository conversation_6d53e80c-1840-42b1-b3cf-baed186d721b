= form_with id: :bank_account_form,
  model: [:organization, @bank_account],
  local: false,
  class: 'ui form',
  data: { controller: 'accounting--bank-account-form' } do |f|

  .two.required.fields
    .field
      = f.label :name
      = f.text_field :name,
        placeholder: 'e.g. Operating Account, Payroll, etc.'

    .field{ class: class_names(disabled: owner_disabled?) }
      = f.label :owner_id, 'Owner'
      = f.semantic_dropdown :owner_id,
        owner_options,
        {},
        class: 'ui search selection dropdown',
        data: { options: { placeholder: 'Select' },
        accounting__bank_account_form_target: 'ownerDropdown' }

  .three.required.fields
    .field{ class: class_names(disabled: persisted_tunisia_account?) }
      = f.label :account_type
      = f.semantic_dropdown :account_type,
        account_type_options,
        {},
        class: 'ui search selection dropdown'

    .field
      = f.label :designation
      = f.semantic_dropdown :designation,
        designation_options,
        {},
        class: 'ui search selection dropdown',
        data: { options: { placeholder: 'Select' } }

    .field{ class: class_names(disabled: ledger_account_disabled?) }
      = f.label :ledger_account_id, 'Ledger Account', style: 'display: inline-block;'
      %i.ui.icon.info.circle{ data: { content: 'Only ledger accounts not in use by another bank account will be available for selection.', variation: 'very wide' }, style: 'color: gray;' }
      = f.semantic_dropdown :ledger_account_id,
        ledger_account_options,
        {},
        class: 'ui search selection dropdown',
        data: { options: { placeholder: 'Select' } }

  .three.two.fields
    .field{ class: class_names(disabled: persisted_tunisia_account?) }
      = f.label :account_number
      = f.text_field :account_number, value: account_number_display

    .field{ class: class_names(disabled: persisted_tunisia_account?) }
      = f.label :routing_number
      = f.text_field :routing_number

    .field
      = f.label :beginning_check_number
      = f.number_field :beginning_check_number,
        placeholder: BankAccount::DEFAULT_BEGINNING_CHECK_NUMBER

  .ui.error.message

  .clearfix
    = f.submit 'Save', class: 'ui primary submit button'



:javascript
  $('.info.circle.icon').popup();
