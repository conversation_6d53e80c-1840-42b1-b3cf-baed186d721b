class Inspections::ReviewStatisticsComponent < ApplicationComponent
  def initialize(inspection:)
    @inspection = inspection
    super()
  end

  private

  attr_reader :inspection

  def total_questions
    inspection.records.sum do |record|
      if record.property
        template_property_questions_count
      elsif record.unit
        template_unit_questions_count +
          (template_bedroom_questions_count * record.bedroom_count) +
          (template_bathroom_questions_count * record.bathroom_count)
      else
        0
      end
    end
  end

  def responded
    @responded ||= inspection.responses.count
  end

  def skipped
    total_questions - responded
  end

  def completed_percentage
    return 0 unless total_questions.positive?

    (responded / total_questions.to_f) * 100
  end

  # TODO: Unarchived questions, rooms

  def template_property_questions_count
    @template_property_questions_count ||= questions.property.count
  end

  def template_unit_questions_count
    @template_unit_questions_count ||= questions.unit.count
  end

  def template_bedroom_questions_count
    @template_bedroom_questions_count ||= questions.bedroom.count
  end

  def template_bathroom_questions_count
    @template_bathroom_questions_count ||= questions.bathroom.count
  end

  delegate :template, to: :inspection
  delegate :questions, to: :template
end
