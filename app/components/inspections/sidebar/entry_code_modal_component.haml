= render ModalComponent.new id: :entry_code_modal,
  title: title,
  options: { size: :tiny, 
             autofocus: false, 
             form: :entry_code_form, 
             submit: { text: 'Submit' } } do

  = form_with model: @inspection,
    id: :entry_code_form,
    url: operations_inspection_path(@inspection),
    method: :patch,
    local: false,
    class: 'ui form' do |f|

    .field
      = f.label :entry_code, 'Entry Code'
      = f.text_field :entry_code,
        value: @inspection.entry_code,
        placeholder: 'Enter entry code',
        class: 'ui input'

    .ui.error.message
