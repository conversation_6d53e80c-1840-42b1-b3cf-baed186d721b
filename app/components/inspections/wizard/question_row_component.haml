%tbody{ data: { question_id: question.id } }
  %tr{ class: table_class, data: row_data }
    %td.five.wide= question.prompt

    - if response&.body&.present?
      %td.eleven.wide= simple_format(response.body)
    - else
      %td.eleven.wide{ style: 'color: rgba(40,40,40,.3);' }
        %i No Response

  -# Note
  - if response&.note&.present?
    %tr{ data: row_data }
      %td.five.wide.right.aligned.disabled
        Note
      %td.eleven.wide
        = response.note

  -# Punch List
  - if response&.punch_list_entries&.any?
    %tr{ data: row_data }
      %td.five.wide.right.aligned.disabled
        Punch List
      %td.eleven.wide
        - response&.punch_list_entries&.each do |entry|
          #{entry.count} x #{entry.description}
          %br

  -# Photos
  - if response&.photos&.any?
    %tr{ data: row_data }
      %td.five.wide.right.aligned.disabled
        Photos
      %td.eleven.wide
        .ui.tiny.images
          - response.photos.each do |photo|
            = link_to photo.upload_url, class: 'ui centered rounded bordered image image-link', target: '_blank' do
              %img{ src: photo.upload_url(:small), style: 'width: 80px; height: 80px; object-fit: cover;' }
