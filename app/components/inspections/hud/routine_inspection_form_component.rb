class Inspections::HUD::RoutineInspectionFormComponent < ApplicationComponent
  def initialize(inspection:)
    super()
    @inspection = inspection
    @order = inspection.reams_order
    @record = inspection.records.sole
  end

  def render_in(view_context, &)
    super
  rescue StandardError => e
    Honeybadger.notify(e)
    '<div class="ui printable paper">Form could not be displayed.</div>'.html_safe
  end

  private

  attr_reader :inspection, :order, :record

  delegate :responses, to: :record

  def photos
    responses.includes(:photos).flat_map(&:photos)
  end

  def categorized_responses
    @categorized_responses ||= responses.group_by do |response|
      section = response.question.section

      case section
      when /exterior/i
        'Exterior'
      when /interior/i
        'Interior'
      when /health/i
        'Health'
      else
        section
      end
    end
  end

  def zipped_responses
    interior = interior_responses
    exterior = exterior_responses
    max_length = [interior.length, exterior.length].max

    interior.fill(nil, interior.length...max_length)
    exterior.fill(nil, exterior.length...max_length)

    exterior.zip(interior)
  end

  def exterior_responses
    categorized_responses.fetch('Exterior', [])
  end

  def interior_responses
    categorized_responses.fetch('Interior', [])
  end

  def health_and_safety_responses
    categorized_responses.fetch('Health', [])
  end

  def response_body_by_question_filter(filter:)
    responses.find_by(question: filter)&.body
  end

  def date_of_inspection
    order.inspection_on
  end

  def date_of_last_inspection
    order.last_inspection_on
  end

  def fha_case_number
    order.case_number
  end

  def address
    inspection.property.address
  end

  def occupied
    reams_id = 'isVacant' # Indeed, isVacant is actually isOccupied

    filter = Inspection::Question.where(reams_id:)

    body = response_body_by_question_filter(filter:)

    case body
    when /y/i
      'Yes'
    when /n/i
      'No'
    else
      'Unspecified'
    end
  end

  def signature_name
    filter = Inspection::Question.where('prompt ILIKE ?', '%inspector name%')

    response_body_by_question_filter(filter:) || 'Unspecified'
  end

  def signature_date
    filter = Inspection::Question.where('prompt ILIKE ?', '%inspection date%')

    body = response_body_by_question_filter(filter:).presence || 'Unspecified'

    Date.parse(body).strftime('%m/%d/%Y')
  rescue StandardError
    body
  end
end
