- additional_notes = {}

.ui.printable.paper#hud-routine-inspection-form
  %table.ui.fixed.celled.table
    %thead
      %tr
        %th.center.aligned{ colspan: 4 }
          Routine Inspection Form

      %tr
        %th.center.aligned{ colspan: 4 }
          Field Service Manager (FSM) Property Inspection Report

    %tbody
      %tr
        %td
          %span.ui.small.text
            %b 1.
            Inspection Type:
          %p
            Routine

        %td
          %span.ui.small.text
            %b 2.
            Date of Inspection:
          %p
            = date_of_inspection&.to_fs(:short_date)

        %td
          %span.ui.small.text
            %b 3.
            Date of Last Inspection:
          %p
            = date_of_last_inspection&.to_fs(:short_date)

        %td
          %span.ui.small.text
            %b 5.
            FHA Case No.
          %p
            = fha_case_number

      %tr
        %td{ colspan: 3 }
          %span.ui.small.text
            %b 5.
            Property Address:
          %p
            = address.simple_address

        %td
          %span.ui.small.text
            %b 6.
            Occupied:
          %p
            = occupied

      %tr
        %td.center.aligned{ colspan: 4 }
          %b
            7. Inspection Report

      %tr
        %td.center.aligned{ colspan: 2 }
          %b
            I. EXTERIOR
        %td.center.aligned{ colspan: 2 }
          %b
            II. INTERIOR

      - zipped_responses.each_with_index do |(left, right), index|
        - bullet_letter = ('a'.ord + index).chr + '.'

        - if left&.note.present?
          - additional_notes["7.I.#{bullet_letter}"] = left.note

        - if right&.note.present?
          - additional_notes["7.II.#{bullet_letter}"] = right.note

        %tr
          %td{ colspan: 2, valign: 'top' }
            - if left
              %span.ui.small.text
                %b= bullet_letter
                = left.question.prompt
              %br
              = left.body

          %td{ colspan: 2, valign: 'top' }
            - if right
              %span.ui.small.text
                %b= bullet_letter
                = right.question.prompt
              %br
              = right.body

      %tr
        %td{ colspan: 4 }
          %strong
            9A. Health & Safety Site Hazards
            (Any condition or situation at the property that exposes the government to abnormal risk,
            that presents a source of danger, that could cause an accident,
            or poses the threat of injury, harm to the public or property), such as:

      %tr
        %td{ colspan: 1, style: 'background-color: #eee;' }

        %td{ colspan: 1 }
          Yes / No

        %td{ colspan: 2 }
          If "Yes" list location ↓

      - health_and_safety_responses.each do |response|
        %tr
          %td{ colspan: 1 }
            = response.question.prompt

          %td{ colspan: 1 }
            = response.body

          %td{ colspan: 2 }
            = response.note
      %tr
        %td{ colspan: 4 }
          %span.ui.small.text
            %b 10.
            Sign & Date:

      %tr
        %td{ colspan: 2}
          %span.ui.small.text
            Inspector Name/Signature:
          %br
          = signature_name
        %td{ colspan: 2 }
          %span.ui.small.text
            Date:
          %br
          = signature_date

      %tr
        %td{ colspan: 1 }
          %span.ui.small.text
            Inspection report item #
            %br
            (Example: Item #7a)
        %td{ colspan: 3 }
          %span.ui.small.text
            Comments and/or additional details
            %br
            (Photos must have description of the item & item # identified on the front bottom & back)

      - additional_notes.each do |section, note|
        %tr
          %td{ colspan: 1 }
            %span.ui.small.text
              = "Item " + section
          %td{ colspan: 3 }
            = note

  .ui.small.images
    - photos.each do |photo|
      %img.ui.image{ src: photo.upload(:medium).url }

:css
  #hud-routine-inspection-form .ui.small.text {
    font-size: 0.85em;
  }

  #hud-routine-inspection-form .ui.small.images .ui.image {
    width: 240px;
  }
