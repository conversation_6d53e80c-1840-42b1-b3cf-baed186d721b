- if rooms.any?
  - rooms.each do |room|
    = render Portfolio::Rooms::ModalComponent.new(parent: parent, room: room)

  = ActionTable::Rooms.call(self, rooms)

- else
  .ui.center.aligned.placeholder.segment
    .ui.header
      - if common_space_language?
        Common Spaces Add Detail To Properties
      - else
        Rooms Add Detail To Units

    %p
      - if common_space_language?
        Common spaces
      - else
        Rooms
      can be used to add more detail and tracking to maintenance and evaluations.

    - helpers.require_permission :create_portfolio_units? do
      = link_to 'Add Room',
        '#',
        class: 'ui button',
        data: { modal: :add_room_modal }

= render Portfolio::Rooms::ModalComponent.new(parent: parent)
