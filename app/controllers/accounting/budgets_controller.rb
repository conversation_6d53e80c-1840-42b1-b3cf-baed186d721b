class Accounting::BudgetsController < AccountingController
  def index
    @noport = true
    @budgets = budgets.joins(:company).merge(Company.reorder(name: :asc))
  end

  def show
    @noport = true
    @budget = budgets.find(params[:id])
  end

  def new
    @budget = Budget.new(
      year: Time.zone.now.year + 1,
      company_id: params[:company_id]
    )

    respond_to do |format|
      format.html
      format.js do
        render 'new', format: :js, layout: nil
      end
    end
  end

  def create
    @budget = Budget::Create.new(params, current_property_manager).call

    if @budget.persisted? && @budget.errors.none?
      flash[:success] = 'Budget Created Successfully'
      redirect_to accounting_budget_path(@budget)
    else
      flash[:error] = @budget.errors.full_messages
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    @budget = budgets.find(params[:id])
  end

  def update
    @budget = Budget::Create.new(params, current_property_manager).call

    if @budget.persisted? && @budget.errors.none?
      flash[:success] = 'Budget Updated Successfully'
      redirect_to accounting_budget_path(@budget)
    else
      flash[:error] = @budget.errors.full_messages
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    budgets.find(params[:id]).destroy!

    flash[:success] = 'Budget Deleted Successfully'

    redirect_to accounting_budgets_path
  end

  def template
    company = Company.find_by(id: params[:company_id]) || Company.first
    year = params[:year].presence&.to_i || Time.zone.today.year
    workbook = Budget::Template.call(journal: company, year: year)
    send_data workbook.stream.string,
              type: :xlsx,
              filename: 'revela_budget_template.xlsx'
  end

  private

  def budgets
    BudgetsQuery.for_user(current_property_manager)
  end
end
