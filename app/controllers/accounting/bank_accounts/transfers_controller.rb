class Accounting::BankAccounts::TransfersController < AccountingController
  before_action :set_bank_account
  before_action :set_deposit_bank_account_from_form, only: :create
  before_action :set_deposit_bank_account_from_url, only: :message

  def new; end

  def create
    unless @deposit_bank_account
      return render_ujs_errors 'Destination bank account cannot be blank'
    end

    result = create_journal_entries

    if result.successful?
      flash[:success] = if result.entries.one?
                          'Entry Created Successfully'
                        else
                          'Entries Created Successfully'
                        end

      entry = result.entries.first
      redirect_to accounting_journal_entry_path(entry.journal, entry)
    else
      render_ujs_errors result.errors
    end
  end

  private

  def set_bank_account
    @bank_account = BankAccount.find(params[:bank_account_id])
  end

  def set_deposit_bank_account_from_form
    @deposit_bank_account = BankAccount.find_by(
      id: params.dig(:transfer, :deposit_bank_account_id)
    )
  end

  def set_deposit_bank_account_from_url
    @deposit_bank_account = BankAccount.find_by(
      id: params[:deposit_bank_account_id]
    )
  end

  def amount
    Monetize.parse(params.dig(:transfer, :amount))
  end

  def description
    params.dig(:transfer, :description).presence || 'Transfer'
  end

  def date
    params.dig(:transfer, :date)
  end

  def create_journal_entries
    Accounting::BankAccount::Transfer.call(
      withdraw_account: @bank_account,
      deposit_account: @deposit_bank_account,
      amount: amount,
      date: date,
      description: description
    )
  end
end
