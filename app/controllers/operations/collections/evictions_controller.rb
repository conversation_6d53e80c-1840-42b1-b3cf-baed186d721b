class Operations::Collections::EvictionsController < OperationsController
  before_action :set_lease, only: %i[new create]
  before_action :set_eviction, except: %i[new create]

  helper_method :outcome_options

  def show
    @noport = true
    @defer_flash_notices = true
  end

  def new
    overdue_balance = @lease.chain.aging_delinquency.overdue

    @eviction = @lease.evictions.build(overdue_balance: overdue_balance)
  end

  def create
    @eviction = @lease.evictions.create!(eviction_params) do |eviction|
      eviction.created_by = current_property_manager
    end

    # Eviction status changed
    Accounting::Ledger::Persisted.refresh!

    flash[:success] = 'Eviction Created Successfully'

    redirect_to operations_collections_eviction_path(@eviction)
  rescue ActiveRecord::RecordInvalid => e
    render_ujs_errors e.record.errors.full_messages
  end

  def update
    if @eviction.update(eviction_params)
      flash[:success] = 'Eviction Updated Successfully'
      redirect_to operations_collections_eviction_path(@eviction)
    else
      render_ujs_errors @eviction.errors.full_messages
    end
  end

  def reopen
    @eviction.reopen!

    flash[:success] = 'Eviction Reopened Successfully'

    redirect_to operations_collections_eviction_path(@eviction)
  end

  private

  def set_lease
    @lease = Lease.find(params[:lease_id])
  end

  def set_eviction
    @eviction = Collections::Eviction.find(params[:id])
  end

  def eviction_params
    params.require(:eviction).permit(
      :date_filed, :overdue_balance,
      :court_id, :court_date, :anticipated_date,
      :notes, :outcome, :close_date
    )
  end

  def outcome_options
    [
      ['Select', nil],
      ['Stay - Paid in Full', 'paid_in_full'],
      ['Stay - Payment Plan', 'payment_plan'],
      ['Vacated Prior to Eviction', 'vacated'],
      ['Evicted', 'evicted']
    ]
  end
end
