class Operations::InspectionQuestionsController < OperationsController
  before_action :set_template, :set_questions

  def index
    render :index, layout: nil
  end

  def create
    @question = @questions.create!(inspection_question_params)
    head :created
  end

  private

  def set_template
    @template = Inspection::Template.find(params[:inspection_template_id])
  end

  def set_questions
    @questions = @template.questions.where(category: params[:category])
  end

  def inspection_question_params
    params.require(:inspection_question)
          .permit(:prompt, :kind, :category, :section)
  end
end
