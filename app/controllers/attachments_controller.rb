class AttachmentsController < ManagementController
  include Api::V1::S3SignedUpload

  def dummy
    # to have a named controller action that will actually be handled by
    # cloudflare redeem_attachment worker ( /cf/redeem_attachment/* )
    fail NotImplementedError
  end

  def create
    parent = find_parent

    attachment = if parent
                   parent.attachments.build(attachment_params)
                 else
                   Photo.new(attachment_params)
                 end

    if attachment.save
      render json: attachment
    else
      render json: attachment.errors, status: :unprocessable_entity
    end
  end

  def destroy
    parent = find_parent

    attachment = parent.attachments.find(params[:id])

    if attachment.destroy
      if parent.is_a?(Plutus::Entry)
        redirect_back fallback_location: root_path
      else
        head :no_content
      end
    else
      render json: attachment.errors, status: :unprocessable_entity
    end
  end

  protected

  def bucket_name
    'revela-uploads'
  end

  def resource_type
    find_parent.class.name.underscore
  end

  def resource_id
    find_parent.id
  end

  private

  def find_parent
    @parent ||= GlobalID::Locator.locate_signed(params[:attachable_sgid])
  end

  def attachment_params
    params.require(:attachment).permit(:direct_upload_url, :upload_content_type,
                                       :upload_file_name, :upload_file_size)
  end
end
