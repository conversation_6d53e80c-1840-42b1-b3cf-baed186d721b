class Apply::EmergencyContactsController < ApplicationController
  include HostedApplicationController

  def show
    return if @lease_application.emergency_contacts.any?

    @lease_application.emergency_contacts.build
  end

  def update
    if @lease_application.update(emergency_contacts_params)
      redirect_to hosted_application_vehicles_path(
        hosted_application_uuid: @lease_application.uuid
      )
    else
      flash.now[:error] = @lease_application.errors.full_messages
      render :show, status: :unprocessable_entity
    end
  end

  private

  def emergency_contacts_params
    params.fetch(:lease_application, {}).permit(
      emergency_contacts_attributes: %i[
        id first_name last_name email phone relation _destroy
      ]
    )
  end
end
