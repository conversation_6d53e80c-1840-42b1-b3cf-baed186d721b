class Apply::IncomeSourcesController < ApplicationController
  include HostedApplicationController

  before_action :set_applicant

  def create
    @income_source = income_sources.build(income_source_params)
    @income_source.applicant_id = @applicant.id

    if @income_source.save
      respond_to do |format|
        format.js { render :update_table }
      end
    else
      respond_to do |format|
        format.js do
          render_ujs_errors @income_source.errors.full_messages
        end
      end
    end
  end

  def update
    @income_source = income_sources.find(params[:id])

    if @income_source.update(income_source_params)
      respond_to do |format|
        format.js { render :update_table }
      end
    else
      respond_to do |format|
        format.js do
          render_ujs_errors @income_source.errors.full_messages
        end
      end
    end
  end

  def destroy
    income_source = income_sources.find(params[:id])
    income_source.destroy!

    respond_to do |format|
      format.js { render :update_table }
    end
  end

  private

  def set_applicant
    @applicant = @lease_application.applicants[applicant_id]
  end

  def applicant_id
    params[:applicant_id].to_i - 1
  end

  def income_source_params
    params.require(:income_source)
          .permit(:type, :name, :monthly_income, :start_date, :end_date,
                  :occupation, :supervisor, :phone,
                  address_attributes: Address::PERMISSABLE_ATTRIBUTES)
  end

  delegate :income_sources, to: :@lease_application
end
