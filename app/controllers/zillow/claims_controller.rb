class Zillow::ClaimsController < ApplicationController
  # TODO: Move to Management or Marketing

  def search
    results = Zillow::Claims::DropdownOptions.new(
      query_line_one: params[:line_one],
      listing_id: params[:listing_id]
    ).call

    render json: {
      success: true,
      results: results
    }
  end

  def edit
    @zillow_claim = Zillow::Claim.find(params[:id])
  end

  def create
    @zillow_claim = Zillow::Claim.create!(zillow_claim_params)
    flash.now[:success] = 'Zillow Address and Details Added'
    render json: @zillow_claim.as_json(methods: :dropdown_option)
                              .merge(address_attributes: @zillow_claim.address.attributes)
  rescue ActiveRecord::RecordInvalid => e
    render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity
  end

  def update
    @zillow_claim = Zillow::Claim.find(params[:id])
    @zillow_claim.update!(zillow_claim_params)
    flash[:success] = 'Zillow Address and Details Updated'
    redirect_to marketing_listings_path
  rescue ActiveRecord::RecordInvalid => e
    render_ujs_errors e.record.errors.full_messages
  end

  def zillow_claim_params
    params.require(:zillow_claim)
          .permit(:grouping, :hotpads_unit_number, :hotpads_property_type, :zrm_property_id,
                  :property_ids, :agent_id, :show_address,
                  address_attributes: Address::PERMISSABLE_ATTRIBUTES,
                  syndication_property_supplement_attributes: [
                    :id, {
                      allowed_pets: [], parking_type: [], laundry: [],
                      cooling_system: [], heating_system: [], heating_fuel: []
                    }
                  ]).tap do |p|
      next unless p[:property_ids].present?

      p[:property_ids] = p[:property_ids].split(',').map(&:strip).compact
    end
  end
end
