class Reports::EmailSchedulesController < ManagementController
  def index
    @noport = true
    @defer_flash_notices = true
    @schedules = Reports::EmailSchedulesQuery
                 .new.search
                 .order(created_at: :desc)
                 .filter_with(params[:filters])
                 .max_paginated(params[:page], 12)
  end

  def sample
    schedule = Reports::EmailSchedule.find(params[:id])

    email = current_property_manager.email

    Reports::SendEmailScheduleSampleJob.perform_later(schedule, email: email)

    flash[:success] = "Sample Sent to #{email}"

    redirect_back(fallback_location: reports_email_schedules_path)
  end

  def batch_destroy
    schedules = Reports::EmailSchedule.where(id: params[:selected_ids])

    count = schedules.count

    ActiveRecord::Base.transaction do
      schedules.each(&:destroy!)
    end

    description = "#{count} #{'Schedule'.pluralize(count)}"

    flash[:success] = "#{description} Deleted Successfully"

    redirect_to reports_email_schedules_path
  end

  def recipients
    employees = EmployeesQuery.for_user(current_property_manager)

    employee_emails = employees.all.map do |employee|
      {
        name: "#{employee.name} &lt;#{employee.email}&gt;",
        value: employee.email
      }
    end

    render json: { results: employee_emails }
  end

  def create
    # Look at filters from the url this form was submitted from
    referer_uri = URI.parse(request.referer)
    query = Rack::Utils.parse_nested_query(referer_uri.query)
    filters = query['filters'] || {}

    email_schedule = Reports::EmailSchedule.new(email_schedule_params)
    email_schedule.filters = filters
    email_schedule.created_by = current_property_manager

    if params[:send_sample]
      Reports::EmailSchedule::SendSample.call(email_schedule)

      head :no_content
    elsif email_schedule.save
      flash[:success] = 'Email Schedule Created Successfully'
      redirect_to reports_path
    else
      respond_to do |format|
        format.js do
          render_ujs_errors email_schedule.errors.full_messages
        end
      end
    end
  end

  private

  def email_schedule_params
    params
      .require(:email_schedule)
      .permit(
        :slug,
        :subject, :body,
        :frequency, :start_date,
        :date_range,
        :pdf_format, :xlsx_format,
        :skip_empty,
        recipients: []
      )
  end
end
