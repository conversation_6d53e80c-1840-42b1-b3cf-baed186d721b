class Reports::PacketTemplatesController < ManagementController
  # TODO: Remove some of these as they are replaced with V3 versions.
  UNAVAILABLE_REPORTS = %w[
    ledger
    occupancy
    rent-schedule
  ].freeze

  before_action :set_report_slugs, only: %i[new filters edit]
  before_action :set_packet_template, only: %i[edit update destroy]

  helper_method :report_user

  def index
    @noport = true
    @defer_flash_notices = true
    @packet_templates = \
      Reports::PacketTemplate.all.order(created_at: :desc).decorate
  end

  def new
    @packet_template = Reports::PacketTemplate.new
    @packet_template.entries.build
  end

  def filters
    render :filters, format: :js
  end

  def create
    @packet_template = Reports::PacketTemplate.create!(
      packet_template_params
    )

    flash[:success] = 'Report Packet Template Created Successfully'

    redirect_to reports_packet_templates_path
  rescue ActiveRecord::RecordInvalid => e
    render_ujs_errors e.record.errors.full_messages
  end

  def edit; end

  def update
    @packet_template.update!(packet_template_params)
    flash[:success] = 'Report Packet Template Updated Successfully'
    redirect_to reports_packet_templates_path
  rescue ActiveRecord::RecordInvalid => e
    render_ujs_errors e.record.errors.full_messages
  end

  def destroy
    @packet_template.destroy!
    flash[:success] = 'Report Packet Template Deleted Successfully'
    redirect_to reports_packet_templates_path
  end

  private

  def set_report_slugs
    catalog = Reports::Catalog.new(user: current_property_manager)

    @report_slugs = catalog.available_reports.pluck(:slug).sort.excluding(
      UNAVAILABLE_REPORTS
    )
  end

  def set_packet_template
    @packet_template = Reports::PacketTemplate.find(params[:id])
  end

  def packet_template_params
    entries_attributes = [:id, :_destroy, :slug, filters: {}]

    params
      .require(:packet_template)
      .permit(:name, :format, entries_attributes: entries_attributes)
  end

  def report_user
    current_property_manager
  end
end
