class Owners::InvoiceApprovalsController < OwnerController
  skip_before_action :redirect_ccp_invoice_approvals

  # TODO: Test this page
  def index
    @needs_approval = \
      Approvals::Query
      .invoice_payment_needs_approval_by(current_owner)
      .includes(*includes)
      .order(date: :asc)

    @previously_approved = \
      Approvals::Query
      .invoice_payment_previously_approved_by(current_owner)
      .includes(*includes)
      .order(invoice_payment_approved_at: :desc)
      .limit(25)
  end

  def show
    @invoice = Invoice.find(params[:id])
  end

  def create
    invoices = Invoice.where(id: params[:invoice_ids])

    result = Approvals::BulkApprove.call(
      invoices,
      :invoice_payment,
      current_owner
    )

    if result.successful?
      flash[:success] = Approvals::BulkApprove.message(result, 'Invoice')
    else
      flash[:error] = result.errors
    end

    redirect_to owners_invoice_approvals_path
  end

  private

  def includes
    %i[buyer seller]
  end
end
