class Owners::ReportsController < OwnerController
  before_action :set_date_clamping

  include Reports::V3::FilterContext
  include Reports::V3::ReportContext

  rescue_from ActiveRecord::RecordNotFound, with: :no_report

  helper_method :report_user, :owner_public_reports

  skip_before_action :redirect_ccp_invoice_approvals

  def index; end

  def show
    @min_date = @date_clamping.minimum_date
    @max_date = @date_clamping.maximum_date

    super
  end

  protected

  def report_class
    klass = super

    unless klass.in?(owner_public_reports)
      fail ActiveRecord::RecordNotFound, 'Report Not Found'
    end

    klass
  end

  def report_user
    current_owner
  end

  def scope
    OpenStruct.new(describe: 'All Properties',
                   properties: current_owner.properties)
  end

  private

  def owner_public_reports
    if Customer.current_subdomain.in?(%w[marketplacehomes mph-sandbox])
      return [] if current_owner.id.in?([11_657, 11_656, 11_659])

      return [
        Reports::V3::OwnerStatement,
        Reports::V3::IncomeStatement,
        Reports::V3::SecurityDeposits
      ]
    elsif Customer.current_subdomain.start_with?('greenoak')
      return [
        Reports::V3::OwnerStatement,
        Reports::V3::IncomeStatement,
        Reports::V3::BalanceSheet,
        Reports::V3::ExpenseReport
      ]
    elsif Customer.current_subdomain.start_with?('evergreen')
      return [
        Reports::V3::OwnerStatement,
        Reports::V3::IncomeStatement,
        Reports::V3::RentRoll
      ]
    elsif Customer.current_subdomain.start_with?('rossrealty')
      return [
        Reports::V3::OwnerStatement,
        Reports::V3::IncomeStatement,
        Reports::V3::RentRoll,
        Reports::V3::SecurityDeposits
      ]
    elsif Customer.current_subdomain.start_with?('pmi', 'snowplace', 'crg')
      return [Reports::V3::OwnerStatement]
    elsif Customer.current_subdomain.start_with?('senihmgt')
      return [
        Reports::V3::OwnerStatement,
        Reports::V3::ExpenseReport,
        Reports::V3::RentRoll,
        Reports::V3::RentRollDetail
      ]
    elsif Customer.current_subdomain.start_with?('rest-assured')
      return [Reports::V3::ExpenseReport]
    elsif Customer.current_subdomain.start_with?('demo', 'alever')
      return [
        Reports::V3::BalanceSheet,
        Reports::V3::ExpenseReport,
        Reports::V3::GeneralLedger,
        Reports::V3::IncomeStatement,
        Reports::V3::OwnerStatement,
        Reports::V3::RentRoll,
        Reports::V3::RentRollDetail,
        Reports::V3::SecurityDeposits
      ]
    end

    if Customer.current_subdomain.start_with?('gebrael', 'ag-mgmt')
      if @date_clamping.completely_unlocked?
        return []
      else
        return [Reports::V3::OwnerStatement, Reports::V3::IncomeStatement]
      end
    end

    # TODO: Remove this?
    reports = Report.where(owner_visible: true).order(name: :asc).filter_map do |report|
      Reports::Catalog.report_class_from_slug(slug: report.name.parameterize)
    end

    if Customer.current_subdomain == 'mavenprops' && current_owner.id == 3
      reports += [Reports::V3::OwnerPortalTenantLedger]
    end

    reports
  end

  def no_report
    flash[:error] = 'The requested report could not be found.'
    redirect_to owners_reports_path
  end

  def set_date_clamping
    @date_clamping = Reports::OwnerDateClamping.new(owner: current_owner)
  end
end
