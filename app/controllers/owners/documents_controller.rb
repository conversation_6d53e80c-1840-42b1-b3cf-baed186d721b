class Owners::DocumentsController < OwnerController
  before_action :set_documents, only: :index

  def index; end

  def self.enabled?(current_owner)
    Rails.env.test? || Feature.enabled?(:public_owner_documents, Customer.current) ||
      (
        Customer.current_subdomain == 'mavenprops' &&
        current_owner.id == 3
      )
  end

  private

  def set_documents
    @documents = all_documents.sort_by(&:created_at).reverse
  end

  def all_documents
    return [] unless self.class.enabled?(current_owner)

    results = owner_documents + entity_documents + property_documents

    unless Customer.current_subdomain.start_with?('pmi')
      results += executed_lease_documents
    end

    results
  end

  def executed_lease_documents
    leases = LeasesQuery.for_user(current_owner)

    Document.template_options(
      template_type: :executed_lease,
    ).where(
      parent: leases,
    ).preload(
      parent: [unit: :property]
    )
  end

  def owner_documents
    current_owner.attachments
  end

  def entity_documents
    current_owner.companies.includes(:attachments).flat_map(&:attachments)
  end

  def property_documents
    current_owner.properties.includes(:attachments).flat_map(&:attachments)
  end
end
