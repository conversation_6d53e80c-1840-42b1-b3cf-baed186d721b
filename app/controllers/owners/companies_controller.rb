class Owners::CompaniesController < OwnerController
  before_action :set_company, except: :index

  def index
    @companies = current_owner.companies
  end

  def show
    @properties = @company.properties
  end

  def edit
    if @company.taxpayer_identification.blank?
      @company.build_taxpayer_identification
    end

    @company.build_address if @company.address.blank?
  end

  def update
    if @company.update(update_params)
      flash[:success] = 'Company Updated Successfully'
      redirect_to owners_company_path(@company)
    else
      flash.now[:error] = @company.errors.full_messages
      render :edit, status: :unprocessable_entity
    end
  end

  private

  def set_company
    @company = current_owner.companies.find(params[:id])
  end

  def update_params
    params.require(:company).permit(Company::PERMISSABLE_ATTRIBUTES)
  end
end
