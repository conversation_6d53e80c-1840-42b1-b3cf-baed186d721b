class Api::V1::FloorplansController < ApiController
  before_action :set_property, :set_floorplans

  def index
    render json: {
      floorplans: @floorplans.map do |floorplan|
        {
          id: floorplan.id,
          images: image_urls(floorplan),
          available_units: floorplan.available_units.map do |unit|
            {
              id: unit.id,
              name: unit.name,
              rent_cents: floorplan.price_cents
            }
          end,
          name: floorplan.name,
          square_feet: floorplan.square_feet,
          rent_cents: floorplan.price_cents,
          bedrooms: floorplan.bedrooms,
          bathrooms: floorplan.bathrooms
        }
      end
    }
  end

  private

  def set_property
    @property = Property.find(params[:property_id])
  end

  def set_floorplans
    @floorplans = @property.floorplans.includes(:available_units, :listing)
  end

  def image_urls(floorplan)
    return [] unless floorplan.listing

    floorplan.listing.photos.map(&:direct_upload_url)
  end
end
