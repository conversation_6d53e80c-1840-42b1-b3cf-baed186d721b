class Api::V1::ContactsController < ApiController
  include Api::V1::AuthenticatedRoute

  def index
    render json: contacts
  end

  private

  def contacts
    results.sort_by(&:name)
  end

  def results
    query.results.map(&:_source)
  end

  def query
    Elasticsearch::Model.search(
      {
        query: { match_all: {} },
        sort: '_doc',
        size: 10_000,
        from: 0
      },
      [<PERSON><PERSON>, Owner, PropertyManager, Vendor]
    )
  end
end
