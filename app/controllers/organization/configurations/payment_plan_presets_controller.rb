class Organization::Configurations::PaymentPlanPresetsController <
  OrganizationController
  layout 'configuration_setup'

  helper_method :configuration_path

  before_action :set_configuration
  before_action :set_payment_plan_preset, only: %i[edit update destroy]

  def index
    @payment_plan_presets = payment_plan_presets.decorate
  end

  def new
    @payment_plan_preset = PaymentPlan::Preset::Duration.new(
      configuration: @configuration, type: 'PaymentPlan::Preset::Duration'
    )

    2.times { @payment_plan_preset.installments.build }
  end

  def create
    type = params.dig(:payment_plan_preset, :type)

    klass = case type
            when 'PaymentPlan::Preset::Duration'
              PaymentPlan::Preset::Duration
            when 'PaymentPlan::Preset::Explicit'
              PaymentPlan::Preset::Explicit
            when 'PaymentPlan::Preset::Range'
              PaymentPlan::Preset::Range
            else
              fail "Unsupported payment plan preset type '#{type}'"
            end

    @payment_plan_preset = klass.create!(payment_plan_preset_params) do |preset|
      preset.configuration = @configuration
    end

    flash[:success] = 'Payment Plan Preset Created Successfully'

    redirect_to index_path
  rescue ActiveRecord::RecordInvalid => e
    render_ujs_errors e.record.errors.full_messages
  end

  def edit
    return unless @payment_plan_preset.is_a?(PaymentPlan::Preset::Explicit)

    return unless @payment_plan_preset.installments.empty?

    2.times { @payment_plan_preset.installments.build }
  end

  def update
    @payment_plan_preset.update!(payment_plan_preset_params)

    flash[:success] = 'Payment Plan Preset Updated Successfully'

    redirect_to index_path
  rescue ActiveRecord::RecordInvalid => e
    render_ujs_errors e.record.errors.full_messages
  end

  def destroy
    @payment_plan_preset.destroy!

    flash[:success] = 'Payment Plan Preset Removed Successfully'

    redirect_to index_path
  rescue ActiveRecord::RecordNotDestroyed => e
    render_ujs_errors e.record.errors.full_messages
  end

  private

  def configuration_path
    %i[organization configuration]
  end

  def index_path
    organization_configuration_payment_plan_presets_path(@configuration)
  end

  def set_configuration
    @configuration = ::Configuration.find(params[:configuration_id])
  end

  def payment_plan_presets
    @configuration.payment_plan_presets
  end

  def set_payment_plan_preset
    @payment_plan_preset = payment_plan_presets.find(params[:id])
  end

  def payment_plan_preset_params
    params.require(:payment_plan_preset).permit(
      :name, :type,
      :minimum_installment_count, :maximum_installment_count,
      :duration_days,
      :range_start_date, :range_end_date,
      :portal_visible,
      :date_available_start, :date_available_end,
      property_whitelist_ids: [], property_blacklist_ids: [],
      installments_attributes: %i[id date _destroy]
    )
  end
end
