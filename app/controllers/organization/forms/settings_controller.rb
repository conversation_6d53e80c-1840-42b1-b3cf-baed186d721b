class Organization::Forms::SettingsController < OrganizationController
  before_action :check_feature_flag

  def update
    @custom_form = CustomForms::Form.find(params[:form_id])
    setting_params = params.require(:setting)
    result = CustomForms::UpdateSetting.(form: @custom_form, setting_params: setting_params)

    if result.successful?
      redirect_to edit_organization_form_path(params[:form_id])
    else
      automation_klass = CustomForms::AutomationNameToClass.convert(
        params[:setting][:automation_name]
      )
      render automation_klass.editor_component(result.setting), status: :unprocessable_entity,
                                                                content_type: 'text/html'
    end
  end

  private

  def check_feature_flag
    return if Feature.enabled?(:custom_forms, Customer.current)

    redirect_to manage_companies_path, alert: 'Not Enabled' and return
  end
end
