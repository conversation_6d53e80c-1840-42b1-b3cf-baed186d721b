class LandingPageController < ApplicationController
  include LandingPage::UpdatedLandingPage
  include Management::AfterSignInPath

  before_action :redirect_managers
  before_action :redirect_to_login, only: :index, if: :customer_instance?

  def index; end

  def contact
    @contact = ContactForm.new
  end

  def submit_contact_form
    @contact = ContactForm.new(contact_form_params)

    if @contact.valid? && verify_recaptcha(model: @contact)
      ContactMailer.contact(
        @contact.full_name, @contact.email,
        @contact.phone,
        @contact.message,
        @contact.company_name,
        @contact.role,
        @contact.address&.simple_address,
        @contact.property_types
      ).deliver_later

      flash[:success] = 'Submitted Successfully'
    else
      flash.now[:error] = @contact.errors.full_messages
      render :contact, status: :unprocessable_entity
    end
  end

  def company; end

  def security; end

  def services; end

  def training; end

  def research; end

  def case_studies; end

  def forms; end

  def lead_qualifier; end

  private

  def public_instance?
    !customer_instance?
  end

  def customer_instance?
    Customer.current.present?
  end

  # If this is a customer instance, automatically redirect to the login page
  # unless they clicked home from the landing page itself
  def redirect_to_login
    referer = request.referer
    internal = referer&.include?(request.host)
    redirect_to new_property_manager_session_path unless internal
  end

  def redirect_managers
    return unless current_property_manager

    redirect_to after_property_manager_sign_in_path(current_property_manager)
  end

  def contact_form_params
    params.require(:contact_form)
          .permit(:first_name, :last_name, :email, :phone, :message,
                  :company_name, :role, address: {}, property_types: [])
  end
end
