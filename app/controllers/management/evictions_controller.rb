class Management::EvictionsController < ManagementController
  before_action :set_tenant, :set_lease, :set_overdue_balance

  def new; end

  def create
    klass = if params.dig(:eviction, :document_type) == 'demand_for_possession'
              Lease::DemandForPossession
            else
              Lease::NoticeToQuit
            end

    result = klass.call(
      params: params,
      lease: lease,
      tenant: tenant,
      user: current_property_manager
    )

    if result.successful?
      flash[:success] = 'Document Generated Successfully'
      redirect_to tenant_path(tenant)
    else
      flash[:error] = result.errors
      render :new
    end
  end

  private

  attr_reader :tenant, :lease

  def set_tenant
    @tenant = Tenant.find(params[:tenant_id])
  end

  def set_lease
    @lease = tenant.leases.unarchived.last
  end

  def set_overdue_balance
    @overdue_balance = if lease
                         lease.chain.aging_delinquency.overdue
                       else
                         Money.zero
                       end
  end
end
