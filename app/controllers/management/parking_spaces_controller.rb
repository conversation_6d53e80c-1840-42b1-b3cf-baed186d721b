class Management::ParkingSpacesController < ManagementController
  before_action :set_parking_lot, :set_parking_spaces

  def index
    respond_to do |format|
      format.html
      format.xlsx { index_xlsx }
    end
  end

  def index_xlsx
    exporter = ParkingSpacesExporter.new(parking_lot: @parking_lot)

    send_data exporter.workbook.stream.string,
              filename: exporter.filename,
              type: :xlsx
  end

  def import
    path = params[:upload].path

    result = ParkingSpacesImporter.import(
      path: path,
      parking_lot: @parking_lot
    )

    if result.successful?
      count = result.parking_spaces.count
      text = 'Parking Space'.pluralize(count)
      flash[:success] = "#{count} #{text} Imported Successfully"
      redirect_to parking_lot_spaces_path(@parking_lot)
    else
      flash.now[:error] = result.errors
      render :index, status: :unprocessable_entity
    end
  end

  def destroy
    parking_space = @parking_spaces.find(params[:id])

    if parking_space.destroy
      flash[:success] = 'Parking Space Removed Successfully'
    else
      flash[:error] = parking_space.errors.full_messages
    end

    redirect_to parking_lot_spaces_path(@parking_lot)
  end

  private

  def set_parking_lot
    @parking_lot = ParkingLot.find(params[:parking_lot_id])
  end

  def set_parking_spaces
    @parking_spaces = @parking_lot.parking_spaces
  end
end
