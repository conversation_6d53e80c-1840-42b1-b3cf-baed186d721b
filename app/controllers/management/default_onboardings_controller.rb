# TODO: remove controller and route when onboarding enhancements are fully rolled out
class Management::DefaultOnboardingsController < Management::PortfoliosController
  before_action :set_portfolio
  before_action :set_onboarding, :set_selected_properties, only: :create
  before_action :require_onboarding_enhancement_disabled

  def create
    ActiveRecord::Base.transaction do
      @selected_properties.each do |property|
        default_onboarding_assignment = property.default_member_onboarding_configuration_assignment
        if default_onboarding_assignment.present?
          default_onboarding_assignment.update!(configuration: @onboarding)
        else
          MemberOnboarding::PropertyMembership.create!(property: property,
                                                       configuration: @onboarding,
                                                       enhanced: false)
        end
      end
    end

    count = @selected_properties.count
    flash[:success] = "#{count} Default #{'Onboarding'.pluralize(count)} Set Successfully"
  rescue ActiveRecord::RecordInvalid => e
    flash[:error] = "Error setting default onboarding: #{e.record.errors.full_messages.join(', ')}"
  rescue StandardError => e
    flash[:error] = "An unexpected error occurred: #{e.message}"
  ensure
    redirect_to portfolio_path(@portfolio)
  end

  def destroy
    default_onboarding_memberships = MemberOnboarding::PropertyMembership.where(
      property_id: property_ids,
      enhanced: false
    )

    count = default_onboarding_memberships.length
    default_onboarding_memberships.destroy_all

    flash[:success] =
      "#{count} Default #{'Onboarding'.pluralize(count)} Removed Successfully"
    redirect_to portfolio_path(@portfolio)
  end

  private

  def require_onboarding_enhancement_disabled
    return unless Feature.enabled?(:onboarding_enhancements, Customer.current)

    Honeybadger.notify('DefaultOnboardingsController called with onboarding_enhancements flag')
    flash[:error] = 'This action is not available when onboarding enhancements are enabled.'
    redirect_back fallback_location: portfolio_path(@portfolio)
  end

  def property_ids
    @property_ids ||= params[:property_ids].split(',')
  end

  def set_portfolio
    @portfolio = Portfolio.find(params[:portfolio_id])
  end

  def set_onboarding
    @onboarding = MemberOnboarding::Configuration.find(params[:configuration_id])
  end

  def set_selected_properties
    @selected_properties = @portfolio.properties.where(id: property_ids)
                                     .includes(:default_member_onboarding_configuration_assignment)
  end
end
