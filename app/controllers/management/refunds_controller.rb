class Management::RefundsController < ManagementController
  include LedgerContextsController

  before_action :set_tenant, :set_ledger_context

  def new
    @credit_balance = - [@ledger_context.ledger.balance, Money.zero].min
  end

  def create
    result = Ledger::PrepareRefund.call(
      ledger: @ledger_context.ledger,
      amount: Monetize.parse(params.dig(:refund, :amount)),
      date: params.dig(:refund, :date),
      description: params.dig(:refund, :description),
      note: params.dig(:refund, :note)
    )

    if result.successful?
      flash[:success] = 'Refund Prepared Successfully'
      redirect_to accounting_payables_invoice_path(result.invoice)
    else
      render_ujs_errors result.errors
    end
  end

  private

  def set_tenant
    @tenant = Tenant.find(params[:tenant_id])
  end
end
