class Leasing::CommercialLeasesController < LeasingController
  def new
    @lease = Lease.new
    @lease.start_date = Time.zone.today.beginning_of_year
    @lease.end_date = Time.zone.today.end_of_year
    @lease.unit = Portfolio.find(params[:portfolio_id]).units.first
    @lease.build_parking_allocation
  end

  def create
    @lease = Lease.new(lease_params)
    @lease.kind = :rollover

    first_name = params.dig(:lease, :tenant)
    last_name = '(Commercial Parking)'
    email = params.dig(:lease, :email)&.strip&.downcase&.presence
    phone = params.dig(:lease, :phone)

    tenant = TenantsQuery.new.search.discover_existing(
      first_name: first_name,
      last_name: last_name,
      email: email,
      phone: phone
    ).first

    ActiveRecord::Base.transaction do
      tenant ||= Tenant.resident.create!(
        first_name: first_name,
        last_name: last_name,
        email: email,
        phone: phone
      )

      @lease.lease_memberships.build(tenant: tenant)

      @lease.save!

      flash[:success] = 'Lease Created Successfully'
      redirect_to leasing_lease_path(@lease)
    end
  rescue ActiveRecord::RecordInvalid => e
    flash.now[:error] = e.record.errors.full_messages
    render :new, status: :unprocessable_entity
  end

  private

  def lease_params
    params
      .require(:lease)
      .permit(:start_date, :end_date, :unit_id,
              parking_allocation_attributes: %i[
                included_count available_to_purchase_count
                space_price purchased_count
              ])
  end
end
