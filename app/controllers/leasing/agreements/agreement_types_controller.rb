class Leasing::Agreements::AgreementTypesController < LeasingController
  before_action :set_agreement_type, except: %i[index new create]

  def index; end

  def new
    @agreement_type = Agreements::AgreementType.new
  end

  def create
    agreement_type = Agreements::AgreementType.new(agreement_type_params)

    if agreement_type.save
      flash[:success] = 'Agreement Type Created Successfully'
    else
      flash[:error] = agreement_type.errors.full_messages
    end

    redirect_to leasing_agreement_types_path
  end

  def show; end

  def edit; end

  def update
    if @agreement_type.update(agreement_type_params)
      flash[:success] = 'Agreement Type Updated Successfully'
      redirect_to leasing_agreement_type_path(@agreement_type)
    else
      flash.now[:error] = @agreement_type.errors.full_messages
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    if @agreement_type.destroy
      flash[:success] = 'Agreement Type Deleted Successfully'
      redirect_to leasing_agreement_types_path
    else
      flash.now[:error] = @agreement_type.errors.full_messages
      render :show
    end
  end

  private

  def set_agreement_type
    @agreement_type = Agreements::AgreementType.find(params[:id])
  end

  def agreement_type_params
    params.require(:agreement_type).permit(:name, :document_template_id)
  end
end
