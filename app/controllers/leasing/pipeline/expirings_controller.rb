class Leasing::Pipeline::ExpiringsController < Leasing::PipelineController
  private

  def resource_name
    'Lease'
  end

  def set_collection
    @collection = expiring_leases
                  .order(end_date: :asc)
                  .includes(
                    :primary_tenant,
                    unit: [property: [company: :portfolio]]
                  )
                  .filter_with(params[:filters])
                  .order_with(params[:sort])
  end

  def expiring_count
    @collection.total_count
  end

  def table_klass
    ActionTable::Leasing::Pipeline::Expiring
  end
end
