class ReportsController < ManagementController
  before_action :set_reports
  before_action :set_report, only: %i[edit update]
  before_action :set_catalog

  def index
    @noport = true
  end

  def show
    @custom = %w[
      rent-roll expense tenant-ledger profit-and-loss
    ].include? params[:name]

    if @custom
      @component = if params[:name] == 'profit-and-loss'
                     'ProfitAndLoss'
                   else
                     "#{params[:name]}-report".tr('-', '_').classify
                   end
    else
      set_report
    end

    fail ActiveRecord::RecordNotFound unless @report

    @catalog.record_visit(params[:name])

    return show_sql_report if @report.type == 'Report::SQL'
  end

  def show_sql_report
    respond_to do |format|
      format.html do
        @noport = true
        @report = SQLReportDecorator.decorate(@report)
        render 'reports/sql_report/show'
      end

      format.xlsx do
        render xlsx: @report
      end
    end
  end

  def favorite
    @catalog.favorite(params[:name])
    render 'reports/index/update_favorites', format: :js
  end

  def unfavorite
    @catalog.unfavorite(params[:name])
    render 'reports/index/update_favorites', format: :js
  end

  private

  def set_reports
    @reports = ReportsQuery.for_user(current_property_manager)
  end

  def set_report
    slug = params[:name]
    @report = @reports.find { |report| report.slug == slug }
  end

  def set_catalog
    @catalog = Reports::Catalog.new(user: current_property_manager)
  end
end
