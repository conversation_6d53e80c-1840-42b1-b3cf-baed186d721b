# Common behavior between ApplicationController and ApiController
module <PERSON><PERSON><PERSON><PERSON>
  extend ActiveSupport::Concern

  include HoneybadgerUserContext
  include CustomerArchiving

  included do
    before_action :set_locale
    before_action :set_time_zone
    around_action :add_current_user_log_tag
  end

  private

  def set_locale
    I18n.locale = :en

    return if Rails.env.test? # use default locale in tests

    return if Customer.current.blank? # just use the default :en locale

    return unless Feature.enabled?(:customer_type_i18n, Customer.current)

    # Customer 'type' overrides begin here.
    # These all fallback to :en locale via config/application.rb
    if Customer.current.greek_housing?
      I18n.locale = :greek_housing
    elsif Customer.current.home_owners_association?
      I18n.locale = :home_owners_association
    end

    # Customer specific overrides begin here
    # these will fallback to :en by default via config.i18n.default_locale in config/application.rb
    # additional fallbacks can be specified via config.i18n.fallbacks in config/application.rb

    subdomain = Customer.current.subdomain

    # there is only one customer specific override right now but we will probably have more
    if subdomain.start_with?('pmi', 'pmi-sandbox') # rubocop:disable Style/IfUnlessModifier, Style/GuardClause
      I18n.locale = :pmi
    elsif subdomain.start_with?('adpi', 'adpi-sandbox', 'staging')
      I18n.locale = :adpi
    end
  end

  def set_time_zone
    Time.zone = Customer.current&.time_zone ||
                Rails.application.config.time_zone
  end

  def add_current_user_log_tag(&)
    if defined?(audited_user) && audited_user
      logger.tagged(audited_user.email, &)
    else
      yield
    end
  end
end
