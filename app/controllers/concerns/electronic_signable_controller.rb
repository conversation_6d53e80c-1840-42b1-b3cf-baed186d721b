module ElectronicSignableController
  extend ActiveSupport::Concern

  def destroy_document
    if electronic_signable.electronic_signature_request.destroyable?
      electronic_signable.electronic_signature_request.destroy!

      flash[:success] = 'Document Removed Successfully'
    else
      flash[:error] = 'Document Cannot be Removed'
    end

    redirect_to electronic_signable.url
  end

  def request_signatures
    result = ElectronicDocument::RequestSignatures.call(
      document: electronic_signable,
      primary_signers: primary_signers,
      user: current_property_manager,
      countersigner: countersigner
    )

    if result.successful?
      flash[:success] = 'Signatures Requested Successfully'
    else
      flash[:error] = result.errors
    end

    redirect_to electronic_signable.url
  end

  def cancel_signatures
    if electronic_signable.electronic_signature_request.cancelable?
      electronic_signable.electronic_signature_request.cancel!

      flash[:success] = 'Signature Request Cancelled'
    else
      flash[:error] = 'Signature Request Cannot Be Cancelled'
    end

    redirect_to electronic_signable.url
  end

  def resend_signature
    signature = electronic_signable.electronic_signatures.find(params[:signature_id])
    signature.resend_request

    redirect_to electronic_signable.url
  end

  private

  def electronic_signable
    fail NotImplementedError
  end

  def primary_signers
    fail NotImplementedError
  end

  def countersigner
    fail NotImplementedError
  end
end
