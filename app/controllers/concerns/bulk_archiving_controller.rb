module BulkArchivingController
  BULK_ARCHIVING_ACTIONS = %i[bulk_archive_modal bulk_archive bulk_unarchive].freeze

  def bulk_archive_modal
    @selected_ids = if params.dig(:selection, :overselection) == 'true'
                      archivables.pluck(:id)
                    else
                      params.dig(:selection, :ids).values
                    end

    @model_type = model_type

    update_bulk_selection_modal(
      :confirm_archive_modal,
      bulk_selection_partial_modal,
      @selected_ids,
      partial_locals: bulk_archive_partial_locals
    )
  end

  def bulk_archive
    archivable_batch = archivables.where(id: params[:selected_ids])

    ActiveRecord::Base.transaction do
      archivable_batch.each(&:archive!)
    end

    pluralized_model_type = model_type.pluralize(archivable_batch.count)
    flash[:toast_success] =
      "#{archivable_batch.count} #{pluralized_model_type} #{archive_terminology} Successfully"
  rescue Archivable::ArchivingError => e
    flash[:error] = e.message
  ensure
    redirect_back fallback_location: archivables_path
  end

  def bulk_unarchive
    archivable_batch = archivables.where(id: params[:selected_ids])

    ActiveRecord::Base.transaction do
      archivable_batch.each(&:unarchive!)
    end

    pluralized_model_type = model_type.pluralize(archivable_batch.count)
    flash[:toast_success] =
      "#{archivable_batch.count} #{pluralized_model_type} #{unarchive_terminology} Successfully"
  rescue Archivable::ArchivingError => e
    flash[:error] = e.message
  ensure
    redirect_back fallback_location: archivables_path
  end

  private

  def model_type
    archivables.model_name.human.titleize
  end

  def bulk_archive_partial_locals
    {
      bulk_archive_path: bulk_archive_path,
      additional_message: bulk_archive_additional_message
    }
  end

  def bulk_archive_additional_message
    nil
  end

  def bulk_selection_partial_modal
    'shared/confirm_bulk_archiving_modal'
  end

  def archive_terminology
    'Archived'
  end

  def unarchive_terminology
    'Unarchived'
  end
end
