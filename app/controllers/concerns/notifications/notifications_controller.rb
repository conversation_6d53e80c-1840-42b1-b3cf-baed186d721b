module Notifications::NotificationsController
  extend ActiveSupport::Concern

  def index
    @unread_count = notifications.nonmessage.unseen.count
    @notifications = notifications.page(params[:page]).per(8)
    @notification_preferences = notification_preferences
    render 'notifications/index'
  end

  def show
    @notification = notifications.find(params[:id])
    @notification.seen!
    redirect_to @notification.link
  end

  def mark_all_as_seen
    notifications.update_all(seen: true)
    redirect_to action: :index
  end

  def update_preferences
    notification_preferences.update!(notification_preferences_params)

    head :no_content
  end

  private

  def notifications
    NotificationsQuery
      .for_user(notifiable)
      .nonmessage
      .order(created_at: :desc)
  end

  def notification_preferences_params
    params
      .require(:notification_preferences)
      .permit(
        :email_preference,
        *Notification::Preferences::NOTIFICATION_TRIGGERS.keys
      )
  end

  def notifiable
    fail NotImplementedError
  end

  delegate :notification_preferences, to: :notifiable
end
