module DataImportController
  extend ActiveSupport::Concern

  included do
    helper_method :data_import_title,
                  :data_import_options_partial,
                  :data_import_class
  end

  def show
    last_import = DataImport.where(
      import_class_name: data_import_class.name
    ).last

    return if last_import.nil? || last_import.archived?

    @current_import = last_import
  end

  def create
    upload = params[:upload]

    options = params.fetch(:options, {}).reverse_merge(data_import_options)

    @data_import = current_property_manager.data_imports.create!(
      import_class_name: data_import_class.name,
      upload: upload,
      options: options
    )

    DataImportJob.perform_later(@data_import)

    @data_import.reload

    render 'shared/data_imports/start_import', format: :js, layout: nil
  end

  def template
    render xlsx: data_import_class.schema.template
  end

  def sample
    render xlsx: data_import_class.schema.sample
  end

  def archive
    import = DataImport.find(params[:id])
    import.archive!

    redirect_back fallback_location: organization_data_imports_path
  end

  protected

  def data_import_class
    fail NotImplementedError
  end

  def data_import_options
    {}
  end

  def data_import_title
    data_import_class.name.demodulize.titleize
  end

  def data_import_options_partial
    nil
  end
end
