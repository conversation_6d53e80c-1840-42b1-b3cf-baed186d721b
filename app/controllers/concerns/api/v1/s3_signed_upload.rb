module Api::V1::S3SignedUpload
  extend ActiveSupport::Concern

  def signed_upload_url
    filename = params[:filename]

    content_type = MIME::Types.type_for(filename).first&.content_type
    content_type ||= 'application/octet-stream'

    url = Aws::S3::Resource.new
                           .bucket(bucket_name)
                           .object(object_path(filename))
                           .presigned_url(:put, acl: 'public-read')

    fn = URI.parse(url).path.gsub(%r{^/}, '')

    render json: { url: url, content_type: content_type, filename: fn }
  end

  protected

  def resource_type
    'ungrouped'
  end

  def resource_id
    nil
  end

  private

  def object_path(filename)
    directory = 'development_uploads' if Rails.env.development?

    [
      directory,
      Customer.current.subdomain,
      resource_type,
      resource_id,
      SecureRandom.uuid,
      filename
    ].compact.join('/')
  end
end
