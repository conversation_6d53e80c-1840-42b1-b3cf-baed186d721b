class LandlordVerification::LandlordInformationsController <
  ApplicationController
  include HasLandlordVerification

  def show
    return unless @landlord_verification.landlord_information.nil?

    residence = @landlord_verification.lease_application_residence

    landlord_information = @landlord_verification.build_landlord_information
    landlord_information.landlord_email = residence.email
    landlord_information.landlord_name = residence.contact
    landlord_information.landlord_phone = residence.phone
  end

  def update
    if @landlord_verification.update(landlord_information_params)
      redirect_to landlord_verification_tenant_information_path(
        landlord_verification_uuid: @landlord_verification.uuid
      )
    else
      flash.now[:error] = @landlord_verification.errors.full_messages
      render :show, status: :unprocessable_entity
    end
  end

  def landlord_information_params
    params.fetch(:landlord_verification, {}).permit(
      landlord_information_attributes: %i[
        id landlord_name landlord_email landlord_phone
      ]
    )
  end
end
