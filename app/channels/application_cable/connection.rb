class ApplicationCable::Connection < ActionCable::Connection::Base
  identified_by :current_user, :tenant

  def connect
    self.tenant = CustomSubdomainElevator.new(nil).parse_tenant_name(request)

    Apartment::Tenant.switch!(tenant)
    self.current_user = find_current_user
    logger.add_tags tenant, current_user.email, 'ActionCable'
  end

  private

  def find_current_user
    user = case request.query_parameters['user_type']
           when 'Owner'
             env['warden'].user(:owner)
           when 'VendorContact'
             vendor_contact
           else
             env['warden'].user(:property_manager)
           end

    if user
      logger.info "Found user #{user.name}"
      user
    else
      logger.info "Didn't find user"
      reject_unauthorized_connection
    end
  end

  def vendor_contact
    decrypted_cookies = cookies.encrypted['_revela_session']

    if decrypted_cookies['masquerading_as_vendor_contact_id']
      return VendorContact.find_by(id: decrypted_cookies['masquerading_as_vendor_contact_id'])
    end

    User::Profile.find_by(id: decrypted_cookies['profile_id'])&.profile
  end
end
