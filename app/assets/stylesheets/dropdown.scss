// Allows semantic ui category selection dropdowns to have the same appearance
// as selection dropdowns
.ui.category.selection.dropdown {
  .menu {
    max-height: auto;
    overflow-x: visible;
    overflow-y: visible;

    .menu {
      border-top-width: 1px !important; // Return blue line to top of sub menu

      .item:first-child {
        border-top-width: 0px !important; // Prevent rectangle over border
      }
    }
  }
}

.ui.form .field input[type=text].no-or-explain {
  display: none;
  margin-top: 0.3em;
}
