@import 'color';

.action-grid {
  .tile-wrapper {
    margin: 1rem 0;

    .tile {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .image-wrapper {
        position: relative;
        display: inline-block;
        width: 100%;
        flex-grow: 1;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          margin-left: 0 !important;
          position: absolute;
          border: 1px solid #c1c1c1a1;
        }

        .hover-content {
          transition: opacity 0.2s ease-in-out;
          opacity: 0;
          position: absolute;
          top: 8px;
          z-index: 1;

          &.checkbox.checked {
            opacity: 1;
          }

          &.right {
            right: 8px;
          }

          &.left {
            left: 8px;
          }
        }

        &:hover {
          .hover-content {
            opacity: 1;
          }
        }

        .action-button {
          color: white;
          background: $link-gray;
          padding: 4px;
          width: 22px;
          height: 22px;
          border-radius: 4px;
        }
      }

      .info {
        font-family: 'Lato';
        font-weight: 600;

        .header {
          font-size: 14px;
        }

        .subheader {
          font-size: 12px;
          color: #595A5CCC;
        }

        .header, .subheader {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          text-align: left;
        }
      }
    }
  }
}
