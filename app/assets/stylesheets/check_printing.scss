@import 'color';

@font-face {
  font-family: 'MICR';
  src: url('/assets/micrenc.ttf') format('truetype');
}

.check-stock.paper {
  padding: 0 !important;
  width: 8.5in;
  height: 11in;

  .check {
    font-family: 'Source Serif Pro', serif;
    margin: 0 0.15in;
    height: 3.66in;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .check-body {
      flex: 1 0 auto;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      padding: .5cm;
      padding-top: .75cm;
      // margin-bottom: .7cm;
      font-size: .16in;
      position: relative;
    }

    .clear-band {
      flex: 0 0 auto;
      font-size: .25in;
      height: .625in;
      padding: .05in .1875in .325in;
      text-align: center;
      vertical-align: top;
    }

    .logo {
      flex: 0 0 auto;
      height: .65in;
      width: 1in;
      margin: 0;

      img {
        max-height: 100%;
        max-width: 100%;
        margin: auto;
      }
    }

    .address {
      flex: 1 0 auto;
      font-size: .16in;
      margin: 0 .5cm;
      max-width: 3.5in;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      .account-name {
        font-size: .1in;
      }
    }

    .check-number {
      min-width: 0.75in;
      text-align: right;
    }

    .check-recipient {
      .recipient-address {
        font-size: .12in;
        line-height: 100%;
      }
    }

    .check-date {
    }

    .underline {
      border-bottom: 1.5px solid $black;
      padding: .05in;
      position: relative;

      &:after {
        bottom: -.25in;
        content: attr(data-underline);
        font-size: .12in;
        left: 0;
        position: absolute;
      }
    }

    .amount {
      position: relative;
    }

    .row {
      align-items: center;
      display: flex;
      flex: 0 1 auto;
      flex-direction: row;
      justify-content: space-between;
    }

    .top-line {
      display: flex;
      flex: 0 0 auto;
      flex-direction: row;
      justify-content: space-between;
    }

    .bank-name {
      flex: 1 0 auto;
      font-size: .75em;
      line-height: 1.1em;
      max-width: 2in;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      b {
        font-size: 1.2em;
        white-space: wrap;
      }
    }

    .check-recipient {
      margin-top: -.25cm;
    }

    .memo-box {
      align-items: flex-end;
      border-bottom: 1px solid black;
      bottom: .5cm;
      display: flex;
      font-size: .85em;
      font-style: italic;
      height: 1cm;
      left: 1cm;
      line-height: 1em;
      position: absolute;
      width: 8cm;

      span {
        font-family: sans-serif;
        font-style: initial;
        font-size: .7em;
        padding-right: .75em;
        line-height: 1em;
      }
   }

    .signature-box {
      align-items: flex-end;
      border-bottom: 1px solid black;
      bottom: .5cm;
      display: flex;
      height: 1.5cm;
      justify-content: center;
      padding: 0 .25cm;
      position: absolute;
      right: 1cm;
      width: 8cm;
    }

    .signature-image {
      max-height: 100%;
      max-width: 100%;
      object-fit: contain;
    }

    .micr {
      font-family: 'MICR';
    }

    .void {
      font-family: sans-serif;
      font-size: 10rem;
      left: 0;
      line-height: 0;
      opacity: .35;
      position: absolute;
      text-align: center;
      top: 50%;
      transform: rotate(-30deg);
      width: 100%;
    }
  }

  .check-table {
    border-top: 1px dashed $shadow;
    height: 3.67in;
    padding: .5cm 1cm;

    font-family: 'Source Serif Pro', serif;
  }

  .officedepot-num10-dualwindow.check {
    .check-body {
      padding-left: 0.5cm;
      padding-right: 0.5cm;
      padding-top: 1.2cm;
    }

    .clear-band {
      padding: .25in .1875in .25in;
    }

    .memo-box {
      bottom: 0;
    }

    .signature-box {
      bottom: 0;
    }
  }
}
