@import 'color';

.chat-room {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  overflow-y: hidden;

  .breadcrumb {
    padding: 1em 0 0 1em;
  }

  .ui.comments {
    flex: 1 1 auto;
    margin: 0;
    max-width: 100%;
    overflow-y: scroll;

    > .meta {
      color: $gray;
      font-style: italic;
      padding: 1em;
    }

    .comment {
      margin: 0;
      padding: .6em 1em .2em;
      transition: background-color .2s ease-out;

      &:hover {
        background-color: darken($off-white, 3%);
      }

      .text {
        margin: .2em 0;
      }

      &:not(.avatar) {
        padding: .2em 1em;

        .content {
          display: flex;
          flex-direction: row;
        }

        .metadata {
          display: inline;
          opacity: 0;
          transition: opacity .2s ease-out;
          width: 3.5em;
        }

        &:hover {
          .metadata {
            opacity: 1;
          }
        }

        .text {
          display: inline;
          top: 0;
          margin: 0;
        }
      }
    }
  }

  .field {
    border-radius: 0;

    textarea,
    textarea:focus {
      border: 0;
      border-radius: 0;
      border-top: 1px solid #d4d4d5;
      min-height: 80px;
    }
  }
}

.chats.list {
  border-right: 1px solid $gray;
  padding: 18px;

  .item {
    .content {
      border-left: 2px solid transparent;
      transition: border-left .2s;
    }

    &.online {
      .content {
        border-left: 2px solid #f05a2a;
      }
    }
  }
}

[data-react-class="MessagingChats"] {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  overflow-y: hidden;
}
