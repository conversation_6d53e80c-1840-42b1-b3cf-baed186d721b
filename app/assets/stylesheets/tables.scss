// Improve selectable table striping

// TODO: It seems we want the striping and hover darkening however we don't
// want the cursor to change to a pointer, this should be updated so that we
// can actually use selectable as intended... for now use .actually-selectable
// to get a cursor, and then long term we can replace all of them.
table.ui.selectable.table:not(.actually-selectable) td {
  cursor: default !important;
}

.ui.striped.table > tbody > tr {
  transition: background-color .1s;
}

.ui.basic.striped.table > tbody > tr:nth-child(2n) {
  background-color: rgba(0, 0, 0, .02) !important;
}

.ui.ui.selectable.table > tbody > tr:hover {
  background-color: rgba(0, 0, 0, .05) !important;
}

// Disable no-sort columns, not sure why this isn't part of semantic already
table.ui.sortable.table th.no-sort {
  cursor: default;

  &:hover {
    background-color: unset;
  }
}
