class UnitsTableSerializer < ApplicationSerializer
  attributes :id,
             :name,
             :tenants,
             :expires,
             :rent,
             :market_rate

  attribute :floorplan, if: -> { object.property.apartment? }
  attribute :square_feet, if: -> { !object.property.apartment? }

  attribute :status

  def name
    {
      type: 'link',
      url: unit_path(object),
      text: object.name
    }
  end

  def tenants
    if Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox])
      if (present = object.present_tenants.presence)
        {
          type: 'link',
          url: present.first&.url,
          text: present.map(&:name).join(', ')
        }
      elsif (upcoming = object.upcoming_tenants.presence)
        {
          type: 'link',
          url: upcoming.first&.url,
          text: "(Upcoming) #{upcoming.map(&:name).join(', ')}"
        }
      else
        {
          type: 'link',
          url: '#',
          text: ''
        }
      end
    else
      object.present_tenants.map(&:name).join(', ')
    end
  end

  def floorplan
    object.floorplan.name
  end

  def square_feet
    object.floorplan.square_feet
  end

  def rent
    leases = object.active_leases
    amount = Money.sum(leases.map(&:total_monthly_amount)) if leases.any?

    { text: amount&.format || '', sort: amount&.cents || 0 }
  end

  def market_rate
    object.price.format
  end

  def status
    if object.active_leases.any?
      { type: 'label', color: 'olive', text: 'Occupied' }
    else
      { type: 'label', color: 'orange', text: 'Vacant' }
    end
  end

  def expires
    if object.active_leases.any?
      date = object.active_leases.map(&:end_date).max
      days = (date - Time.zone.today).to_i
      {
        text: "#{date.strftime('%b %e, %Y')} (#{days} days)",
        sort: date.to_time.to_i
      }
    else
      { text: '', sort: 0 }
    end
  end
end
