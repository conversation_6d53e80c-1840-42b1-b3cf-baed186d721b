class Plutus::EntrySerializer < ApplicationSerializer
  attributes :id, :date, :description, :url, :credits_cents, :debits_cents

  def url
    accounting_journal_entry_path(object.journal_id, object.id)
  end

  def credits_cents
    total_cents(object.credit_amounts)
  end

  def debits_cents
    total_cents(object.debit_amounts)
  end

  private

  def account
    instance_options[:account]
  end

  def total_cents(amounts)
    amounts.select { |a| a.account == account }.sum(&:amount).to_i
  end
end
