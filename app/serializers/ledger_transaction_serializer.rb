class LedgerTransactionSerializer < ApplicationSerializer
  attributes :id, :date, :description, :type, :amount_cents, :url, :regarding,
             :status

  def amount_cents
    entry = object

    accounts_receivable = Plutus::Account.where(
      id: ChartOfAccounts.select(:accounts_receivable_id)
    )

    credits = Plutus::CreditAmount
              .joins(:account)
              .where(entry: entry, account: accounts_receivable).sum(&:amount)

    debits = Plutus::DebitAmount
             .joins(:account)
             .where(entry: entry, account: accounts_receivable).sum(&:amount)

    (debits - credits).to_i
  end

  def type
    document&.class&.name
  end

  def regarding
    return nil if document.blank?

    if document.is_a? Invoice
      document.buyer.name
    else
      document.payer.name
    end
  end

  def status
    if document.blank?
      'paid'
    elsif document.is_a? Invoice
      document.paid? || document.waived? ? 'paid' : 'unpaid'
    else
      document.reversed? ? 'unpaid' : 'paid'
    end
  end

  def url
    document&.url
  end

  private

  def document
    object.commercial_document
  end
end
