class BookingsTableSerializer < ApplicationSerializer
  attributes :id, :name, :bookings

  def name
    object.qualified_name
  end

  def bookings
    # TODO: replace with non subleases
    object
      .leases
      .where('leases.end_date >= ?', 3.months.ago)
      .unarchived
      .joins(:allocations)
      .where('charge_schedule_entry_allocations.amount_cents > 0')
      .distinct
      .map do |lease|
      {
        id: lease.id,
        tenants: lease.tenants.map(&:name).join(', '),
        start_date: lease.start_date,
        end_date: lease.end_date
      }
    end
  end
end
