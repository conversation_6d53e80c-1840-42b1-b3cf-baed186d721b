class Api::V2::Accounting::AmountResource < JSONAPI::Resource
  include Api::V2::Shared

  model_name 'Plutus::Amount'
  model_hint model: Plutus::CreditAmount, resource: :amount
  model_hint model: Plutus::DebitAmount, resource: :amount

  attributes :direction, :amount

  has_one :account, exclude_links: :default
  has_one :entry, exclude_links: :default

  exclude_links :default

  filter :start_date,
         verify: verify_date_filter,
         apply: lambda { |records, value, options|
           if value
             records.where(plutus_entries: { date: value.. })
           else
             records
           end
         }

  filter :end_date,
         verify: verify_date_filter,
         apply: lambda { |records, value, options|
           if value
             records.where(plutus_entries: { date: ..value })
           else
             records
           end
         }

  def amount
    @model.amount / 100.0
  end

  def direction
    if @model.debit?
      'debit'
    else
      'credit'
    end
  end

  def self.records(options = {})
    # The entries endpoint already checks for an appropriate journal, basis, and
    # lock date, so we don't need to rejoin to entries to ensure that we are
    # fetching amounts for accessible entries.
    return super(options) if we_are_being_included_from_the_entries_endpoint?(options)

    journal = options[:context][:journal]
    basis = options[:context][:basis]

    entries = if basis == 'cash'
                journal.journal_entries.not_accrual_basis
              else
                journal.journal_entries.not_cash_basis
              end

    policy = Api::V2::Accounting::UnlockedPeriodsPolicy.new(
      api_key: options[:context][:api_key]
    )

    unless policy.allowed_to?(:access_unlocked_periods?)
      entries = if journal.locked_at
                  entries.where(date: ..journal.locked_at)
                else
                  entries.none
                end
    end

    super(options).joins(:entry).merge(entries)
  end
end
