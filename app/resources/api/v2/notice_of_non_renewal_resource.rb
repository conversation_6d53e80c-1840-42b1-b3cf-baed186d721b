class Api::V2::NoticeOfNonRenewalResource < JSONAPI::Resource
  include Api::V2::Shared

  model_name 'Lease::NoticeOfNonRenewal'

  attribute :submitted_at, delegate: :date
  attribute :reason
  attribute :anticipated_move_out_date

  attributes :created_at, :updated_at

  has_one :lease

  def self.user_records(relation, user)
    leases = LeasesQuery.new.search.by_user(user)

    relation.where(lease: leases)
  end
end
