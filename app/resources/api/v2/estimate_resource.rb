class Api::V2::EstimateResource < JSONAPI::Resource
  include Api::V2::Shared

  model_name 'Maintenance::Estimate'

  attribute :summary
  attributes :amount, :materials, :labor
  attributes :markup, :materials_markup, :labor_markup
  attributes :approval_requested_at, :approved_at, :rejected_at
  attributes :created_at, :updated_at

  has_one :property
  has_one :unit
  has_one :work_order, relation_name: :maintenance_ticket
  # has_one :inspection
  has_one :prepared_by, class_name: 'Employee'

  def self.records_base(options = {})
    super(options).where.not(
      maintenance_ticket_id: nil
    ).preload(
      :approval_requests, :approvals, :rejections, # for approval attributes
      :tasks # for amount attribtues
    )
  end

  def self.user_records(relation, user)
    # TODO: Eventually use
    # Maintenance::EstimatesQuery.new.search.by_user(user), for now this is
    # instead based on work order access because non work order estimates are
    # not present in this api.
    work_orders = MaintenanceTicketsQuery.new.search.by_user(user)

    relation.where(maintenance_ticket: work_orders)
  end

  def approval_requested_at
    # TODO: use column
    @model.approval_requests.map(&:created_at).max
  end

  def approved_at
    # TODO: use column
    @model.approvals.map(&:created_at).max
  end

  def rejected_at
    # TODO: use column
    @model.rejections.map(&:created_at).max
  end

  def markup
    labor_markup + materials_markup
  end

  def labor_markup
    Money.sum(
      @model.tasks.map do |task|
        task.labor_markup.apply_to(task.labor_amount)
      end
    )
  end

  def materials_markup
    Money.sum(
      @model.tasks.map do |task|
        task.materials_markup.apply_to(task.materials_amount)
      end
    )
  end
end
