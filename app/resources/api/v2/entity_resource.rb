class Api::V2::EntityResource < JSONAPI::Resource
  include Api::V2::Shared
  include Api::V2::TaggableResource

  model_name 'Company'

  attribute :name
  attribute :business_type
  attribute :trade_name
  attribute :state_of_incorporation
  attribute :tags
  attribute :archived_at
  attributes :created_at, :updated_at

  has_one :management_contract
  has_one :portfolio
  has_many :owners
  has_many :properties

  def self.records_base(options = {})
    super(options).includes(:tags)
  end

  def self.user_records(relation, user)
    companies = CompaniesQuery.new.search.by_user(user)

    relation.where(id: companies)
  end
end
