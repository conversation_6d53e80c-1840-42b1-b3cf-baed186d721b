class LeaseApplication::Unadjudicate
  extend Service

  def initialize(user:, lease_application:)
    @user = user
    @lease_application = lease_application
  end

  def call
    unless @lease_application.adjudicated?
      return OpenStruct.new(
        successful?: false,
        errors: ['This application is not adjudicated']
      )
    end

    unless application_can_be_unadjudicated?
      return OpenStruct.new(
        successful?: false,
        errors: ['This lease application was adjudicated too long ago']
      )
    end

    unless user_can_unadjudicate?
      return OpenStruct.new(
        successful?: false,
        errors: ['You cannot unadjudicate lease applications']
      )
    end

    lease_application.update!(
      adjudicated_by: nil, rejected_at: nil, approved_at: nil
    )

    OpenStruct.new(successful?: true)
  rescue ActiveRecord::RecordInvalid => e
    OpenStruct.new(successful?: false, errors: e.record.errors.full_messages)
  end

  private

  attr_reader :user, :lease_application

  def user_can_unadjudicate?
    user.role.administrator?
  end

  def application_can_be_unadjudicated?
    date = lease_application.rejected_at || lease_application.approved_at

    date.after?(7.days.ago)
  end
end
