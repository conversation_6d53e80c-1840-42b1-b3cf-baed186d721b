class LeaseApplication::InitiateIncomeVerification
  extend Service

  attr_reader :lease_application

  def initialize(lease_application)
    @lease_application = lease_application
  end

  def call
    return unless configuration.income_verification?

    income_verification = lease_application.create_income_verification!

    AfterCommitEverywhere.after_commit do
      TheClosingDocs::InitiateIncomeVerificationJob.perform_later(
        income_verification
      )
    end
  end

  delegate :configuration, to: :lease_application
end
