class Tenant::Merge
  extend Service

  attr_reader :source, :destination

  def initialize(source:, destination:)
    @source = source
    @destination = destination
  end

  def call
    ActiveRecord::Base.transaction do
      source.guest_cards.update(tenant: destination)
      source.lease_application_memberships.update(tenant: destination)
      source.lease_memberships.update(tenant: destination)
      source.timeline_entries.update(regarding: destination)
      source.attachments.update(parent: destination)
      source.maintenance_tickets.update(tenant: destination)

      Invoice.where(buyer: source).update_all(buyer_id: destination.id)
      Invoice.where(seller: source).update_all(seller_id: destination.id)
      MaintenanceTicket.where(opened_by: source).update_all(opened_by_id: destination.id)
      Payment.where(payer: source).update_all(payer_id: destination.id)
      Payment.where(payee: source).update_all(payee_id: destination.id)
      Plutus::Entry.where(tenant: source).update_all(tenant_id: destination.id)
      Telephony::TextMessage.where(recipient: source).update_all(recipient_id: destination.id)
      Telephony::TextMessage.where(sender: source).update_all(sender_id: destination.id)
      ElectronicSignature.where(recipient: source).update_all(recipient_id: destination.id)
      Agreements::SimpleAgreement::Membership.where(tenant: source).update_all(tenant_id: destination.id)
      Messaging::Email.where(sender: source).update_all(sender_id: destination.id)
      Messaging::Message::Delivery.where(recipient: source).update_all(recipient_id: destination.id)

      source.reload
      source.destroy!

      OpenStruct.new(successful?: true, tenant: destination)
    end
  rescue ActiveRecord::RecordInvalid => e
    OpenStruct.new(successful?: false, errors: e.record.errors.full_messages)
  end
end
