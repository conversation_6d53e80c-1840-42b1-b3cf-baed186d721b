class Inspectify::Order::ScheduleInternalInspection < Inspectify::Service
  def initialize(inspectify_order)
    super()
    @inspectify_order = inspectify_order
  end

  attr_reader :inspectify_order

  def call
    unless assigned_to_internal_inspectify_vendor?
      return OpenStruct.new(
        successful?: false,
        errors: ['Inspection is not assigned to an internal inspectify vendor.']
      )
    end

    post(endpoint, request)

    inspectify_order.schedule!

    OpenStruct.new(successful?: true)
  end

  private

  delegate :inspectify_order_id, :inspection_report, to: :inspectify_order
  delegate :assigned_to_internal_inspectify_vendor?, to: :inspection_report
  delegate :name, :email, :phone, to: :contractor, prefix: true

  def endpoint
    "inspections/#{inspection_id}/schedule_internal_inspection"
  end

  def inspection_id
    return @inspection_id if defined?(@inspection_id)

    order_url = "orders?order_id[]=#{inspectify_order_id}"

    response = get(order_url)

    case response.body.with_indifferent_access
    in { orders: [{ inspections: [{ id: inspection_id }] }] }
      @inspection_id = inspection_id
    else
      fail 'Unexpected orders response format'
    end
  end

  def request
    {
      booking_time:,
      contractor_name:,
      contractor_email:,
      contractor_phone:
    }
  end

  def booking_time
    Inspectify::BookingTime.new(inspectify_order).booking_time.to_i
  end

  def vendor
    inspection_report.assigned_to
  end

  def contractor
    vendor.primary_contact
  end
end
