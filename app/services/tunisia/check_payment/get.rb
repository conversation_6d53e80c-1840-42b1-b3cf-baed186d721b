class Tunisia::CheckPayment::Get < Tunisia::Service
  def initialize(tunisia_check_payment_id:)
    super()

    @tunisia_check_payment_id = tunisia_check_payment_id
  end

  def call
    response = get(endpoint).body.with_indifferent_access
    case response
    in {
      data: { attributes: { returnCutoffTime: expires_at } },
      included: [{ attributes: { tags: { subdomain: subdomain } } }]
    }
      OpenStruct.new(successful?: true, expires_at:, subdomain:)
    else
      OpenStruct.new(successful?: false, errors: ['Unexpected response format'])
    end
  end

  private

  attr_reader :tunisia_check_payment_id

  def endpoint
    "/check-payments/#{tunisia_check_payment_id}?include=customer"
  end
end
