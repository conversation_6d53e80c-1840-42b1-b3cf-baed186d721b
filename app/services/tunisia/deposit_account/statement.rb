class Tunisia::DepositAccount::Statement < Tunisia::Service
  def initialize(statement_id:, customer:)
    super()
    @statement_id = statement_id
    @customer = customer
  end

  def call
    response = get(endpoint)
    OpenStruct.new(successful?: true, html: response.body)
  end

  def connection
    Faraday.new(url: base_url) do |faraday|
      faraday.response :raise_error
      faraday.adapter :net_http
      faraday.headers['Content-Type'] = 'application/html'
      faraday.headers['Authorization'] = authorization_header
    end
  end

  private

  attr_reader :statement_id, :customer

  delegate :tunisia_customer_id, to: :customer

  def endpoint
    "/statements/#{statement_id}/html"
  end
end
