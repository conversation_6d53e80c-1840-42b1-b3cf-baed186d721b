class Tunisia::CustomerToken::Verification::Create < Tunisia::Service
  def initialize(company:, current_property_manager:)
    super()
    @company = company
    @current_property_manager = current_property_manager
  end

  def call
    result = Tunisia::AuthorizedUsers::GetList.call(customer_id: customer_id, company: @company)

    request = build_request(result)

    if request.nil?
      return OpenStruct.new(successful?: false,
                            errors: ['You are currently not an Authorized User.'])
    end

    response = post(endpoint, request)

    json = response.body.with_indifferent_access

    case json
    in { data: { attributes: { verificationToken: verification_token } } }
      OpenStruct.new(successful?: true, verification_token: verification_token)
    else
      OpenStruct.new(successful?: false, errors: 'Unexpected response format')
    end
  end

  private

  def endpoint
    "/customers/#{customer_id}/token/verification"
  end

  def customer_id
    @company.tunisia_customer_id
  end

  def build_request(result)
    if result.primary_contact.email == @current_property_manager.email
      return primary_contact_request
    end

    current_user = result.authorized_users.find do |user|
      user.email == @current_property_manager.email
    end

    return if current_user.nil?

    authorized_user_request(current_user.tunisia_formatted[:phone])
  end

  def primary_contact_request
    {
      data: {
        type: 'customerTokenVerification',
        attributes: {
          channel: 'sms'
        }
      }
    }
  end

  def authorized_user_request(phone)
    {
      data: {
        type: 'customerTokenVerification',
        attributes: {
          channel: 'sms',
          phone: phone
        }
      }
    }
  end
end
