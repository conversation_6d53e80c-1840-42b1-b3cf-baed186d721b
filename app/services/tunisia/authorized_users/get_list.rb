class Tunisia::AuthorizedUsers::GetList < Tunisia::Service
  attr_reader :company

  def initialize(customer_id:, company:)
    super()
    @customer_id = customer_id
    @company = company
  end

  def call
    response = get(endpoint)

    json = response.body.with_indifferent_access

    case json
    in {
      data: {
        attributes: {
          contact: primary_contact_attrs,
          authorizedUsers: authorized_users_attrs } } }

      primary_contact_user = build(primary_contact_attrs.merge(primary: true))
      OpenStruct.new(
        successful?: true,
        primary_contact: primary_contact_user,
        authorized_users: [primary_contact_user] + authorized_users_attrs.map { build(_1) }
      )
    else
      fail 'Unexpected response format'
    end
  end

  private

  attr_reader :customer_id

  def endpoint
    "/customers/#{customer_id}"
  end

  def build(attrs = {})
    Tunisia::AuthorizedUserPresenter.new(
      email: attrs[:email],
      company: company,
      phone: attrs[:phone],
      name: attrs[:fullName],
      primary: attrs[:primary],
      jwt_subject: attrs[:jwtSubject]
    )
  end
end
