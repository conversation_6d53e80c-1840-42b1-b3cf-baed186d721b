class MemberOnboarding::CreateCharges::Membership < MemberOnboarding::CreateCharges::Base
  attr_reader :membership

  def initialize(membership:, cohort:)
    @membership = membership
    super(cohort:)
  end

  private

  def assign_invoice_attributes(invoice)
    invoice.buyer = membership.primary_tenant
    invoice.seller = membership.property
  end

  def charge_memberships
    return cohort.membership_charge_presets if cohort.is_a?(GreekHousing::Cohort)

    cohort.membership_charge_memberships
  end

  def agreement
    membership
  end
end
