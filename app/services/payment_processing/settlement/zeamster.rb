class PaymentProcessing::Settlement::Zeamster
  MORNING_ACH_DEADLINE_EASTERN = 11

  attr_reader :zeamster_transaction

  def initialize(zeamster_transaction:)
    @zeamster_transaction = zeamster_transaction
  end

  def batch_identifier
    transaction_batch_id if credit_card?
  end

  def estimated_batch_time
    paya_estimated_batch_time if paya_ach?
  end

  def estimated_statement_date
    if credit_card?
      nil # Unknown until we can look at entire group by batch_identifier
    elsif paya_ach?
      paya_estimated_statement_date
    else
      transaction_settle_date
    end
  end

  private

  def transaction_json
    @transaction_json ||= begin
      result = Zeamster::Transaction::Get.call(
        merchant_account: merchant_account,
        transaction: zeamster_transaction
      )

      transaction = result.response.body['transaction']

      if transaction.blank?
        Rails.logger.warn do
          "Unable to lookup zeamster transaction #{zeamster_transaction.id}"
        end

        transaction = {}
      end

      transaction
    end
  end

  def transaction_batch_id
    transaction_json['transaction_batch_id']
  end

  def transaction_settle_date
    transaction_json['settle_date']
  end

  def credit_card?
    payment.credit?
  end

  def weekend_or_holiday?(time)
    time.on_weekend? ||
      time.to_date == Date.new(2022, 6, 20) ||
      Holidays.on(time.to_date, :federalreservebanks).any?
  end

  # TODO: Remove completely once fully migrated off Paya
  def paya_ach?
    payment.ach? && merchant_account.zeamster_paya_ach?
  end

  def paya_estimated_batch_time
    time = created_at.in_time_zone('America/New_York')

    if time.hour < MORNING_ACH_DEADLINE_EASTERN
      time = time.change(hour: MORNING_ACH_DEADLINE_EASTERN)
      time += 1.day while weekend_or_holiday?(time)
    else
      time += 1.day
      time += 1.day while weekend_or_holiday?(time)
      time = time.change(hour: MORNING_ACH_DEADLINE_EASTERN)
    end

    time
  end

  def paya_estimated_statement_date
    time = paya_estimated_batch_time

    time += 1.day

    time += 1.day while weekend_or_holiday?(time)

    time.to_date
  end

  delegate :created_at, :payment, :merchant_account, to: :zeamster_transaction
end
