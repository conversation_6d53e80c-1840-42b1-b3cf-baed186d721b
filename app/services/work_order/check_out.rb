##
# Check out of a work order.
#
# How can this possibly need a whole service, you ask? Well, checking out
# involves creating other records for accounting/billing reasons. Potentially
# timesheet/HR things in the future, too.
class WorkOrder::CheckOut
  extend Service

  attr_reader :work_order, :user

  def initialize(work_order:, user:)
    @work_order = work_order
    @user = user
  end

  def call
    if work_order.resolved? || work_order.closed?
      self.class.resp_error(
        errors: ['Cannot check out of a resolved/closed work order.']
      )
    elsif checked_in?
      event = work_order.events.check_out.create!(author: user)
      create_labor_items(event)
      self.class.resp_success
    else
      self.class.resp_error(errors: ['Not checked in to this work order.'])
    end
  end

  private

  def checked_in?
    work_order.user_checked_in_since(user).present?
  end

  def new_time(event, date, hour, min, sec, zone)
    d = date.presence || event.created_at.to_date
    copy = event.dup
    copy.id = nil
    copy.created_at = Time.zone.local(d.year, d.month, d.day, hour, min, sec)
    copy.readonly!
    copy
  end

  def handle_split_dates(check_in:, check_out:)
    if check_in.created_at.to_date == check_out.created_at.to_date
      [[check_in, check_out]]
    else
      tmp = []

      start = check_in.created_at.to_date
      finish = check_out.created_at.to_date
      zone = check_in.created_at.time_zone

      (start..finish).each do |d|
        tmp << if d == start
                 [check_in, new_time(check_in, nil, 23, 59, 59, zone)]
               elsif d == finish
                 [new_time(check_out, nil, 0, 0, 1, zone), check_out]
               else
                 [
                   new_time(check_in, d, 0, 0, 1, zone),
                   new_time(check_in, d, 23, 59, 59, zone)
                 ]
               end
      end

      tmp
    end
  end

  def create_labor_items(checkout)
    checkin = work_order
              .events
              .check_in
              .where(author: user)
              .reorder(created_at: :desc)
              .first

    grouped = handle_split_dates(check_in: checkin, check_out: checkout)

    grouped.each do |g|
      start, finish = g
      hrs = hours_from_seconds((finish.created_at - start.created_at).floor)

      work_order.labor_items.create!(
        hours: hrs,
        date: start.created_at.to_date,
        rate: work_order.billing_rate,
        employee: start.author
      )
    end
  end

  ##
  # Seconds to hours, rounded up to the next 15 minutes.
  def hours_from_seconds(seconds)
    quarter_hours = BigDecimal(seconds) / 60 / 15
    BigDecimal(quarter_hours.ceil) / 4
  end
end
