class Lease::Extend
  extend Service

  attr_reader :lease, :effective_date

  def initialize(lease:, effective_date:)
    @lease = lease
    @effective_date = effective_date
  end

  def call
    extend_lease unless lease.fixed?

    convert_to_month_to_month if lease.rollover?

    OpenStruct.new(successful?: true)
  rescue ActiveRecord::RecordInvalid => e
    OpenStruct.new(successful?: false, errors: e.record.errors.full_messages)
  end

  private

  def extend_lease
    new_end_date = if lease.end_date == lease.end_date.end_of_month
                     lease.end_date.next_month.end_of_month
                   else
                     lease.end_date + 1.month
                   end

    lease.update!(end_date: new_end_date)
    lease.lease_memberships.update(move_out_date: new_end_date)
  end

  def convert_to_month_to_month
    result = Lease::AddMonthToMonthCharge.call(
      lease: lease, effective_date: effective_date
    )

    fail result.errors.to_sentence unless result.successful?

    lease.end_date_before_rollover = lease.end_date_previously_was

    lease.month_to_month!
  end
end
