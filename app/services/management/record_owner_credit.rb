class Management::RecordOwnerCredit
  extend Service

  attr_reader :params

  def initialize(params)
    @params = params
  end

  def call
    unless property
      return OpenStruct.new(successful?: false,
                            errors: ['Please select a property'])
    end

    unless Monetize.parse(credit_params[:amount]).positive?
      return OpenStruct.new(successful?: false,
                            errors: ['Amount must be postive'])
    end

    # TODO: Require payable and receivable accounts

    ActiveRecord::Base.transaction do
      invoice = create_invoice

      payment = create_payment(invoice)

      OpenStruct.new(successful?: true, invoice: invoice, payment: payment)
    end
  rescue ActiveRecord::RecordInvalid => e
    OpenStruct.new(successful?: false, errors: e.record.errors.full_messages)
  end

  private

  def create_invoice
    Invoice.create!(
      buyer: property,
      seller: management_company,
      date: credit_params[:date],
      description: description,
      line_items_attributes: [
        {
          description: description,
          unit_price: credit_params[:amount],
          quantity: 1,
          receivable_account: due_to_customer_account,
          payable_account: due_from_client_entity_account
        }
      ]
    ) do |invoice|
      invoice.invoice_payment_approved_at = Time.zone.now
    end
  end

  def create_payment(invoice)
    Payment.create!(
      kind: :credit_note,
      payee: management_company,
      payer: property,
      date: credit_params[:date],
      description: description,
      amount: credit_params[:amount],
      note: credit_params[:notes],
      payable_credit_note_account_id: \
      credit_params[:payable_credit_note_account_id],
      receivable_credit_note_account_id: \
      credit_params[:receivable_credit_note_account_id]
    ) do |payment|
      payment.invoice_payments.build(invoice: invoice, amount: payment.amount)
    end
  end

  def property
    Property.find_by(
      company_id: params[:manage_company_id],
      id: credit_params[:property_id]
    )
  end

  def management_company
    Customer.current.client_entity
  end

  def due_to_customer_account
    management_company.chart_of_accounts.due_to_customer_account
  end

  def due_from_client_entity_account
    property.company.chart_of_accounts.due_from_client_entity_account
  end

  def description
    credit_params[:description].presence || 'Owner Credit'
  end

  def credit_params
    params.require(:owner_credit).permit(
      :property_id, :date, :description, :amount, :notes,
      :payable_credit_note_account_id, :receivable_credit_note_account_id
    )
  end
end
