class Management::PrepareDisbursements
  extend Service

  attr_reader :params

  def initialize(params)
    @params = params
  end

  def call
    send_slack_notification

    if Rails.env.production? &&
       Feature.enabled?(:prepared_disbursement_amounts, Customer.current)
      prepare_from_prepared
    else
      prepare_from_accounting
    end
  rescue ActiveRecord::RecordInvalid => e
    OpenStruct.new(successful?: false, errors: e.record.errors.full_messages)
  end

  private

  def post_date
    @post_date ||= begin
      Date.parse(params.dig(:disbursement, :post_date))
    rescue StandardError
      Time.zone.today
    end
  end

  def effective_date
    @effective_date ||= begin
      Date.parse(params.dig(:disbursement, :effective_date))
    rescue StandardError
      Time.zone.today
    end
  end

  def entities
    @entities ||= begin
      ids = params.dig(:disbursement, :entity_ids)&.split(',')
      Company.setup.where(id: ids)
    end
  end

  def properties
    @properties ||= entities.flat_map(&:properties)
  end

  def prepare_from_accounting
    ActiveRecord::Base.transaction do
      Plutus.in_cash_basis(journal_cache_hint: entities) do
        payables_batch = Payment::Batch.create!(
          name: params.dig(:disbursement, :payables_batch_name)
        )

        properties.each do |property|
          activity = Accounting::CashActivity.new(
            target: property,
            start_date: nil,
            end_date: effective_date
          )

          balance = activity.cash_balance_on_date(effective_date)

          amount = if params.dig(:disbursement, :disbursement_method) == 'zero_cash_balance'
                     balance
                   else
                     unpaid = activity.unpaid_expenses
                     reserve = activity.reserve_amount
                     balance - unpaid - reserve
                   end

          next unless amount.positive?

          Management::PrepareDisbursement.call(
            property: property,
            amount: amount,
            date: post_date,
            payables_batch: payables_batch
          )
        end

        OpenStruct.new(successful?: true, payables_batch: payables_batch)
      end
    end
  end

  def prepare_from_prepared
    ActiveRecord::Base.transaction do
      if Feature.enabled?(:per_property_disbursements, Customer.current)
        payables_batches = []

        xproperties = properties.select do |property|
          amount = Monetize.parse(property.meta(:prepared_disbursement_amount))

          amount.positive?
        end

        maximum_batch_size = begin
          Integer(Redis.instance { |r| r.get('maximum_batch_size') })
        rescue StandardError
          1000
        end

        slices = xproperties.each_slice(maximum_batch_size)

        slices.each.with_index do |slice, index|
          name = params.dig(:disbursement, :payables_batch_name)

          name += " (#{index + 1} of #{slices.count})" if slices.many?

          payables_batch = Payment::Batch.create!(name: name)

          payables_batches << payables_batch

          slice.each do |property|
            amount = Monetize.parse(property.meta(:prepared_disbursement_amount))

            next unless amount.positive?

            Management::PrepareDisbursement.call(
              property: property,
              amount: amount,
              date: post_date,
              payables_batch: payables_batch
            )
          end
        end

        OpenStruct.new(successful?: true, payables_batch: payables_batches.first)
      else
        payables_batch = Payment::Batch.create!(
          name: params.dig(:disbursement, :payables_batch_name)
        )

        entities.each do |entity|
          amount = Monetize.parse(entity.meta(:prepared_disbursement_amount))

          next unless amount.positive?

          Management::PrepareEntityDisbursement.call(
            entity: entity,
            amount: amount,
            date: post_date,
            payables_batch: payables_batch
          )
        end

        OpenStruct.new(successful?: true, payables_batch: payables_batch)
      end
    end
  end

  def send_slack_notification
    message = "Preparing #{Customer.current_subdomain} disbursements " \
              "for #{properties.count} property(s) @charlotte :eyes:"

    Slack::SendSystemNotificationJob.perform_later(message: message)
  end
end
