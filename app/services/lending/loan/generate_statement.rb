class Lending::Loan::GenerateStatement
  extend Service

  attr_reader :loan, :date, :entry

  def initialize(loan:, date:)
    @loan = loan
    @date = date
  end

  def call
    @entry = find_relevant_entry

    return OpenStruct.new(successful?: true) unless entry

    Lending::Loan::GenerateStatementFromEntry.call(
      loan: loan, entry: entry
    )
  end

  private

  def amortization_schedule
    Lending::AmortizationSchedule.new(loan: loan)
  end

  def find_relevant_entry
    amortization_schedule.entries.detect do |entry|
      entry.date == date
    end
  end
end
