class Taxes::Nelco::CreateAccount::Response
  def initialize(raw_response)
    @raw_response = raw_response
  end

  def successful?
    response_code == '1'
  end

  def validate!
    fail build_error unless successful?
  end

  attr_reader :raw_response

  private

  def response_code
    @response_code ||= raw_response
                       .xpath("//*[local-name()='AccountSetupResult']")
                       .text
  end

  def build_error
    ep = Taxes::Nelco::CreateAccount::ErrorCodes.fetch(response_code)

    if ep[:ui_remediable]
      return Taxes::Nelco::CreateAccount::FormError.new(ep[:remediation])
    end

    Taxes::Nelco::CreateAccount::Error.new(ep[:description])
  end
end
