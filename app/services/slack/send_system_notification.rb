class Slack::SendSystemNotification
  extend Service

  attr_reader :message, :channel, :file_attributes

  def initialize(message:, channel: '#system', file_attributes: nil)
    @message = message
    @channel = channel
    @file_attributes = file_attributes
  end

  def call
    return if ENV['SLACK_SYSTEM_NOTIFICATIONS_TOKEN'].blank?

    if file_attributes
      client.files_upload(
        channels: channel,
        as_user: false,
        content: file_attributes[:content],
        title: file_attributes[:title],
        filename: file_attributes[:name],
        initial_comment: message
      )
    else
      client.chat_postMessage(
        channel: channel,
        text: message
      )
    end
  end

  private

  def client
    @client ||= Slack::Web::Client.new \
      token: ENV.fetch('SLACK_SYSTEM_NOTIFICATIONS_TOKEN')
  end
end
