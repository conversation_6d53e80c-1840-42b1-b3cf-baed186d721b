class Agreements::SimpleAgreement::GenerateDocumentPacket <
  Lease::GenerateDocumentPacket
  def initialize(agreement:, templates:)
    super(lease: agreement, templates: templates)
  end

  protected

  def pdf_values
    values = {
      created_at: lease.created_at.to_fs(:human_date),
      execution_date: execution_date.to_fs(:human_date),
      tenant_names: tenants.map(&:name).to_sentence,
      property_name: property.name,
      property_address: property_address,
      property_phone: property.formatted_phone,
      start_date: lease.start_date&.to_datetime&.to_fs(:human_date),
      end_date: lease.end_date&.to_datetime&.to_fs(:human_date),
      entity_name: property.company.name
    }

    {
      todays_date: Time.zone.today,
      start_date: lease.start_date,
      end_date: lease.end_date,
      execution_date: execution_date
    }.compact.each do |key, value|
      values[key.to_s] = value.to_fs(:human_date)
      values["#{key}.month_name"] = value.strftime('%B')
      values["#{key}.day_ordinal"] = value.day.ordinalize
      values["#{key}.year"] = value.year
    end

    tenants = lease.memberships.reorder(:role, :id).map(&:tenant)

    tenants.each_with_index do |tenant, index|
      prefix = "tenants[#{index}]"

      values.merge!(
        "#{prefix}.name" => tenant.name,
        "#{prefix}.first_name" => tenant.first_name,
        "#{prefix}.last_name" => tenant.last_name,
        "#{prefix}.email" => tenant.email,
        "#{prefix}.phone" => tenant.formatted_phone
      )
    end

    metadata = tenants.map(&:metadata_values).reduce(&:reverse_merge).merge(
      lease.metadata_values
    )

    metadata.each do |key, value|
      values["meta[#{key}]"] = value
    end

    values.transform_values! { |v| v.nil? ? '' : v }

    values
  end
end
