class Agreements::BulkPresets::AddCharge
  extend Service

  # TODO: Remove params:
  def initialize(agreement:, form_model:, params:)
    @agreement = agreement
    @form_model = form_model
    @params = params
  end

  def call
    installments = form_model.installments.sort_by(&:post_date)

    installment_count = installments.count

    installments.each.with_index do |installment, index|
      line_items_attributes = form_model.line_items.map do |item|
        charge_preset = item.charge_preset

        account = charge_preset&.account || item.receivable_account

        ratio_price = item.unit_price / installment_count

        amount = if index == installment_count - 1
                   # Last, subtract '2/3, e.g.' to ensure adds up to '100 cents'
                   item.unit_price - (ratio_price * (installment_count - 1))
                 else
                   ratio_price
                 end

        {
          description: item.description,
          charge_preset: charge_preset,
          receivable_account: account,
          quantity: item.quantity,
          unit_price: amount
        }
      end

      description = form_model.description

      if installment_count > 1
        number = (index + 1).humanize.titleize
        description += " - Installment #{number}"
      end

      Invoice.create!(
        buyer: agreement.primary_tenant,
        buyer_lease_membership: agreement.try(:primary_lease_membership),
        seller: agreement.property,
        description: description,
        post_date: installment.post_date,
        physical_date: installment.post_date,
        due_date: installment.due_date,
        notification_preference: form_model.notification_preference,
        payment_plan_eligible: form_model.payment_plan_eligible?,
        note: form_model.notes,
        line_items_attributes: line_items_attributes,
        bulk_attachments: params.dig(:bulk_charge, :bulk_attachments)
      )
    end

    installment_count
  end

  private

  attr_reader :agreement, :form_model, :params
end
