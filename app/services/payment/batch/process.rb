class Payment::Batch::Process
  extend Service

  class BatchPaymentFailure < StandardError; end

  # Prevent overflow of check stubs table
  MAXIMUM_INVOICES_PER_CHECK = 6

  attr_reader :payment_batch

  def initialize(payment_batch:)
    @payment_batch = payment_batch
    @pending_ach_transactions = []
  end

  def call
    payments = nil

    ActiveRecord::Base.transaction do
      payment_batch.close!

      invoice_balances = {}

      payments = payment_batch.pairs.flat_map do |pair|
        pair.make_payments(invoice_balances, @pending_ach_transactions)
      end

      payment_batch.meta!(:invoice_balances, invoice_balances)
    end

    @pending_ach_transactions.each do |args|
      PaymentProcessing::ProcessElectronicPaymentJob.perform_later(*args)
    end

    if Customer.current_subdomain.start_with?('gebrael', 'pmi')
      PaymentProcessing::NotifyOwnerDisbursementsJob.perform_later
    end

    OpenStruct.new(successful?: true, payments: payments)
  rescue ActiveRecord::RecordInvalid => e
    OpenStruct.new(successful?: false, errors: e.record.errors.full_messages)
  rescue BatchPaymentFailure => e
    OpenStruct.new(successful?: false, errors: Array(e.message))
  end
end
