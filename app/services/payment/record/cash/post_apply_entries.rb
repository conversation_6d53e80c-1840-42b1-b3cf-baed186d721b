# rubocop:disable Metrics
module Payment::Record::Cash::PostApplyEntries
  private

  def build_apply_entries
    return unless invoice_payments.any?

    return build_void_entries if payment.invoice_void?

    if is_a?(Payment::Record::Cash::Receivable)
      invoice_payments.each do |invoice_payment|
        debits = []
        credits = []

        add_merchant_processing = !invoice_payment.from_prepaid_for_persisted_cash_basis? && payment.deposit_bank_account&.owner == journal && payment.deposit_convenience_fee.positive?

        groups = invoice_payment.invoice.line_items.group_by(&:receivable_account)

        pt_map = {}

        can_passthrough = journal != client_entity

        if can_passthrough
          groups.select do |account, _|
            account&.passthrough?
          end.each do |account, items|
            ratio = invoice_payment.amount / invoice_payment.invoice.amount
            amount = (items.sum(&:amount).cents * ratio).round

            dest_account = Accounting::PassthroughAccount.new(
              local_account: account,
              passthrough_chart_of_accounts: client_entity.chart_of_accounts
            ).account

            pt_map[dest_account] = amount
          end
        end

        if pt_map.any?
          total = pt_map.values.sum

          dt = Accounting::DueToFromMatrix.new(journal).due_to(client_entity)

          # journal_entries.persisted_cash_basis_only.build(
          #   kind: :cash_basis_payment_post,
          #   property: property,
          #   unit: unit,
          #   journal: journal,
          #   date: date,
          #   description: invoice_payment.invoice.description,
          #   debits: [{ amount: total, account: dt }],
          #   credits: [{ amount: total, account: df }]
          # )

          journal_entries.build(
            basis: :persisted_cash_basis_only,
            kind: :cash_basis_payment_post,
            property: property,
            unit: unit,
            tenant: tenant,
            lease_membership: lease_membership,
            journal: client_entity,
            date: date,
            description: invoice_payment.invoice.description,
            invoice_payment_id: invoice_payment.id,
            debits: [{ amount: total, account: dt }],
            credits: pt_map.map do |account, amount|
              { account: account, amount: amount }
            end
          )
        end

        ptcredit = 0

        credits += groups.filter_map do |account, items|
          ratio = invoice_payment.amount / invoice_payment.invoice.amount
          amount = (items.sum(&:amount).cents * ratio).round

          if can_passthrough && account&.passthrough?
            ptcredit += amount
            nil
          else
            { amount: amount, account: account }
          end
        end

        if ptcredit.positive?
          credits += [{
            amount: ptcredit,
            account: Accounting::DueToFromMatrix.new(journal).due_from(client_entity)
          }]
        end

        acct = if invoice_payment.from_prepaid_for_persisted_cash_basis?
                 journal.chart_of_accounts.prepaid_revenue_account
               else
                 apply_cash_account
               end

        description = invoice_payment.invoice.description

        date = if invoice_payment.from_prepaid_for_persisted_cash_basis?
                 invoice_payment.invoice.post_date
               else
                 payment.date
               end

        mp_amount_cents = 0

        if add_merchant_processing
          mp_ratio = invoice_payment.amount / payment.amount

          mp_amount_cents = (payment.deposit_convenience_fee.cents * mp_ratio).round

          credits += [{ amount: mp_amount_cents,
                        account: journal.chart_of_accounts.merchant_processing_revenue_account }]
        end

        post_amount = if add_merchant_processing
                        invoice_payment.amount + Money.new(mp_amount_cents)
                      else
                        invoice_payment.amount
                      end

        debits += [{ amount: post_amount.cents, account: acct }]

        adjust_last_to_match(credits, post_amount.cents)

        journal_entries.build(
          basis: :persisted_cash_basis_only,
          kind: :cash_basis_payment_post,
          property: property,
          unit: unit,
          tenant: tenant,
          lease_membership: lease_membership,
          journal: journal,
          date: date,
          description: description,
          invoice_payment_id: invoice_payment.id,
          debits: debits,
          credits: credits
        )
      end
    else
      invoice_payments.each do |invoice_payment|
        debits = []
        credits = []

        groups = invoice_payment.invoice.line_items.group_by(&:payable_account)

        debits += groups.map do |account, items|
          ratio = invoice_payment.amount / invoice_payment.invoice.amount
          amount = (items.sum(&:amount).cents * ratio).round
          { amount: amount, account: account }
        end

        acct = if invoice_payment.from_prepaid_for_persisted_cash_basis?
                 journal.chart_of_accounts.prepaid_expense_account
               else
                 apply_cash_account
               end

        description = invoice_payment.invoice.description

        date = if invoice_payment.from_prepaid_for_persisted_cash_basis?
                 invoice_payment.invoice.post_date
               else
                 payment.date
               end

        credits += [{ amount: invoice_payment.amount.cents, account: acct }]

        adjust_last_to_match(debits, invoice_payment.amount.cents)

        journal_entries.build(
          basis: :persisted_cash_basis_only,
          kind: :cash_basis_payment_post,
          property: property,
          unit: unit,
          tenant: tenant,
          lease_membership: lease_membership,
          journal: journal,
          date: date,
          description: description,
          invoice_payment_id: invoice_payment.id,
          debits: debits,
          credits: credits
        )
      end
    end
  end

  def apply_cash_account
    if is_a?(Payment::Record::Cash::Receivable)
      if payment.credit_note?
        payment.receivable_credit_note_account
      else
        bank_account = payment.deposit_bank_account

        if bank_account&.owner == journal
          payment.deposit_bank_account&.ledger_account || journal.chart_of_accounts.owner_cash_account
        elsif bank_account
          Accounting::DueToFromMatrix.new(journal).due_from(bank_account.owner)
        elsif hits_owner_cash?
          journal.chart_of_accounts.owner_cash_account
        else
          journal.default_deposit_account # TODO: Why? Always?
        end
      end
    elsif is_a?(Payment::Record::Cash::Payable)
      if payment.credit_note?
        payment.payable_credit_note_account
      else
        bank_account = payment.withdrawal_bank_account

        if bank_account&.owner == journal
          payment.withdrawal_bank_account&.ledger_account || journal.chart_of_accounts.owner_cash_account
        elsif bank_account
          Accounting::DueToFromMatrix.new(journal).due_from(bank_account.owner)
        elsif hits_owner_cash?
          journal.chart_of_accounts.owner_cash_account
        else
          journal.default_withdrawal_account
        end
      end
    end
  end
end
# rubocop:enable Metrics
