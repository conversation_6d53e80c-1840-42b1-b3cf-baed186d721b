class Telephony::PhoneNumber::Provision < Telephony::TwilioService
  attr_reader :locality, :phone_number, :service_sid, :callback_url

  def initialize(account_holder:, phone_number:, locality:, service_sid:, callback_url:)
    super(account_holder)

    @phone_number = phone_number
    @locality = locality
    @service_sid = service_sid
    @callback_url = callback_url
  end

  def call
    result = client.incoming_phone_numbers.create(
      phone_number: phone_number,
      sms_url: callback_url
    )

    add_to_messaging_service(result.sid)

    record = Telephony::PhoneNumber.create!(
      twilio_sid: result.sid,
      twilio_service_sid: service_sid,
      number: result.phone_number,
      friendly_name: result.friendly_name,
      locality: locality
    )

    successful(phone_number: record)
  rescue ActiveRecord::RecordInvalid => e
    unsuccessful(e.record.errors.full_messages)
  rescue Twilio::REST::TwilioError => e
    unsuccessful([e.message])
  end

  private

  def add_to_messaging_service(twilio_sid)
    client.messaging
          .v1
          .services(service_sid)
          .phone_numbers
          .create(phone_number_sid: twilio_sid)
  end
end
