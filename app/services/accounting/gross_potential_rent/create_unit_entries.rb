class Accounting::GrossPotentialRent::CreateUnitEntries
  extend Service

  attr_reader :unit, :period

  def initialize(unit:, period:)
    @unit = unit
    @period = period
  end

  def call
    entry = journal_entries.create!(
      kind: :manual_entry,
      basis: :accrual_basis_only,
      description: 'Gross Potential Rent Adjustment',
      date: period.end_of_month,
      property: property,
      unit: unit,
      debits: debits,
      credits: credits
    )

    OpenStruct.new(successful?: true, entry: entry)
  end

  private

  delegate :property, :floorplan, :configuration, to: :unit
  delegate :company, to: :property
  delegate :journal_entries, :chart_of_accounts, to: :company
  delegate :gross_potential_rent_account,
           :vacancy_loss_account,
           :gain_loss_to_lease_account, to: :chart_of_accounts

  def debits
    [
      vacancy_loss_debit,
      loss_to_lease_debit,
      decreate_accrued_rent_debit
    ].compact
  end

  def credits
    [
      gross_potential_rent_credit,
      gain_to_lease_credit
    ].compact
  end

  def gross_potential_rent_credit
    return if gross_potential_rent.zero?

    {
      amount: gross_potential_rent.cents,
      account: gross_potential_rent_account
    }
  end

  def gain_to_lease_credit
    return unless gain_loss_to_lease.positive?

    {
      amount: gain_loss_to_lease.cents,
      account: gain_loss_to_lease_account
    }
  end

  def decreate_accrued_rent_debit
    return unless accrued_base_rent.positive?

    {
      amount: accrued_base_rent.cents,
      account: base_rent_account
    }
  end

  def vacancy_loss_debit
    return if vacancy_loss.zero?

    {
      amount: vacancy_loss.cents,
      account: vacancy_loss_account
    }
  end

  def loss_to_lease_debit
    return unless gain_loss_to_lease.negative?

    {
      amount: gain_loss_to_lease.abs.cents,
      account: gain_loss_to_lease_account
    }
  end

  def base_rent_account
    configuration.rent_preset.account
  end

  def market_rate
    floorplan.price
  end

  def gross_potential_rent
    market_rate
  end

  def vacancy_loss
    vacant? ? market_rate : Money.zero
  end

  def gain_loss_to_lease
    vacant? ? Money.zero : accrued_base_rent - market_rate
  end

  def accrued_base_rent
    @accrued_base_rent ||= begin
      entries = \
        Plutus::Entry.where(
          date: period.all_month,
          unit: unit
        )

      cents = \
        Plutus::CreditAmount
        .joins(:entry)
        .merge(entries)
        .where(account: base_rent_account)
        .sum(:amount)

      Money.new(cents)
    end
  end

  def vacant?
    @vacant ||= unit.vacant?(period.beginning_of_month)
  end
end
