##
# Adds service fees and interest journal entries at the beginning of a
# reconciliation.
class Accounting::AddReconciliationEntries
  extend Service

  attr_reader :reconciliation, :params

  def initialize(reconciliation:, params:)
    @reconciliation = reconciliation
    @params = params
  end

  def call
    add_service_fees_entry
    add_interest_entry
  end

  private

  def add_service_fees_entry
    return if service_fees.zero?

    journal.journal_entries.create!(
      kind: :manual_entry,
      date: reconciliation.statement_date,
      description: 'Service Charges',
      debits: [{ amount: service_fees.cents, account: service_fees_account }],
      credits: [{
        amount: service_fees.cents,
        account: ledger_account,
        reconciliation: reconciliation
      }]
    )
  end

  def add_interest_entry
    return if interest.zero?

    journal.journal_entries.create!(
      kind: :manual_entry,
      date: reconciliation.statement_date,
      description: 'Interest Earned',
      debits: [{
        amount: interest.cents,
        account: ledger_account,
        reconciliation: reconciliation
      }],
      credits: [{ amount: interest.cents, account: interest_account }]
    )
  end

  def service_fees
    Monetize.parse(params.dig(:reconciliation, :service_charges))
  end

  def service_fees_account
    Plutus::Expense.find_by \
      id: params.dig(:reconciliation, :service_charge_account_id)
  end

  def interest
    Monetize.parse(params.dig(:reconciliation, :interest_earned))
  end

  def interest_account
    Plutus::Revenue.find_by \
      id: params.dig(:reconciliation, :interest_account_id)
  end

  delegate :journal, :ledger_account, to: :reconciliation
end
