module SierraLeone::Inspection::Orders::AnswerUtils
  VISIBILITY_OPTIONS = ['Visible', 'Not Visible'].freeze
  YES_NO_OPTIONS = ['Yes', 'No'].freeze
  YES_NO_NA_OPTIONS = ['Yes', 'No', 'N/A'].freeze

  private

  def coerce_sl_response(question:, sl_response:) # rubocop:disable Metrics/CyclomaticComplexity,Metrics/PerceivedComplexity
    case question.options
    when VISIBILITY_OPTIONS
      sl_response ? 'Visible' : 'Not Visible'
    when YES_NO_OPTIONS
      sl_response ? 'Yes' : 'No'
    when YES_NO_NA_OPTIONS
      if sl_response.nil?
        'N/A'
      else
        sl_response ? 'Yes' : 'No'
      end
    else
      sl_response&.strip
    end
  end

  def coerce_struct(value)
    if value.is_a?(OpenStruct)
      value
    else
      OpenStruct.new(value:)
    end
  end
end
