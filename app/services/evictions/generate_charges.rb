class Evictions::GenerateCharges
  extend ::Service

  attr_reader :eviction, :property, :management_contract, :configuration

  delegate :eviction_owner_charge_preset, :eviction_owner_mark_paid?,
           to: :configuration

  delegate :eviction_tenant_charge_preset, to: :configuration

  def initialize(eviction:, property:, configuration:, management_contract:)
    @eviction = eviction
    @property = property
    @configuration = configuration
    @management_contract = management_contract
  end

  def call
    charge_owner!
    charge_tenant!
  end

  private

  def owner_eligible?
    return true if management_contract.blank?

    management_contract.eligible_for_eviction_fees?
  end

  def charge_owner!
    return self.class.resp_success unless eviction_owner_charge_preset.present? && owner_eligible?

    result = Accounting::ChargeFromPreset.call!(
      charge_presets: eviction_owner_charge_preset
    ) do |invoice|
      invoice.buyer = property
      invoice.seller = Customer.current.client_entity
    end

    mark_paid!(result.invoice) if eviction_owner_mark_paid?

    result
  end

  def charge_tenant!
    return self.class.resp_success if eviction_tenant_charge_preset.blank?

    Accounting::ChargeFromPreset.call!(
      charge_presets: eviction_tenant_charge_preset
    ) do |invoice|
      invoice.buyer = eviction.lease.primary_tenant
      invoice.seller = property
    end
  end

  def mark_paid!(invoice)
    Invoice::PayWithOwnerBalance.call!(invoice: invoice)
  end
end
