class Collections::Communications::MakePhoneCall <
  Collections::Communications::TwilioCollectionsService
  def call
    make_phone_call!

    OpenStruct.new(successful?: true)
  end

  private

  def make_phone_call!
    Rails.logger.info do
      "Making collections call to #{to_phone} from #{from_phone}"
    end

    twilio_client.calls.create(
      method: 'GET',
      status_callback: status_callback_url,
      status_callback_method: 'POST',
      url: twiml_url,
      to: to_phone,
      from: from_phone
    )
  end

  def twiml_url
    routes.telephony_collections_communications_phone_call_url(
      communication,
      subdomain: Customer.current_subdomain
    )
  end

  def status_callback_url
    routes.status_callback_telephony_collections_communications_phone_call_url(
      communication,
      subdomain: Customer.current_subdomain
    )
  end

  def routes
    Rails.application.routes.url_helpers
  end
end
