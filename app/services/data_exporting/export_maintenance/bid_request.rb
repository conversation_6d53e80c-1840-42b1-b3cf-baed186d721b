class DataExporting::ExportMaintenance::BidRequest
  extend Service

  include DataExporting::ExportAttachments

  attr_reader :bid_request

  def initialize(bid_request, user:)
    @bid_request = bid_request
  end

  def call
    Dir.mktmpdir do |directory|
      export_attachments(directory)

      yield directory if block_given?
    end

    OpenStruct.new(successful?: true)
  end

  def attachments
    if bid_request.include_attachments?
      return MaintenanceTicket::AttachmentsQuery
        .for_maintenance_ticket(bid_request.maintenance_ticket)
        .before(bid_request.created_at)
        .map(&:attachment)
    end

    return bid_request.attachments
  end
end
