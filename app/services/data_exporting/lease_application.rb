class DataExporting::LeaseApplication
  attr_reader :lease_application

  def initialize(lease_application:)
    @lease_application = lease_application
  end

  def export(directory:)
    destination = File.join(directory, filename)

    File.binwrite(destination, pdf)
  end

  private

  def filename
    "lease-application-#{status}-#{lease_application.id}.pdf"
  end

  def pdf
    Pdf.from_html(html)
  end

  def html
    ApplicationController.render(
      template: 'leasing/applications/_lease_application',
      layout: 'grover',
      assigns: { application: lease_application }
    )
  end

  def status
    if lease_application.rejected?
      'rejected'
    elsif lease_application.approved?
      'approved'
    elsif lease_application.awaiting_results?
      'pending'
    end
  end
end
