class DataExporting::LeaseSummary
  attr_reader :lease

  def initialize(lease:)
    @lease = lease
  end

  def export(directory:)
    template = 'agreements/_summary'

    locals = { agreement: lease.decorate, current_property_manager: nil }

    html = ApplicationController.render \
      template, layout: 'grover', locals: locals

    pdf = Pdf.from_html(html)

    destination = File.join(directory, "lease-#{lease.id}-summary.pdf")

    File.open(destination, 'wb') { |f| f.puts(pdf) }
  end
end
