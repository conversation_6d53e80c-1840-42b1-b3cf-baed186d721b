class Invoice::MarkPaid
  extend Service

  attr_reader :invoice, :params, :receivable

  def initialize(invoice:, params:, receivable:)
    @invoice = invoice
    @params = params
    @receivable = receivable
  end

  def call
    return unsuccessful('Invoice Is Closed') if invoice.closed?

    if invoice.payable? &&
       params.dig(:payment, :kind) == 'check' &&
       !invoice.invoice_payment_approved?
      return unsuccessful('Unapproved payables cannot be marked paid via check')
    end

    ActiveRecord::Base.transaction do
      approve_if_needed!

      if params.dig(:payment, :kind)&.end_with?('_balance')
        pay_with_balance
      else
        make_payment
      end
    end
  rescue ActiveRecord::RecordInvalid => e
    OpenStruct.new(successful?: false, errors: e.record.errors.full_messages)
  end

  private

  def unsuccessful(errors)
    OpenStruct.new(successful?: false, errors: Array(errors))
  end

  def account
    @account ||= GlobalID::Locator.locate_signed(
      params.dig(:payment, :account_sgid)
    )
  end

  def updated_payment_params
    bank_account_id = account.bank_account.id if account&.bank_account

    key = if receivable
            :credit_bank_account_id
          else
            :debit_bank_account_id
          end

    check_number = if !receivable &&
                      params.dig(:payment, :kind) == 'check' &&
                      params.dig(:payment, :check_number).blank?
                     # Payable check without specified check number
                     account&.bank_account&.next_check_number
                   else
                     params.dig(:payment, :check_number).presence
                   end

    params.tap do |par|
      par[:payment] ||= {}
      par[:payment][:amount] ||= invoice.amount.format
      par[:payment][key] = bank_account_id
      par[:payment][:check_number] = check_number
      par[:mark_paid] = true
    end
  end

  def payment_params
    updated_payment_params
      .require(:payment).permit(Payment::PERMISSABLE_ATTRIBUTES)
  end

  def approve_if_needed!
    return unless invoice.payable?

    return if invoice.invoice_payment_approved?

    invoice.update!(invoice_payment_approved_at: Time.zone.now)
  end

  def make_payment
    payment = Payment.create!(payment_params) do |payment|
      payment.payer = invoice.buyer
      payment.payer_lease_membership = invoice.buyer_lease_membership
      payment.payee = invoice.seller
      payment.payee_lease_membership = invoice.seller_lease_membership
      payment.invoices = [invoice]
      payment.description = 'Payment' if payment.description.blank?
    end

    OpenStruct.new(successful?: true, payment: payment)
  end

  def pay_with_balance
    service = case params.dig(:payment, :kind)
              when 'owner_balance' then Invoice::PayWithOwnerBalance
              when 'deposit_balance' then Invoice::PayWithDepositBalance
              end

    service.call(invoice: invoice) do |payment|
      payment.date = params.dig(:payment, :date)
      payment.amount = params.dig(:payment, :amount)
    end
  end
end
