class Invoice::Forward
  extend Service

  include Invoice::Forward::MarketplaceAccountMapping

  attr_reader :invoice

  def initialize(invoice)
    @invoice = invoice
  end

  def call
    invoice.line_items.group_by(&:forward_id).map do |sgid, items|
      next unless sgid

      buyer = GlobalID::Locator.locate_signed(sgid)

      next unless buyer

      Invoice.create!(
        description: invoice.description,
        post_date: invoice.post_date,
        physical_date: invoice.physical_date,
        due_date: invoice.due_date,
        seller: invoice.buyer,
        buyer: buyer,
        forwarded_from: invoice,
        invoice_payment_approved_at: Time.zone.now
      ) do |forwarded|
        items.each do |item|
          next if item.sourcing_markup&.destination_item.present?

          raw = item.markup.presence || Markupify::Markup.new(0, 'percent')

          markup = if raw.percent?
                     item.create_sourcing_markup!(percentage_markup: raw.value)
                   else
                     item.create_sourcing_markup!(flat_markup: raw.value)
                   end

          forwarded_line_item = forwarded.line_items.build(
            description: item.description,
            quantity: item.quantity,
            unit_price: markup.marked_up_unit_price,
            sourced_markup: markup,
            payable_account: item.payable_account
          )

          forwarded_line_item.receivable_account = receivable_account(forwarded, item,
                                                                      forwarded_line_item)
        end
      end.tap do |forwarded|
        if pay_forwarded_invoice?(forwarded)
          # TODO: Rescue Errors
          Invoice::PayWithOwnerBalance.call(invoice: forwarded)
        end
      end
    end
  end

  private

  def pay_forwarded_invoice?(invoice)
    return false unless invoice.buyer.class.in?([Property, Company])

    case Customer.current_subdomain
    when 'rest-assured', '1827-properties', '1872-sandbox'
      false
    when 'ag-mgmt'
      !invoice.seller.in?([Vendor.find(927), Company.find(610)])
    else
      true
    end
  end

  def receivable_account(forwarded, line_item, forwarded_line_item)
    if CustomerSpecific::Behavior.invoice_forwarding_receivable_account_match?
      # Net to zero by billing for the same expense account
      return line_item.payable_account
    end

    # mapped receivable account should take care of this
    if Customer.current_subdomain == 'marketplacehomes' # rubocop:disable CustomerSpecific/ExplicitSubdomainCheck
      return marketplacehomes_receivable_account(forwarded, line_item)
    end

    Accounting::InverseAccounts.receivable_account(
      line_item: forwarded_line_item,
      fallback: default_maintenance_revenue_account(forwarded)
    )
  end

  def default_maintenance_revenue_account(forwarded)
    forwarded
      .seller
      .accounting_context
      .chart_of_accounts
      .default_maintenance_revenue_account
  end
end
