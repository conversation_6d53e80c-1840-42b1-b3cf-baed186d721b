class Documents::Hierarchy::Folder::ExportJob < ApplicationJob
  queue_as :within_one_hour

  def perform(user:, download_id:, path:, property:)
    DownloadsChannel.download_preparing(
      user:,
      download_id:,
      title: 'Preparing Export',
      subtitle: 'Please wait.'
    )

    result = folder(path:, property:).export

    if result.successful?
      DownloadsChannel.download_ready(
        user:,
        download_id:,
        filename: result.download.filename,
        url: result.download.direct_upload_url
      )
    else
      error = result.errors&.to_sentence || 'Please try again later.'

      DownloadsChannel.download_failed(
        user:,
        download_id:,
        title: 'Unable to Process Export',
        subtitle: error
      )
    end
  end

  private

  def folder(path:, property:)
    root_folder = Documents::Hierarchy::Roots::Property.new(
      property: property
    ).folder

    result = root_folder
             .folder_from_relative_path(path)

    return result unless result.successful?

    result
      .folder
      .load
  end
end
