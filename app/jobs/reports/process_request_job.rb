class Reports::ProcessRequestJob < Reports::GenerateReportJob
  queue_as :reports

  around_perform :use_read_replica, if: :use_read_replica?

  def perform(request_uuid, _report_slug = nil)
    @request = Reports::Request.find(request_uuid)

    return unless @request.pending?

    complete_request
  end

  private

  def complete_request
    html = render_report

    @request.completed!(payload: html)
  rescue Reports::V3::RequestCanceled
    nil # Report was canceled while loading
  rescue StandardError => e
    Honeybadger.notify(e)

    @request.failed!(e)
  ensure
    ReportsChannel.request_updated(request: @request)
  end

  # TODO: Move html rendering
  def render_report
    report = make_report(
      slug: @request.slug,
      params: {
        filters: @request.filters || {}
      },
      user: @request.user,
      request: @request
    )

    partial = 'reports/v3/report'

    locals = {
      report: report.as_html,
      brand: report.brand,
      allow_links: allow_links?
    }

    ApplicationController.render(partial: partial, layout: nil, locals: locals)
  end

  def allow_links?
    @request.user.is_a?(PropertyManager) || (
      @request.user.is_a?(Owner) && @request.slug =~ /owner.statement/
    )
  end

  def use_read_replica?
    Feature.enabled?(:reports_read_replica, Customer.current)
  end

  def use_read_replica(&)
    Replication.use_read_replica(topic: :reports, &)
  end
end
