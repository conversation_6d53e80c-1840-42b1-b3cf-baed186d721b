##
# This is here to be able to enqueue each email schedule job individually in
# the case of failures, but also be able to fetch attachments etc outside the
# mailer but also use deliver_now as the attachments cannot be serialized.
class Reports::RunEmailScheduleJob < ApplicationJob
  queue_as :within_one_hour

  def perform(email_schedule)
    Reports::EmailSchedule::Send.call(email_schedule)
  end
end
