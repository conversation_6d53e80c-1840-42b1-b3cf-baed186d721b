class OwnerContribution::ProcessJob < ApplicationJob
  queue_as :zeamster

  attr_reader :owner_contribution, :owner, :merchant_account, :ip_address

  def perform(owner_contribution:, owner:, merchant_account:, ip_address:)
    @owner_contribution = owner_contribution
    @owner = owner
    @merchant_account = merchant_account
    @ip_address = ip_address

    fail 'Owner contribution not in idle state' unless owner_contribution.idle?

    owner_contribution.processing!

    owner_contribution.allocations.each do |allocation|
      process_allocation(allocation)
    end

    owner_contribution.completed!

    target_users.each do |user|
      user.notifications.create!(
        kind: :owner_contribution,
        resource: owner,
        link: link,
        title: 'An Owner Contribution was Submitted',
        description: "#{owner_contribution.amount.format} from #{owner.name}"
      )
    end
  end

  private

  def link
    Rails.application.routes.url_helpers.owner_url(
      owner, subdomain: Customer.current_subdomain
    )
  end

  def target_users
    Notification::Preferences.where(
      notify_owner_contribution: true
    ).map(&:user).select { |e| (e.properties & related_properties).any? }
  end

  def process_allocation(allocation)
    ActiveRecord::Base.transaction do
      invoice = create_invoice(allocation)
      create_payment(allocation, invoice)
    end
  end

  def description(allocation)
    if owner_contribution.request.present?
      owner_contribution.request&.description
    else
      "Contribution to #{allocation.property.name}"
    end
  end

  def create_invoice(allocation)
    Invoice.create!(
      description: description(allocation),
      date: Time.zone.today,
      due_date: Time.zone.today,
      buyer: allocation.property,
      seller: client_entity,
      invoice_payment_approved_at: Time.zone.now,
      note: owner_contribution.request&.message,
      line_items_attributes: [
        {
          description: 'Contribution',
          unit_price: allocation.amount,
          quantity: 1,
          receivable_account: due_to_customer_account,
          payable_account: due_from_client_entity_account
        }
      ]
    )
  end

  def create_payment(allocation, invoice)
    payment_params = ActionController::Parameters.new(
      payment: {
        kind: owner_contribution.payment_method.electronic_payment_kind,
        amount: allocation.amount.format,
        date: Time.zone.today,
        description: description(allocation)
      }
    )

    convenience_fee = allocation.convenience_fee(merchant_account)

    result = PaymentProcessing::CreateElectronicPayment.new(
      params: payment_params,
      payee: client_entity,
      invoices: [invoice],
      accounting_context: allocation.property.accounting_context,
      ip_address: ip_address,
      user: owner,
      payment_method: owner_contribution.payment_method,
      merchant_account: merchant_account
    ).call do |payment|
      payment.convenience_fee = convenience_fee
    end

    return if result.successful?

    send_failed_slack_notification

    fail result.errors.to_sentence
  end

  def client_entity
    Customer.current.client_entity
  end

  def due_to_customer_account
    client_entity.chart_of_accounts.due_to_customer_account
  end

  def due_from_client_entity_account
    owner_contribution.company.chart_of_accounts.due_from_client_entity_account
  end

  def send_failed_slack_notification
    message = 'Owner contribution failed for ' \
              "#{Customer.current_subdomain} / #{owner.name}"

    send_slack_notification(message: message)
  end

  def send_slack_notification(message:)
    Slack::SendSystemNotificationJob.perform_later(message: message)
  end

  def related_properties
    owner_contribution.allocations.map(&:property).uniq
  end
end
