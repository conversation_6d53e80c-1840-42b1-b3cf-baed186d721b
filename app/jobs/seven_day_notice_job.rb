class SevenDayNoticeJob < ApplicationJob
  include PdfFilling

  queue_as :default

  def perform(user, tenant)
    @user = user
    @tenant = tenant

    generate_pdf(output_file: output_file) do |pdf_file|
      InvoiceAttachment.create!(
        parent: tenant,
        upload: pdf_file
      )

      Contact::TimelineEntry.create!(
        author: user,
        regarding: tenant,
        kind: 'demand_for_possession',
        unit: unit,
        body: 'Seven Day Notice Sent'
      )

      ReportsMailer.seven_day_notice(tenant, pdf_file.read).deliver_now
    end
  end

  private

  attr_reader :tenant, :user

  def pdf_values
    {
      'Community' => property.name,
      'Resident Name' => tenant.name,
      'Address' => property.address.full_street_address,
      'BlgApt' => unit.name,
      'Dear' => tenant.first_name,
      'Property Manager' => user.name,
      'Date' => Time.zone.now.strftime('%B %-e, %Y'),
      'Amount' => tenant.balance.to_f,
      'Amount Due' => due_date.strftime('%B %-e'),
      'Year' => due_date.strftime('%y')
    }
  end

  def pdf_template_path
    Rails.root.join('spec', 'fixtures', 'seven_day_notice.pdf')
  end

  def output_file
    dir = Rails.root.join \
      'tmp', 'pdfs', 'seven_day', SecureRandom.uuid
    FileUtils.mkpath dir
    path = dir.join('Seven Day Notice.pdf')
    File.open(path, 'w+')
  end

  def property
    tenant.current_property
  end

  def unit
    tenant.current_unit
  end

  def due_date
    7.days.from_now
  end
end
