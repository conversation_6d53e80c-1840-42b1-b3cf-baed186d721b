##
# If the vendor has an email,
# and hasn't already submitted an invoice,
# and this assignment is accepting invoice submissions,
# sends an email request to the vendor to submit an invoice.
class Vendors::Assignments::RequestInvoiceJob < ApplicationJob
  queue_as :within_one_hour

  discard_on ActiveRecord::RecordNotFound

  attr_reader :vendor_assignment

  def perform(vendor_assignment:)
    @vendor_assignment = vendor_assignment

    return if vendor_assignment.email.blank? # No vendor email

    vendor_assignment.with_lock do
      next unless ready_to_send?

      VendorAssignmentsMailer
        .invoice_request(vendor_assignment, user: vendor_assignment.created_by)
        .deliver_later

      vendor_assignment.update!(invoice_request_sent_at: Time.zone.now)
    end
  end

  private

  def ready_to_send?
    # Already sent
    return false if vendor_assignment.invoice_request_sent?

    # Already has an invoice
    return false if vendor_assignment.invoices.any?

    # Not accepting invoices
    return false unless vendor_assignment.accepting_invoice_submissions?

    true
  end
end
