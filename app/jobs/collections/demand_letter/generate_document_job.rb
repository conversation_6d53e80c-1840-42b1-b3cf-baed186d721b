class Collections::DemandLetter::GenerateDocumentJob < ApplicationJob
  queue_as :within_one_day

  def perform(demand_letter:)
    lease = demand_letter.lease

    user = demand_letter.created_by

    electronic_delivery = if demand_letter.deliver_electronically?
                            '1'
                          else
                            '0'
                          end

    params = {
      eviction: {
        move_out_date: lease.end_date.to_s,
        lease_expired: false,
        reason: 'Balance',
        electronic_delivery: electronic_delivery
      }
    }

    result = Lease::DemandForPossession.call(
      params: params,
      lease: lease,
      tenant: lease.primary_tenant,
      user: user,
      generate_only: false,
      demand_letter: demand_letter
    )

    fail result.errors.join(' ') unless result.successful?
  end
end
