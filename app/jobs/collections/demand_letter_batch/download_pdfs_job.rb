class Collections::DemandLetterBatch::DownloadPdfsJob < ApplicationJob
  queue_as :within_one_minute

  attr_reader :user, :demand_letter_batch, :scope

  def perform(download_id:, user:, demand_letter_batch:, scope: :all)
    @demand_letter_batch = demand_letter_batch
    @scope = scope
    @user = user

    DownloadsChannel.download_preparing(user: user, download_id: download_id)

    tempfile = make_packet

    download = ShrineDownload.create!(upload: tempfile)

    DownloadsChannel.download_ready(
      user: user,
      download_id: download_id,
      filename: 'demand_letter_batch.pdf',
      url: download.expiring_url
    )
  rescue StandardError => e
    Honeybadger.notify(e)
    DownloadsChannel.download_failed(user: user, download_id: download_id)
  end

  private

  def letters
    case scope.to_s
    when 'manual'
      demand_letter_batch.manual_deliveries
    when 'electronic'
      demand_letter_batch.electronic_deliveries
    else
      demand_letter_batch.demand_letters
    end
  end

  def make_packet
    # even_pages(combine_pdfs(letters.map(&method(:render_pdf))))

    combine_pdfs(letters.map(&method(:render_pdf)))
  end

  def render_pdf(letter)
    lease = letter.lease

    electronic_delivery = if scope == 'electronic'
                            '1'
                          else
                            '0'
                          end

    params = {
      eviction: {
        move_out_date: lease.end_date.to_s,
        lease_expired: false,
        reason: 'Balance',
        electronic_delivery: electronic_delivery
      }
    }

    Rails.logger.info("Generating demand for possession for lease #{lease.id}")

    result = Lease::DemandForPossession.call(
      params: params,
      lease: lease,
      tenant: lease.primary_tenant,
      user: user,
      generate_only: true,
      demand_letter: letter
    )

    fail result.errors.join(' ') unless result.successful?

    result.file
  end

  def combine_pdfs(files)
    paths = files.map(&:path).join(' ')
    file = Tempfile.new(['demand_letter_batch_all', '.pdf'])
    file.open
    command = "pdftk #{paths} cat output #{file.path}"
    system(command)
    files.each { |f| File.delete(f) }
    file
  end

  def even_pages(file)
    even = Tempfile.new(['demand_letter_batch', '.pdf'])
    even.open
    command = "pdftk #{file.path} cat 1-endeven output #{even.path}"
    system(command)
    File.delete(file)
    even
  end
end
