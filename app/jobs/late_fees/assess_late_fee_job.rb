##
# Job to assess a late fee on a {LineItem} from a given late fee {ChargePreset}.
class LateFees::Assess<PERSON>ate<PERSON><PERSON><PERSON><PERSON> < ApplicationJob
  queue_as :within_one_hour

  attr_reader :line_item, :late_fee_preset, :late_invoice, :balance_date

  def perform(line_item, late_fee_preset, date)
    @line_item = line_item
    @late_fee_preset = late_fee_preset
    @late_invoice = line_item.invoice
    @balance_date = date.yesterday

    unit_price = amount

    return unless unit_price.positive?

    Invoice.create!(
      post_date: date,
      due_date: date + late_fee_preset.net_d.days,
      description: late_fee_preset.name,
      buyer: late_invoice.buyer,
      buyer_lease_membership: late_invoice.buyer_lease_membership,
      seller: late_invoice.seller
    ) do |late_fee|
      late_fee.line_items.build(
        charge_preset: late_fee_preset,
        receivable_account: late_fee_preset.account,
        description: late_fee_preset.name,
        unit_price: unit_price,
        quantity: 1
      )
    end
  end

  private

  def amount
    fixed_amount + percentage_of_amount + percentage_of_balance
  end

  def fixed_amount
    @late_fee_preset.amount
  end

  def percentage_of_amount
    return Money.zero unless late_fee_preset.late_fee_amount_ratio

    late_fee_preset.late_fee_amount_ratio * late_invoice.amount
  end

  def percentage_of_balance
    return Money.zero unless late_fee_preset.late_fee_balance_ratio

    late_fee_preset.late_fee_balance_ratio * late_balance
  end

  def late_balance
    if Customer.current_subdomain.start_with?('pmi')
      late_pro_rata_balance
    elsif Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox])
      LeaseMembership::AgingDelinquency.with_effective_date(balance_date) do
        line_item.receivable_balance.balance
      end
    else
      late_invoice.balance
    end
  end

  def late_pro_rata_balance
    total_amount = late_invoice.line_items.select do |line_item|
      line_item.charge_preset&.late_fees&.include?(late_fee_preset)
    end.sum(&:amount)

    balance = total_amount - late_invoice.invoice_payments.sum(&:amount)

    [balance, Money.zero].max
  end
end
