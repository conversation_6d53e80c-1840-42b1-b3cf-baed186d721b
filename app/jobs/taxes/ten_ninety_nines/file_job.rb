class Taxes::TenNinetyNines::File<PERSON>ob < ApplicationJob
  queue_as :within_one_minute

  def perform(task:, user:, batch_group:, eula_consent:)
    result = Taxes::File.call(batch_group.id, user, eula_consent)

    if result.successful?
      count = batch_group.nelco_submissions.count

      route = Rails.application.routes.url_helpers.taxes_batch_group_path(
        batch_group
      )

      task.completed! do |toast|
        toast.title = 'Submitted Successfully'
        toast.subtitle = "#{count} #{'1099'.pluralize(count)}"
        toast.redirect = route
      end
    else
      task.failed! do |toast|
        toast.title = 'Review Failed'
        toast.subtitle = result.errors.to_sentence
      end
    end
  end
end
