ActiveAdmin.register MerchantAccount do
  permit_params :name, :bank_account_id, :priority,
                :payment_processor,
                :developer_id, :user_id, :user_api_key,
                :location_id, :product_transaction_id,
                :ach_debit_enabled,
                :ach_debit_markup_cents,
                :ach_debit_rate,
                :credit_card_enabled,
                :credit_card_markup_cents,
                :credit_card_rate,
                :ach_credit_enabled,
                :ach_credit_markup_cents,
                :ach_credit_rate,
                :check_scanning_enabled,
                :check_scanning_markup_cents,
                :check_scanning_rate

  form do |f|
    f.inputs do
      f.input :name
      f.input :bank_account,
              label: 'Bank Account',
              collection: BankAccountsQuery.new.search.customer_managed

      f.input :priority, placeholder: 'Lowest first, leave at 0 generally'

      f.input :payment_processor, label: 'Payment Processor'

      f.input :developer_id, label: 'Developer ID'
      f.input :user_id, label: 'User ID'
      f.input :user_api_key, label: 'User API Key'
      f.input :location_id, label: 'Location ID'
      f.input :product_transaction_id, label: 'Product Transaction ID'

      f.input :ach_debit_enabled, label: 'Enable ACH Debit'
      f.input :ach_debit_markup_cents, label: 'ACH Debit Markup Cents'
      f.input :ach_debit_rate, label: 'ACH Debit Rate'

      f.input :credit_card_enabled, label: 'Enable Credit Card'
      f.input :credit_card_markup_cents, label: 'Credit Card Markup Cents'
      f.input :credit_card_rate, label: 'Credit Card Rate'

      f.input :ach_credit_enabled, label: 'Enable ACH Credit'
      f.input :ach_credit_markup_cents, label: 'ACH Credit Markup Cents'
      f.input :ach_credit_rate, label: 'ACH Credit Rate'

      f.input :check_scanning_enabled, label: 'Enable Check Scanning'
      f.input :check_scanning_markup_cents, label: 'Check Scan Markup Cents'
      f.input :check_scanning_rate, label: 'Check Scanning Rate'
    end
    f.actions
  end

  action_item :refresh_transactions, only: :show, if: -> { resource.like_zeamster? } do
    link_to 'Refresh Transactions',
            prepare_refresh_transactions_admin_merchant_account_path(resource)
  end

  member_action :prepare_refresh_transactions, method: :get do
    render 'admin/merchant_accounts/refresh_transactions'
  end

  member_action :refresh_transactions, method: :post do
    start_date = params[:start_date]
    end_date = params[:end_date]

    from_time = Time.zone.parse(start_date).beginning_of_day
    to_time = Time.zone.parse(end_date).end_of_day

    if from_time.before?(90.days.ago)
      flash[:error] = 'Cannot refresh transactions older than 90 days.'
    elsif to_time.after?(Time.zone.now)
      flash[:error] = 'Cannot refresh transactions in the future.'
    elsif from_time.after?(to_time)
      flash[:error] = 'Start date must be before end date.'
    end

    unless resource.like_zeamster?
      flash[:error] = 'Only Zeamster merchant accounts can be refreshed.'
    end

    if flash[:error].present?
      return redirect_to prepare_refresh_transactions_admin_merchant_account_path(resource)
    end

    Zeamster::RefreshMerchantAccountJob.perform_later(
      merchant_account: resource,
      from_time: from_time,
      to_time: to_time
    )

    flash[:notice] = 'Transactions for this merchant account will be refreshed shortly.'
    redirect_to admin_merchant_account_path(resource)
  end
end
