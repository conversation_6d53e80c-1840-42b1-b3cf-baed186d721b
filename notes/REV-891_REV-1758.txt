[x] phone formatting handling (breaks on back/reload?)
[x] what does membership_agreement.completed mean?
[x] Invalidate Signatures
!disable forms for nonvisitable?
!error passing from submit
!idempotence on member onboarding
!separate wizard membership agreement signing step from the handling of the agreement 
. signing timestamp

Customer.activate! :staging
onboarding = MemberOnboarding::Configuration.find(110)
property = onboarding.member_assignments.first.property
asam = FactoryBot.create(:agreements_simple_agreement_membership, property: property), simple_agreement: FactoryBot.create(:simple_agreement, agreement_type: Agreements::AgreementType.first))
member.update(confirmed_at: Time.zone.now)


asam = FactoryBot.create(:agreements_simple_agreement_membership, property: Property.find(1), simple_agreement: FactoryBot.create(:simple_agreement, agreement_type: Agreements::AgreementType.first))
member = asam.tenant
lm = FactoryBot.create(:lease_membership, property: Property.find(1), tenant: member)
member.update(confirmed_at: Time.zone.now)

findable_guarantor = FactoryBot.create(:tenant, confirmed_at: Time.zone.now)

Rails.application.routes.url_helpers.polymorphic_url(member, subdomain: 'alever')
Rails.application.routes.url_helpers.masquerade_url(sgid: tenant.to_sgid.to_s)

member_id = 10
findable_guarantor_id = "<EMAIL>"
existing_guarantor_lm = FactoryBot.create(:lease_membership, lease: lm.lease, role: :guarantor)
existing_guarantor_asam = FactoryBot.create(:agreements_simple_agreement_membership, simple_agreement: asam.simple_agreement, role: :guardian)
existing_guarantor = existing_guarantor_lm.tenant
existing_guarantor.update(confirmed_at: Time.zone.now)
existing_guarantor_email = '<EMAIL>'

their_little_buddy_asam = FactoryBot.create(:agreements_simple_agreement_membership, property: Property.last)
their_little_buddy = their_little_buddy_asam.tenant

"branch: remove-guarantor-lookup"
"branch: presplit-pretest-remove-guarantor-lookup"
"branch: but-still-has-it-remove-guarantor-lookup"
"branch: piles-remove-guarantor-lookup"
"branch: REV-1759-member-onboarding-refactor-auto-navigation"
"branch: REV-891-remove-guarantor-lookup"
"branch: REV-1759-891-combined"
"branch: REV-1758-REV-1763-incremental-updates-and-review-page"
"branch: prebase/REV-1758-incremental-refactor"
result = begin
	onboarding = FactoryBot.build(:member_onboarding_configuration, :information_collection, :guarantor, :membership_agreement, :lease_agreement, :charge, :risk_release, portfolio: Property.last.portfolio)
	onboarding.save
	moa = FactoryBot.create(:member_onboarding_assignment, configuration: onboarding)
rescue => e
	print e.awesome_inspect
	e
end


onboarding = FactoryBot.build(:member_onboarding_configuration, :information_collection, :membership_agreement, :lease_agreement, :charge, :risk_release,
	guarantor: FactoryBot.build(:member_onboarding_guarantor, collections_information: true),
	portfolio: Property.last.portfolio, num_property_memberships: 0)
onboarding.save
moa = FactoryBot.create(:member_onboarding_assignment, configuration: onboarding)
print "http://alever.lvh.me:3000/manage/tenants/#{moa.tenant.id}"


# onboarding = FactoryBot.build(:member_onboarding_configuration, :information_collection, :membership_agreement, :lease_agreement, :charge, :risk_release,
# 	guarantor: FactoryBot.build(:member_onboarding_guarantor, collections_information: true),
# 	portfolio: Property.last.portfolio)
# onboarding.save


moa = FactoryBot.create(:member_onboarding_assignment, configuration: onboarding)
moa.tenant.update(last_name: 'HasExistingGuarantor')
asam = FactoryBot.create(:agreements_simple_agreement_membership, property: Property.last, simple_agreement: FactoryBot.create(:simple_agreement, agreement_type: Agreements::AgreementType.first))
gsam = FactoryBot.create(:agreements_simple_agreement_membership, simple_agreement: asam.simple_agreement, role: :guardian)
gsam.tenant.update(taxpayer_identification_attributes: { tin_type: :ssn, tin: '*********' }, last_name: 'ExistingGuarantor')
print "http://alever.lvh.me:3000/manage/tenants/#{moa.tenant.id}"





<<-TODO
	review cards
	consolidate electronic signature
	reintroduce keep-params
	finish guarantor
TODO

<<~STRING
	Validate Steps on Submit
	Store Membership Agreement Signature in Wizard
	Use Signature stored in Wizard when Submitting
	Introduce the Submit Step
	Submit in Submit Step - Refactor Now that we don't need those params
	Cleanup Refactor - Steps dont need to try and submit anymore
	Cleanup Refactor - Steps dont need #requires_submission_before_visit? or #submit_after_completed? anymore
	Invalidate Signature when there are changes on the form
	Factory Changes
STRING


REV-1758 REV-1763
git branch -m REV-1758-REV-1763-incremental-updates-and-review-page
docker compose up -d
docker-compose exec -e "RAILS_ENV=test" web bundle exec rspec spec/system/tenants/member_onboarding_spec.rb

bundle exec rspec ./spec/system/tenants/member_onboarding_spec.rb:213
bundle exec rspec ./spec/system/tenants/member_onboarding_spec.rb:243
bundle exec rspec ./spec/system/tenants/member_onboarding_spec.rb:305
bundle exec rspec ./spec/system/tenants/member_onboarding_spec.rb:334
bundle exec rspec ./spec/system/tenants/member_onboarding_spec.rb:213 && bundle exec rspec ./spec/system/tenants/member_onboarding_spec.rb:243 && bundle exec rspec ./spec/system/tenants/member_onboarding_spec.rb:305 && bundle exec rspec ./spec/system/tenants/member_onboarding_spec.rb:334


rspec ./spec/system/tenants/member_onboarding_spec.rb:267 # Configurable Member Onboarding onboarding steps guarantor profile handling existing guardian shows the guarantor profile page
rspec ./spec/system/tenants/member_onboarding_spec.rb:301 # Configurable Member Onboarding onboarding steps guarantor profile handling existing guardian preventing assigning people who are invalid valid guarantors when looking up is expected to be present
rspec ./spec/system/tenants/member_onboarding_spec.rb:302 # Configurable Member Onboarding onboarding steps guarantor profile handling existing guardian preventing assigning people who are invalid valid guarantors when looking up is expected to be present
rspec ./spec/system/tenants/member_onboarding_spec.rb:303 # Configurable Member Onboarding onboarding steps guarantor profile handling existing guardian preventing assigning people who are invalid valid guarantors when looking up is expected to be present
rspec ./spec/system/tenants/member_onboarding_spec.rb:310 # Configurable Member Onboarding onboarding steps guarantor profile handling existing guardian preventing assigning people who are invalid valid guarantors when creating without lookup prevents them from proceeding
rspec ./spec/system/tenants/member_onboarding_spec.rb:339 # Configurable Member Onboarding onboarding steps guarantor profile handling skip guarantor when guarantor is skippable expects guarantor profile details until the member opts to skip
rspec ./spec/system/tenants/member_onboarding_spec.rb:358 # Configurable Member Onboarding onboarding steps guarantor profile handling skip guarantor when guarantor is not skippable does not give the option of skipping the guarantor
