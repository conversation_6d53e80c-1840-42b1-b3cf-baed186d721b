Introduce PropertyVendor model, migration, and associations

Existing Property-Vendor associations:
    via utilities, via vendor contracts
    these may be company-based or even portfolio based as well
    we'll need to insert these when created
    we'll also need to backfill when destroyed

Stash : "REV-1354 - where propert_vendors only has the property_id column"

Prompt Workspace:
    So I talked to the folks who wanted this change, and they need something slightly different.
    They need to be able to associate either a `Portfolio` or a `Property` (but not both) with a Vendor.

    I think we should:
    - rollback that migration we just added
    - update that migration to add adding a portfolio_id column (and whatever index is appropriate for this)
    - add a database constraint or index or whatever that ensures that portfolio_id or property_id will have a value, but not both
    - rename the table to 
    - rename the model and the associations
