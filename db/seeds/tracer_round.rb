module TracerRound
  @@traces = {}

  def save!(*args, &block)
    klass = self.class.name
    line = caller.grep(/demo/).first
    (@@traces[klass] ||= []) << line
    super
  end

  def self.traces
    @@traces.map do |k, arr|
      counts = arr.inject(Hash.new(0)) { |a, e| a[e] += 1; a }
      [k, counts]
    end.to_h
  end

  def self.trace!
    ApplicationRecord.send(:include, TracerRound)
  end
end
