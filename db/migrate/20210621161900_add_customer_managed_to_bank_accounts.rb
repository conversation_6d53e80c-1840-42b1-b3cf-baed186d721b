class AddCustomerManagedToBankAccounts < ActiveRecord::Migration[6.1]
  def change
    # This should be false for:
    #   Tenant payment methods
    #   Owner Entity payment methods / deposit accounts
    # This should be true for:
    #   Customer owned entity bank accounts
    #   Trust accounts
    #   Customer managed owner entity accounts
    add_column :bank_accounts, :customer_managed, :boolean, null: false, default: false

    reversible do |direction|
      direction.up do
        # This logic mirrors the previous implementation of
        # BankAccountsQuery::Scopes#customer_accessible

        # Bank accounts owned by customer owned companies
        companies = Company.customer_owned
        BankAccount.where(owner: companies).update_all(customer_managed: true)

        # Bank accounts used as trust accounts
        BankAccount.joins(:portfolios_bank_accounts).update_all(customer_managed: true)

        # Specific customers who are managing all bank accounts
        if Apartment::Tenant.current.in?(%w[ccp greenoak])
          BankAccount.where(owner_type: 'Company').update_all(customer_managed: true)
        end
      end
    end
  end
end
