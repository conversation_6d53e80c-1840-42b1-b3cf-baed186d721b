class RemoveLegacyApprovedFromInvoices < ActiveRecord::Migration[5.2]
  def up
    Invoice.update_all(
      'invoice_payment_approved_at = approved_at'
    )

    remove_column :invoices, :approved_at, :datetime
    remove_reference :invoices, :approved_by
  end

  def down
    add_column :invoices, :approved_at, :datetime
    add_reference :invoices, :approved_by,
                  foreign_key: { to_table: :property_managers }

    Invoice.update_all(
      'approved_at = invoice_payment_approved_at'
    )
  end
end
