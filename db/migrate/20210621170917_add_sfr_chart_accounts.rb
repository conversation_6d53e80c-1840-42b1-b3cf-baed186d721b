class AddSfrChartAccounts < ActiveRecord::Migration[6.1]
  def change
    add_reference :charts_of_accounts, :property_transfers_account,
      index: false, foreign_key: { to_table: :plutus_accounts }
    add_reference :charts_of_accounts, :due_from_client_entity_account,
      index: false, foreign_key: { to_table: :plutus_accounts }
    add_reference :charts_of_accounts, :due_to_customer_account,
      index: false, foreign_key: { to_table: :plutus_accounts }
    add_reference :charts_of_accounts, :owner_cash_account,
      index: false, foreign_key: { to_table: :plutus_accounts }
  end
end
