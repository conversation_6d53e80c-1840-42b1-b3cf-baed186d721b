class ChangeVendorListsToSellerGlobalIds < ActiveRecord::Migration[5.2]
  def up
    add_column :approvals_rules, :seller_whitelist, :string, array: true
    add_column :approvals_rules, :seller_blacklist, :string, array: true

    Approvals::Rule.where.not(vendor_whitelist_ids: nil).find_each do |rule|
      vendors = Vendor.where(id: rule.vendor_whitelist_ids)
      rule.update(seller_whitelist: vendors)
    end

    Approvals::Rule.where.not(vendor_blacklist_ids: nil).find_each do |rule|
      vendors = Vendor.where(id: rule.vendor_blacklist_ids)
      rule.update(seller_blacklist: vendors)
    end

    remove_column :approvals_rules, :vendor_whitelist_ids
    remove_column :approvals_rules, :vendor_blacklist_ids
  end

  def down
    add_column :approvals_rules, :vendor_whitelist_ids, :bigint, array: true
    add_column :approvals_rules, :vendor_blacklist_ids, :bigint, array: true

    Approvals::Rule.where.not(seller_whitelist: nil).find_each do |rule|
      vendors = rule.seller_whitelist.select { |v| v.is_a?(Vendor) }
      rule.update(vendor_whitelist_ids: vendors.map(&:id))
    end

    Approvals::Rule.where.not(seller_blacklist: nil).find_each do |rule|
      vendors = rule.seller_blacklist.select { |v| v.is_a?(Vendor) }
      rule.update(vendor_blacklist_ids: vendors.map(&:id))
    end

    remove_column :approvals_rules, :seller_whitelist
    remove_column :approvals_rules, :seller_blacklist
  end
end
