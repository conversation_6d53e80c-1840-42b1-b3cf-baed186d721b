class AddMoveOutToItemizedDamages < ActiveRecord::Migration[5.2]
  def change
    add_reference :itemized_damages, :move_out, foreign_key: { to_table: :lease_move_outs }

    reversible do |direction|
      direction.up do
        # This has to exist at this point for Lease to load
        create_table :charge_schedules do |t|
          t.references :chargeable, polymorphic: true
          t.timestamps
        end

        ItemizedDamages.find_each do |damages|
          lease = damages.lease
          move_out = lease.move_outs.processed.order(processed_at: :asc).first
          move_out ||= lease.move_outs.last

          # For some reason there are ILDs but no move outs on some shamrock leases.
          puts "Unable to find move out for lease #{lease.id}" unless move_out
          move_out ||= Lease::MoveOut.create!(
            lease: lease,
            walk_through_date: damages.walk_through_date,
            lease_memberships: [lease.lease_memberships.first]
          )

          damages.update!(move_out: move_out)
        end

        # Added back later
        drop_table :charge_schedules
      end
    end

    change_column_null :itemized_damages, :move_out_id, false
  end
end
