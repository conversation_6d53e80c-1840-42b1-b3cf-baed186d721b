class AddConfirmableToDevise < ActiveRecord::Migration[4.2]
  def up
    add_column :property_managers, :confirmation_token, :string
    add_column :property_managers, :confirmed_at, :datetime
    add_column :property_managers, :confirmation_sent_at, :datetime
     add_column :property_managers, :unconfirmed_email, :string
    add_index :property_managers, :confirmation_token, unique: true
    # User.reset_column_information # Need for some types of updates, but not for update_all.
    # To avoid a short time window between running the migration and updating all existing
    # property_managers as confirmed, do the following
    execute("UPDATE property_managers SET confirmed_at = NOW()")
    # All existing user accounts should be able to log in after this.
    # Remind: Rails using SQLite as default. And SQLite has no such function :NOW.
    # Use :date('now') instead of :NOW when using SQLite.
    # => execute("UPDATE property_managers SET confirmed_at = date('now')")
    # Or => User.all.update_all confirmed_at: Time.now
  end

  def down
    remove_columns :property_managers, :confirmation_token, :confirmed_at, :confirmation_sent_at
    remove_columns :property_managers, :unconfirmed_email
  end
end
