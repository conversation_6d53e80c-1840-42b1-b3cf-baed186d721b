class ConvertTenantMessagesToNotes < ActiveRecord::Migration[6.1]
  def change
    Message.where(chat_room_type: 'Tenant').find_each do |message|
      tenant = message.chat_room

      if tenant.present?
        user = message.author
        body = message.body

        tenant.timeline_entries.note.create!(
          created_at: message.created_at,
          body: body,
          author: user,
          regarding: tenant
        )
      end

      message.destroy!
    end
  end
end
