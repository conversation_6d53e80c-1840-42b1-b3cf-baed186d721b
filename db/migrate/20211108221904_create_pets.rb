class CreatePets < ActiveRecord::Migration[6.1]
  def change
    create_table :pets do |t|
      t.integer :kind, null: false
      t.string :breed
      t.string :color
      t.integer :weight, null: false
      t.string :kind_detail
      t.integer :age, null: false
      t.string :name, null: false
      t.boolean :service_animal, default: false, null: false
      t.references :lease_application, foreign_key: true
      t.references :lease, foreign_key: true

      t.timestamps
    end
  end
end
