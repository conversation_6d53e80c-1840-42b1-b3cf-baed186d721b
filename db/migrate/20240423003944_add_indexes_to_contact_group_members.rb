class AddIndexesToContactGroupMembers < ActiveRecord::Migration[7.0]
  disable_ddl_transaction!

  def change
    add_index(
      :contact_group_members,
      [:first_name, :last_name, :phone, :email],
      name: 'index_contact_group_members_on_name_phone_email',
      unique: true,
      algorithm: :concurrently
    )
    add_index(:contact_group_members, :phone, algorithm: :concurrently)
    add_index(:contact_group_members, :email, algorithm: :concurrently)
  end
end
