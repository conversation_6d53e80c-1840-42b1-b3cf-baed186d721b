class CreateElectronicSignatures < ActiveRecord::Migration[5.2]
  def change
    create_table :electronic_signatures do |t|
      t.string :uuid, null: false, index: { unique: true }
      t.references :document, null: false, foreign_key: { to_table: :leases }
      t.references :requested_by, polymorphic: true, index: { name: 'index_electronic_signatures_on_requested_by' }
      t.references :recipient, polymorphic: true, index: { name: 'index_electronic_signatures_on_recipient' }
      t.boolean :countersigner, null: false, default: false
      t.string :email, null: false
      t.datetime :expires_at, null: false
      t.datetime :signed_at
      t.inet :ip_address
      t.string :full_name
      t.text :signature

      t.timestamps
    end
  end
end
