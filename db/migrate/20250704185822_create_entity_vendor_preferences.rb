class CreateEntityVendorPreferences < ActiveRecord::Migration[7.0]
  def change
    create_table :entity_vendor_preferences do |t|
      t.references :entity, null: false, polymorphic: true, index: false
      t.references :vendor, null: false, foreign_key: true

      t.timestamps
    end

    add_index :entity_vendor_preferences, [:entity_id, :entity_type, :vendor_id], unique: true, name: 'index_entity_vendor_preferences_on_entity_and_vendor'
  end
end
