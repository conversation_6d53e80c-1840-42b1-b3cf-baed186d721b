class CreateMemberOnboardingRiskReleases < ActiveRecord::Migration[7.0]
  def change
    create_table :member_onboarding_risk_releases do |t|
      t.references :configuration,
                   null: false,
                   foreign_key: { to_table: :member_onboarding_configurations }
      t.references :account, foreign_key: { to_table: :plutus_accounts }, null: false
      t.integer :monthly_cost_cents, null: false
      t.date :coverage_start_date, null: false
      t.boolean :enrollment_required, null: false
      t.integer :billing_frequency, null: false

      t.timestamps
    end
  end
end
