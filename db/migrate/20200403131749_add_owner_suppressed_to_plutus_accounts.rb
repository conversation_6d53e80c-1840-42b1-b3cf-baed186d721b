class AddOwnerSuppressedToPlutusAccounts < ActiveRecord::Migration[5.2]
  def change
    add_column :plutus_accounts, :owner_suppressed, :boolean, null: false, default: false
    drop_view :plutus_cash_amounts, materialized: true
    update_view :plutus_cash_accounts, version: 1, revert_to_version: 1, materialized: true
    create_view :plutus_cash_amounts, version: 3, materialized: true
    add_index :plutus_cash_amounts, %i[account_id entry_id]
    add_index :plutus_cash_amounts, %i[entry_id account_id]
    add_index :plutus_cash_amounts, :type
    add_index :plutus_cash_amounts, :account_id
    add_index :plutus_cash_amounts, :entry_id
  end
end
