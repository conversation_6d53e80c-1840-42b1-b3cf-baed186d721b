class AddVendorInsuranceProofRequiredToConfigurations < ActiveRecord::Migration[7.0]
  def change
    add_column :configurations,
               :vendor_insurance_proof_required,
               :boolean,
               null: false,
               default: true

    reversible do |direction|
      direction.up do
        Configuration.update_all(vendor_insurance_proof_required: false)
      end
    end
  end
end
