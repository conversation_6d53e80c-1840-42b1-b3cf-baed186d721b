class AddServiceAreaToMaintenanceEstimates < ActiveRecord::Migration[5.2]
  def change
    add_reference :maintenance_estimates, :property, foreign_key: true
    add_reference :maintenance_estimates, :unit, foreign_key: true

    reversible do |direction|
      direction.up do
        Maintenance::Estimate.find_each do |estimate|
          service_area = if estimate.inspection
                           estimate.inspection.property
                         elsif estimate.maintenance_ticket
                           estimate.maintenance_ticket.service_area
                         end

          property, unit = if service_area.is_a?(Property)
                             [service_area, nil]
                           elsif service_area.is_a?(Unit)
                             [service_area.property, service_area]
                           end

          estimate.update!(
            property: property,
            unit: unit
          )
        end
      end
    end

    change_column_null :maintenance_estimates, :property_id, false
  end
end
