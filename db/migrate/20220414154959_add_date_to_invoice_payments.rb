class AddDateToInvoicePayments < ActiveRecord::Migration[6.1]
  def change
    add_column :invoice_payments, :date, :date

    reversible do |direction|
      direction.up do
        update('
          UPDATE invoice_payments
          SET date = GREATEST(payments.date, invoices.post_date)
          FROM payments, invoices
          WHERE payments.id = invoice_payments.payment_id
          AND invoices.id = invoice_payments.invoice_id
        ')
      end
    end

    change_column_null :invoice_payments, :date, false
  end
end
