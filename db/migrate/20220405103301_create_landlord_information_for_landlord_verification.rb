class CreateLandlordInformationForLandlordVerification < ActiveRecord::Migration[6.1]
  def change
    create_table :landlord_verification_landlord_informations do |t|
      t.string :landlord_name, null: false
      t.string :landlord_email, null: false
      t.string :landlord_phone, null: false
      t.references :landlord_verification, index: { name: "index_landlord_informations_on_landlord_verification_id"}
      t.timestamps
    end
  end
end
