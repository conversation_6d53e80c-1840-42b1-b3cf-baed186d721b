class AddEnhancedAndAutoAssignmentColumnsToMemberOnboardingPropertyMembership < ActiveRecord::Migration[7.0]
  disable_ddl_transaction!

  def change
    add_column :member_onboarding_property_memberships,
               :enhanced,
               :boolean,
               default: false,
               null: false

    add_column :member_onboarding_property_memberships,
               :auto_assign,
               :boolean,
               default: false,
               null: false

    add_index :member_onboarding_property_memberships, :property_id,
              unique: true,
              where: 'auto_assign = TRUE',
              name: 'index_unique_onboarding_property_memberships_on_property',
              algorithm: :concurrently
  end
end
