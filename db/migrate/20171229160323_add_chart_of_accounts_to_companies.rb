class AddChartOfAccountsToCompanies < ActiveRecord::Migration[5.1]
  def up
    add_reference :companies, :chart_of_accounts, foreign_key: true

    ChartOfAccounts.find_each do |chart|
      next unless chart.company_id

      Company.find(
        chart.company_id
      ).update!(chart_of_accounts_id: chart.id)
    end

    change_column_null :companies, :chart_of_accounts_id, false
    remove_column :charts_of_accounts, :company_id
  end

  def down
    add_column :charts_of_accounts, :company_id, :integer

    Company.find_each do |company|
      ChartOfAccounts.find(
        company.chart_of_accounts_id
      ).update!(company_id: company.id)
    end

    remove_reference :companies, :chart_of_accounts, foreign_key: true
  end
end
