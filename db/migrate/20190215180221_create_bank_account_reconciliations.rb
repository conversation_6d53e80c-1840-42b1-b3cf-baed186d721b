class CreateBankAccountReconciliations < ActiveRecord::Migration[5.2]
  def change
    create_table :bank_account_reconciliations do |t|
      t.references :bank_account, foreign_key: true, null: false
      t.date :statement_date, null: false
      t.monetize :statement_beginning_balance, currency: { present: false }, amount: { null: false, default: nil }
      t.monetize :statement_ending_balance, currency: { present: false }, amount: { null: false, default: nil }
      t.monetize :uncleared_deposits, currency: { present: false }
      t.monetize :uncleared_payments, currency: { present: false }
      t.monetize :register_ending_balance, currency: { present: false }
      t.references :submitted_by, foreign_key: { to_table: :property_managers }, index: false
      t.datetime :submitted_at
      t.timestamps
    end
  end
end
