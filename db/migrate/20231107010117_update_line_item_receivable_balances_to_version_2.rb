class UpdateLineItemReceivableBalancesToVersion2 < ActiveRecord::Migration[7.0]
  def change
    drop_view :accounting_persisted_ledgers, revert_to_version: 7, materialized: true
    drop_view :lease_chain_overdue_balance_breakdowns, revert_to_version: 2
    update_view :line_item_receivable_balances, version: 2, revert_to_version: 1
    create_view :lease_chain_overdue_balance_breakdowns, version: 2
    create_view :accounting_persisted_ledgers, version: 7, materialized: true
  end
end
