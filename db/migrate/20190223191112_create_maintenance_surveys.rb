class CreateMaintenanceSurveys < ActiveRecord::Migration[5.2]
  def change
    create_table :maintenance_surveys do |t|
      t.integer :experience_rating
      t.integer :quality_rating
      t.text :feedback
      t.references :maintenance_ticket, foreign_key: true, null: false
      t.references :tenant, foreign_key: true, null: false
      t.datetime :submitted_at

      t.timestamps
    end
  end
end
