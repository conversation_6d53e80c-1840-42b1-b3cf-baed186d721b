class CreateLeaseNoticeOfNonRenewals < ActiveRecord::Migration[5.2]
  def change
    create_table :lease_notice_of_non_renewals do |t|
      t.references :lease, foreign_key: true, null: false
      t.references :submitted_by, polymorphic: true, null: false, index: { name: 'index_lease_notice_of_non_renewals_on_submitted_by' }
      t.date :date, null: false
      t.date :anticipated_move_out_date, null: false

      t.timestamps
    end
  end
end
