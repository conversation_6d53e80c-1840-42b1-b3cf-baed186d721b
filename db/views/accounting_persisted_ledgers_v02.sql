WITH most_recent_demand_letters AS (
  SELECT
    lease_chains.id AS lease_chain_id,
    MAX(collections_demand_letters.id) AS id
  FROM
    collections_demand_letters
  LEFT JOIN
    lease_chains
      ON collections_demand_letters.lease_id = ANY(lease_chains.lease_ids)
  GROUP BY
    lease_chain_id
),

most_recent_evictions AS (
  SELECT
    lease_chains.id AS lease_chain_id,
    MAX(collections_evictions.id) AS id
  FROM
    collections_evictions
  LEFT JOIN
    lease_chains
      ON collections_evictions.lease_id = ANY(lease_chains.lease_ids)
  GROUP BY
    lease_chain_id
)

SELECT
  lease_chains.id AS id,
  lease_chains.id AS ledgerable_id,
  'Lease::Chain' AS ledgerable_type,
  lease_chains.start_date AS start_date,
  lease_chains.end_date AS end_date,
  properties.id AS property_id,
  units.id AS unit_id,
  lease_tails.id AS lease_id,
  primary_lease_memberships.tenant_id AS primary_tenant_id,
  delinquency.balance_cents AS current_balance_cents,
  delinquency.overdue_cents AS overdue_balance_cents,
  demand_letters.created_at AS demand_letter_last_sent_at,
  demand_letters.overdue_balance_cents AS demand_letter_balance_cents,
  evictions.created_at AS eviction_started_at,
  evictions.overdue_balance_cents AS eviction_balance_cents
FROM
  lease_chains
LEFT JOIN
  leases
    AS lease_tails
    ON lease_tails.id = lease_chains.lease_ids[array_upper(lease_ids, 1)]
LEFT JOIN
  lease_memberships
    AS primary_lease_memberships
    ON (
      primary_lease_memberships.lease_id = lease_tails.id
      AND
      primary_lease_memberships.role = 0
    )
LEFT JOIN
  units
    ON units.id = lease_tails.unit_id
LEFT JOIN
  properties
    ON properties.id = units.property_id

LEFT JOIN
  lease_chain_aging_delinquencies
    AS delinquency
    ON delinquency.lease_chain_id = lease_chains.id

LEFT JOIN
  most_recent_demand_letters
    ON most_recent_demand_letters.lease_chain_id = lease_chains.id
LEFT JOIN
  collections_demand_letters
    AS demand_letters
    ON demand_letters.id = most_recent_demand_letters.id

LEFT JOIN
  most_recent_evictions
    ON most_recent_evictions.lease_chain_id = lease_chains.id
LEFT JOIN
  collections_evictions
    AS evictions
    ON evictions.id = most_recent_evictions.id
;
