WITH accounts_receivable_ids AS (
  SELECT
    charts_of_accounts.id AS chart_of_accounts_id,
    (
      CASE
        WHEN charts_of_accounts.accounts_receivable_id IS NULL
          THEN (
            SELECT
              plutus_accounts.id
            FROM
              plutus_accounts
            WHERE
              plutus_accounts.name = 'Accounts Receivable'
          )
        ELSE
          charts_of_accounts.accounts_receivable_id
        END
    ) AS accounts_receivable_id
  FROM
    charts_of_accounts
  GROUP BY
    charts_of_accounts.id
)

SELECT
  plutus_entries.*,
  (
    CASE
      WHEN plutus_amounts.type = 'Plutus::DebitAmount' THEN plutus_amounts.amount
      ELSE -plutus_amounts.amount
    END
  ) AS amount_cents
FROM
  plutus_amounts
LEFT JOIN
  plutus_accounts
  ON
    plutus_accounts.id = plutus_amounts.account_id
LEFT JOIN
  plutus_entries
  ON
    plutus_entries.id = plutus_amounts.entry_id
WHERE
  plutus_amounts.account_id IN (
    SELECT accounts_receivable_id FROM accounts_receivable_ids
  )
;
