WITH assigned_tasks AS (
  SELECT
    assignments.user_id,
    ('/operations/projects/' || tasks.project_id || '/tasks/' || tasks.id)::varchar AS path,
    'task'::varchar AS type,
    tasks.name AS title,
    tasks.description,
    tasks.created_at,
    tasks.due_date::timestamptz as due_at,
    null::timestamptz as scheduled_at
  FROM
    assignments
  JOIN
    tasks
  ON
    assignments.assignable_type = 'Task'
    AND assignments.assignable_id = tasks.id
    AND tasks.completed_at IS NULL
),

assigned_work_orders AS (
  SELECT
    assignments.user_id,
    ('/maintenance/tickets/' || maintenance_tickets.id)::varchar AS path,
    'work_order'::varchar AS type,
    maintenance_tickets.subject AS title,
    maintenance_tickets.description,
    maintenance_tickets.created_at,
    null::timestamptz as due_at,
    null::timestamptz as scheduled_at
  FROM
    assignments
  JOIN
    maintenance_tickets
  ON
    assignments.assignable_type = 'MaintenanceTicket'
    AND assignments.assignable_id = maintenance_tickets.id
    AND maintenance_tickets.closed_at IS NULL
),

scheduled_tours AS (
  SELECT
    tours.tour_guide_id AS user_id,
    ('/leasing/guest_cards/' || tours.guest_card_id)::varchar AS path,
    'tour'::varchar AS type,
    ('Tour of ' || properties.name) AS title,
    ('With ' || tenants.first_name || ' ' || tenants.last_name) AS description,
    tours.created_at,
    null::timestamptz as due_at,
    tours.time AS scheduled_at
  FROM
    tours
  JOIN
    guest_cards ON guest_cards.id = tours.guest_card_id
  JOIN
    tenants ON tenants.id = guest_cards.tenant_id
  LEFT JOIN
    properties ON properties.id = guest_cards.property_id
  WHERE
    tours.time > NOW()
)

SELECT * FROM assigned_tasks
UNION ALL
SELECT * FROM assigned_work_orders
UNION ALL
SELECT * FROM scheduled_tours
;
