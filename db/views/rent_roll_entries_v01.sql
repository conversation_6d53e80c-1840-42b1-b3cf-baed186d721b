WITH membership_amounts AS (
  SELECT
    lease_membership_id,
    sum(amount_cents) AS amount_cents
  FROM
    charge_amounts
  GROUP BY
    charge_amounts.lease_membership_id
)
SELECT
  lease_memberships.id as lease_membership_id,
  leases.unit_id AS unit_id,
  tenants.id AS tenant_id,
  tenants.first_name || ' ' || tenants.last_name AS tenant_name,
  leases.start_date,
  leases.end_date,
  membership_amounts.amount_cents,
  lease_membership_balances.amount_cents AS balance_cents
FROM
  lease_memberships
LEFT JOIN
  leases
    ON
      lease_memberships.lease_id = leases.id
LEFT JOIN
  tenants
    ON
      lease_memberships.tenant_id = tenants.id
LEFT JOIN
  lease_membership_balances
    ON
      lease_membership_balances.lease_membership_id = lease_memberships.id
LEFT JOIN
  membership_amounts
    ON
      membership_amounts.lease_membership_id = lease_memberships.id
;
