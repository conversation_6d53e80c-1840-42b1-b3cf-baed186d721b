WITH bare_amounts AS (
  SELECT
    plutus_amounts.id AS id,
    plutus_entries.journal_id AS journal_id,
    plutus_amounts.reconciliation_id AS reconciliation_id,
    plutus_accounts.id AS account_id,
    plutus_entries.id AS entry_id,
    (
      CASE plutus_entries.commercial_document_type
      WHEN 'Payment' THEN plutus_entries.commercial_document_id
      ELSE NULL::INTEGER
      END
    ) AS payment_id,
    NULL::INTEGER AS transaction_batch_id,
    plutus_entries.date AS date,
    plutus_entries.description AS description,
    plutus_entries.property_id AS property_id,
    plutus_entries.contact_name AS contact_name,
    plutus_entries.reference_number AS reference_number,
    plutus_entries.reference_text AS reference_text,
    (
      CASE plutus_amounts.type
      WHEN 'Plutus::DebitAmount' THEN plutus_amounts.amount
      ELSE -plutus_amounts.amount
      END
    ) AS amount_cents
  FROM
    plutus_amounts
  JOIN
    plutus_entries
      ON
        plutus_amounts.entry_id = plutus_entries.id
  JOIN
    plutus_accounts
      ON
        plutus_accounts.id = plutus_amounts.account_id
  WHERE
    deposit_batch_id IS NULL
      AND
        plutus_accounts.id IN (SELECT ledger_account_id FROM bank_accounts)
  AND
    plutus_entries.basis <> 2
),

transaction_batches AS (
  SELECT
    deposit_batches.id AS id,
    bank_accounts.owner_id AS journal_id,
    deposit_batches.reconciliation_id AS reconciliation_id,
    bank_accounts.ledger_account_id AS account_id,
    NULL::INTEGER AS entry_id,
    NULL::INTEGER AS payment_id,
    deposit_batches.id AS transaction_batch_id,
    deposit_batches.expected_date AS date,
    deposit_batches.name AS description,
    NULL::INTEGER AS property_id,
    NULL AS contact_name,
    NULL AS reference_number,
    (
      CASE deposit_batches.expected_count
      WHEN 1 THEN '1 Item'
      ELSE deposit_batches.expected_count || ' Items'
      END
    )
    AS reference_text,
    SUM (
      CASE plutus_amounts.type
      WHEN 'Plutus::DebitAmount' THEN plutus_amounts.amount
      ELSE -plutus_amounts.amount
      END
    ) AS amount_cents
  FROM
    deposit_batches
  JOIN
    bank_accounts
      ON
        bank_accounts.id = deposit_batches.bank_account_id
  JOIN
    plutus_amounts
      ON
        plutus_amounts.deposit_batch_id = deposit_batches.id
  WHERE
    deposit_batches.posted_at IS NOT NULL
  GROUP BY
    deposit_batches.id, bank_accounts.owner_id, bank_accounts.ledger_account_id
)

SELECT * FROM bare_amounts
UNION ALL
SELECT * FROM transaction_batches
;
