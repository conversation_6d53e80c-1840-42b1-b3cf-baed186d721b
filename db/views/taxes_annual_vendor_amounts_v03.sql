SELECT
  -- Owned entity
  companies.id AS company_id,

  -- Vendor
  payments.payee_id AS vendor_id,

  -- Accounting year
  CAST(EXTRACT(YEAR FROM payments.date) AS INT) AS year,

  -- Total paid to vendor
  SUM(payments.amount_cents) AS amount_cents

FROM
  payments

-- Optionally join to properties for owned property payments
LEFT JOIN
  properties
    ON
      payments.payer_type = 'Property' AND properties.id = payments.payer_id

JOIN
  companies
    ON (
      -- A payment from a property owned by an owned entity
      companies.id = properties.company_id
    ) OR (
      -- A payment made directly by an owned entity
      payments.payer_type = 'Company' AND companies.id = payments.payer_id
    )

-- Join to vendors, to ensure payments are to vendors of eligible type
JOIN
  vendors
    ON
      payments.payee_type = 'Vendor' AND vendors.id = payments.payee_id

WHERE
  -- Only payments to certain vendor types (exclude 'scorp' and 'ccorp').
  -- See: Taxpayer::INELIGIBLE_FOR_1099_TYPES.
  (
    vendors.business_type NOT IN (1, 2)
      OR
    vendors.business_type IS NULL
  )

    AND

  -- Omit suppliers, municipals, utilities
  vendors.kind NOT IN (1, 3, 4)

    AND

  -- Only payments from owned entities or their properties
  companies.customer_managed = false

    AND

  -- Omit reversed payments
  payments.reversed_at IS NULL

GROUP BY
  companies.id, vendor_id, year

;
