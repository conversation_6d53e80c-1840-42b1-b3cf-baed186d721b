WITH

text_messages AS (
  SELECT
    recipient_id AS tenant_id,
    created_at::date AS contacted_at
  FROM
    telephony_text_messages
  WHERE
    recipient_type = 'Tenant'
),

email_receipt_dates AS (
  SELECT
    email_receipts.created_at AS contacted_at,
    UNNEST(email_receipts.to || email_receipts.cc || email_receipts.bcc) AS email
  FROM
    email_receipts
),

tenant_email_receipts AS (
  SELECT
    tenants.id AS tenant_id,
    email_receipt_dates.contacted_at AS contacted_at
  FROM
    email_receipt_dates
  JOIN
    tenants
  ON
    tenants.email = email_receipt_dates.email
),

contact_times AS (
  SELECT
    tenant_id,
    MIN(contacted_at) AS first_contacted_at,
    MAX(contacted_at) AS last_contacted_at
  FROM
    (
      SELECT * FROM text_messages
      UNION ALL
      SELECT * FROM tenant_email_receipts
    ) AS last_contacts
  GROUP BY
    tenant_id
),

-- Most recent guest card per prospect
most_recent_guest_cards AS (
  SELECT
    DISTINCT ON (tenant_id) tenant_id,
    guest_cards.created_at::date AS guest_card_date,
    guest_cards.updated_at AS updated_at,
    guest_cards.property_id AS property_id,
    sources.source AS source,
    'Submitted Guest Card' AS status
  FROM guest_cards
  LEFT JOIN sources ON sources.id = guest_cards.external_source_id
  ORDER BY tenant_id, guest_cards.created_at DESC
),

-- Most recent tour per prospect
most_recent_tours AS (
  SELECT
    DISTINCT ON (lead_id) lead_id AS tenant_id,
    tours.time::date AS tour_date,
    tours.property_id AS property_id,
    tours.updated_at AS updated_at,
    sources.source AS source,
    'Toured' AS status
  FROM tours
  LEFT JOIN sources ON sources.id = tours.source_id
  ORDER BY tenant_id, tours.time DESC
),

-- Most recent application per prospect
most_recent_applications AS (
  SELECT
    DISTINCT ON (tenant_id) tenant_id,
    lease_application_memberships.created_at::date AS application_date,
    lease_applications.property_id AS property_id,
    lease_applications.updated_at AS updated_at,
    (
      CASE
      WHEN lease_applications.rejected_at IS NOT NULL THEN 'Application Rejected'
      WHEN lease_applications.approved_at IS NOT NULL THEN 'Application Approved'
      ELSE 'Application Submitted'
      END
    ) AS status,
    lease_application_memberships.primary AS primary
  FROM lease_application_memberships
  JOIN lease_applications ON lease_applications.id = lease_application_memberships.lease_application_id
  ORDER BY tenant_id, lease_application_memberships.created_at DESC
),

-- Most recent lease per prospect
most_recent_leases AS (
  SELECT
    DISTINCT ON (tenant_id) tenant_id,
    lease_memberships.created_at::date AS lease_date,
    lease_memberships.updated_at AS updated_at,
    units.property_id AS property_id,
    (
      CASE
      WHEN renewed.id IS NOT NULL THEN 'Renewed'
      ELSE 'Leased'
      END
    ) AS status,
    (
      CASE
      WHEN lease_memberships.role = 0 THEN true
      ELSE false
      END
    ) AS primary
  FROM lease_memberships
  JOIN leases ON leases.id = lease_memberships.lease_id AND leases.archived_at IS NULL
  LEFT JOIN leases AS renewed On renewed.renewal_id = leases.id
  JOIN units ON units.id = leases.unit_id
  ORDER BY tenant_id, lease_memberships.created_at DESC
),

guest_card_counts AS (
  SELECT
    tenant_id,
    COUNT(*) AS guest_card_count
  FROM guest_cards
  GROUP BY tenant_id
),

tour_counts AS (
  SELECT
    lead_id AS tenant_id,
    COUNT(*) AS tour_count
  FROM tours
  GROUP BY tenant_id
),

application_counts AS (
  SELECT
    tenant_id,
    COUNT(*) AS application_count
  FROM lease_application_memberships
  GROUP BY tenant_id
),

lease_counts AS (
  SELECT
    tenant_id,
    COUNT(*) AS lease_count
  FROM lease_memberships
  GROUP BY tenant_id
)

SELECT
  tenants.id AS id,
  tenants.id AS tenant_id,
  -- Prefer most recent property as property_id
  COALESCE (
    most_recent_leases.property_id,
    most_recent_applications.property_id,
    most_recent_tours.property_id,
    most_recent_guest_cards.property_id
  ) AS property_id,
  -- Prefer most recent status
  COALESCE (
    most_recent_leases.status,
    most_recent_applications.status,
    most_recent_tours.status,
    most_recent_guest_cards.status
  ) AS status,
  tenants.first_name,
  tenants.last_name,
  tenants.email,
  tenants.phone,
  COALESCE(most_recent_guest_cards.source, most_recent_tours.source) AS source,
  COALESCE(guest_card_counts.guest_card_count, 0) AS guest_card_count,
  most_recent_guest_cards.guest_card_date AS last_guest_card,
  COALESCE(tour_counts.tour_count, 0) AS tour_count,
  most_recent_tours.tour_date AS last_tour,
  COALESCE(application_counts.application_count, 0) AS application_count,
  most_recent_applications.application_date AS last_application,
  COALESCE(lease_counts.lease_count, 0) AS lease_count,
  most_recent_leases.lease_date AS last_lease,
  contact_times.first_contacted_at AS first_contacted_at,
  contact_times.last_contacted_at AS last_contacted_at,
  tenants.created_at AS created_at,
  GREATEST (
    tenants.updated_at::date,
    most_recent_guest_cards.updated_at,
    most_recent_tours.updated_at,
    most_recent_applications.updated_at,
    most_recent_leases.updated_at,
    last_contacted_at
  ) AS updated_at
FROM tenants

LEFT JOIN guest_card_counts ON guest_card_counts.tenant_id = tenants.id
LEFT JOIN most_recent_guest_cards ON most_recent_guest_cards.tenant_id = tenants.id

LEFT JOIN tour_counts ON tour_counts.tenant_id = tenants.id
LEFT JOIN most_recent_tours ON most_recent_tours.tenant_id = tenants.id

LEFT JOIN application_counts ON application_counts.tenant_id = tenants.id
LEFT JOIN most_recent_applications ON most_recent_applications.tenant_id = tenants.id
LEFT JOIN most_recent_applications
  AS most_recent_primary_applications
  ON most_recent_primary_applications.tenant_id = tenants.id
  AND most_recent_primary_applications.primary = true

LEFT JOIN lease_counts ON lease_counts.tenant_id = tenants.id
LEFT JOIN most_recent_leases ON most_recent_leases.tenant_id = tenants.id
LEFT JOIN most_recent_leases
  AS most_recent_primary_leases
  ON most_recent_primary_leases.tenant_id = tenants.id
  AND most_recent_primary_leases.primary = true

LEFT JOIN contact_times ON contact_times.tenant_id = tenants.id

-- Must have at least a guest card, tour, or lease application
WHERE (
  COALESCE (
    most_recent_guest_cards.guest_card_date,
    most_recent_tours.tour_date,
    most_recent_primary_applications.application_date
  ) IS NOT NULL
)
;
